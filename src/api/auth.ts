import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

console.log('API_BASE_URL:', API_BASE_URL) // 调试信息

// 创建axios实例
const authApi = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
})

// 请求拦截器
authApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器（简化版，主要用于其他可能使用authApi的地方）
authApi.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
      // 不自动跳转，让组件处理
    }
    return Promise.reject(error)
  },
)

// 认证相关接口
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
}

export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  full_name: string
  is_active?: boolean
  user_source: 'enterprise' | 'individual' | 'school' | 'hospital' | 'government'
}

export interface User {
  id: number
  email: string
  username: string
  full_name: string
  is_active: boolean
  user_source: string
  created_at: string
  updated_at: string
}

// 登录
export const login = async (data: LoginRequest): Promise<LoginResponse> => {
  console.log('登录请求数据:', data) // 调试信息
  console.log('请求URL:', `${API_BASE_URL}/auth/login`) // 调试信息

  try {
    // 使用正确的接口路径
    const response = await axios.post<ApiResponse<LoginResponse>>(
      `${API_BASE_URL}/auth/login`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )
    console.log('登录响应:', response) // 调试信息

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '登录失败')
    }
  } catch (error: any) {
    console.error('登录请求失败:', error) // 调试信息
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 注册
export const register = async (data: RegisterRequest): Promise<User> => {
  try {
    const response = await axios.post<ApiResponse<User>>(`${API_BASE_URL}/users/`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '注册失败')
    }
  } catch (error: any) {
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 获取当前用户信息
export const getCurrentUser = async (): Promise<User> => {
  try {
    const token = localStorage.getItem('access_token')
    const response = await axios.get<ApiResponse<User>>(`${API_BASE_URL}/users/me`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取用户信息失败')
    }
  } catch (error: any) {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
      throw new Error('登录已过期，请重新登录')
    }
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 权限对象接口
interface PermissionObject {
  id: number
  name: string
  display_name: string
  description: string
  resource: string
  action: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// 获取当前用户权限
export const getCurrentUserPermissions = async (): Promise<string[]> => {
  try {
    const token = localStorage.getItem('access_token')
    const response = await axios.get<ApiResponse<PermissionObject[]>>(
      `${API_BASE_URL}/permissions/me`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    )

    if (response.data.success) {
      // 将权限对象数组转换为权限名称字符串数组
      return response.data.data.map((permission) => permission.name)
    } else {
      throw new Error(response.data.message || '获取权限信息失败')
    }
  } catch (error: any) {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
      throw new Error('登录已过期，请重新登录')
    }
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    // 权限获取失败时返回空数组，不阻断登录流程
    console.warn('获取权限失败:', error)
    return []
  }
}

export default authApi
