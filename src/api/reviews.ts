import type {
  ApiResponse,
  DatasetListResponse,
  DatasetReview,
  DatasetType,
  ReviewRequest,
  ReviewStats,
  ReviewStatus,
} from '@/types'
import { http } from './request'

// 数据集审核API
export const reviewApi = {
  // 获取待审核数据集列表
  getPendingReviews(params: {
    page?: number
    size?: number
    dataset_type?: DatasetType
    creator_id?: number
    search?: string
  }): Promise<ApiResponse<DatasetListResponse>> {
    return http.get('/dataset-reviews/pending', { params })
  },

  // 获取审核历史
  getReviewHistory(params: {
    page?: number
    size?: number
    dataset_id?: number
    reviewer_id?: number
    review_status?: ReviewStatus
    dataset_type?: DatasetType
    start_date?: string
    end_date?: string
  }): Promise<
    ApiResponse<{
      items: DatasetReview[]
      total: number
      page: number
      size: number
      pages: number
    }>
  > {
    return http.get('/dataset-reviews/history', { params })
  },

  // 获取我的审核记录
  getMyReviews(params: {
    page?: number
    size?: number
    dataset_id?: number
    review_status?: ReviewStatus
    dataset_type?: DatasetType
    start_date?: string
    end_date?: string
  }): Promise<
    ApiResponse<{
      items: DatasetReview[]
      total: number
      page: number
      size: number
      pages: number
    }>
  > {
    return http.get('/dataset-reviews/my', { params })
  },

  // 获取审核详情
  getReviewDetail(reviewId: number): Promise<ApiResponse<DatasetReview>> {
    return http.get(`/dataset-reviews/${reviewId}`)
  },

  // 审核数据集
  reviewDataset(datasetId: number, reviewData: ReviewRequest): Promise<ApiResponse<void>> {
    return http.post(`/dataset-reviews/${datasetId}/review`, reviewData)
  },

  // 检查审核权限
  checkReviewPermission(datasetId: number): Promise<ApiResponse<{ can_review: boolean }>> {
    return http.get(`/dataset-reviews/check/${datasetId}`)
  },

  // 获取审核统计
  getReviewStats(): Promise<ApiResponse<ReviewStats>> {
    return http.get('/dataset-reviews/stats')
  },
}
