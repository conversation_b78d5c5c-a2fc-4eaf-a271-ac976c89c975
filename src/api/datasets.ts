import type {
  ApiResponse,
  Dataset,
  DatasetCreateRequest,
  DatasetListResponse,
  DatasetStats,
  DatasetStatus,
  DatasetType,
  DatasetUpdateRequest,
} from '@/types'
import { http } from './request'

// 数据集管理API
export const datasetApi = {
  // 获取数据集列表
  getDatasets(params: {
    page?: number
    size?: number
    dataset_type?: DatasetType
    dataset_status?: DatasetStatus
    creator_id?: number
    search?: string
    tags?: string
  }): Promise<ApiResponse<DatasetListResponse>> {
    return http.get('/datasets', { params })
  },

  // 获取我的数据集
  getMyDatasets(params: {
    page?: number
    size?: number
    dataset_type?: DatasetType
    dataset_status?: DatasetStatus
    search?: string
    tags?: string
  }): Promise<ApiResponse<DatasetListResponse>> {
    return http.get('/datasets/my', { params })
  },

  // 获取数据集详情
  getDatasetDetail(id: number, includeDetails = false): Promise<ApiResponse<Dataset>> {
    return http.get(`/datasets/${id}`, {
      params: { include_details: includeDetails },
    })
  },

  // 创建数据集
  createDataset(data: DatasetCreateRequest): Promise<ApiResponse<Dataset>> {
    return http.post('/datasets', data)
  },

  // 更新数据集
  updateDataset(id: number, data: DatasetUpdateRequest): Promise<ApiResponse<Dataset>> {
    return http.put(`/datasets/${id}`, data)
  },

  // 删除数据集
  deleteDataset(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/datasets/${id}`)
  },

  // 提交审核
  submitForReview(id: number, comment?: string): Promise<ApiResponse<void>> {
    return http.post(`/datasets/${id}/submit-review`, {
      submit_comment: comment,
    })
  },

  // 获取数据集统计
  getDatasetStats(): Promise<ApiResponse<DatasetStats>> {
    return http.get('/datasets/stats')
  },
}
