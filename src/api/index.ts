import type {
  EvaluationResult,
  EvaluationTask,
  ListResponse,
  LoginForm,
  Model,
  PaginationParams,
  RegisterForm,
  User,
} from '@/types'
import { http } from './request'

// 用户相关 API
export const userApi = {
  // 登录
  login(data: LoginForm) {
    return http.post<{ token: string; user: User }>('/auth/login', data)
  },

  // 注册
  register(data: RegisterForm) {
    return http.post<{ user: User }>('/auth/register', data)
  },

  // 获取用户信息
  getUserInfo() {
    return http.get<User>('/user/info')
  },

  // 更新用户信息
  updateUserInfo(data: Partial<User>) {
    return http.put<User>('/user/info', data)
  },

  // 修改密码
  changePassword(data: { oldPassword: string; newPassword: string }) {
    return http.post('/user/change-password', data)
  },

  // 登出
  logout() {
    return http.post('/auth/logout')
  },
}

// 模型相关 API
export const modelApi = {
  // 获取模型列表
  getModels(params?: PaginationParams & { search?: string; type?: string }) {
    return http.get<ListResponse<Model>>('/models', { params })
  },

  // 获取模型详情
  getModel(id: string) {
    return http.get<Model>(`/models/${id}`)
  },

  // 创建模型
  createModel(data: Omit<Model, 'id' | 'createdAt' | 'updatedAt'>) {
    return http.post<Model>('/models', data)
  },

  // 更新模型
  updateModel(id: string, data: Partial<Model>) {
    return http.put<Model>(`/models/${id}`, data)
  },

  // 删除模型
  deleteModel(id: string) {
    return http.delete(`/models/${id}`)
  },
}

// 评测任务相关 API
export const evaluationApi = {
  // 获取评测任务列表
  getTasks(params?: PaginationParams & { status?: string; modelId?: string }) {
    return http.get<ListResponse<EvaluationTask>>('/evaluations', { params })
  },

  // 获取评测任务详情
  getTask(id: string) {
    return http.get<EvaluationTask>(`/evaluations/${id}`)
  },

  // 创建评测任务
  createTask(data: Omit<EvaluationTask, 'id' | 'status' | 'progress' | 'createdAt' | 'updatedAt'>) {
    return http.post<EvaluationTask>('/evaluations', data)
  },

  // 启动评测任务
  startTask(id: string) {
    return http.post(`/evaluations/${id}/start`)
  },

  // 停止评测任务
  stopTask(id: string) {
    return http.post(`/evaluations/${id}/stop`)
  },

  // 删除评测任务
  deleteTask(id: string) {
    return http.delete(`/evaluations/${id}`)
  },

  // 获取评测结果
  getResults(taskId: string) {
    return http.get<EvaluationResult[]>(`/evaluations/${taskId}/results`)
  },
}

// 统计相关 API
export const statisticsApi = {
  // 获取仪表板数据
  getDashboard() {
    return http.get<{
      totalModels: number
      totalTasks: number
      runningTasks: number
      completedTasks: number
      recentTasks: EvaluationTask[]
      performanceChart: any
    }>('/statistics/dashboard')
  },

  // 获取模型性能对比
  getModelComparison(modelIds: string[]) {
    return http.post<any>('/statistics/model-comparison', { modelIds })
  },

  // 获取评测趋势
  getEvaluationTrend(params: { startDate: string; endDate: string }) {
    return http.get<any>('/statistics/evaluation-trend', { params })
  },
}

// 导出新的API服务
export * from './dataDict'
export * from './datasets'
export * from './fileUpload'
export * from './permissions'
export * from './reviews'
