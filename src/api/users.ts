import axios from 'axios'
import type { ApiResponse } from './auth'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

export interface User {
  id: number
  email?: string
  username: string
  phone: string
  full_name: string
  is_active: boolean
  user_source: number
  created_at: string
  updated_at: string
}

export interface CreateUserRequest {
  email?: string
  username: string
  phone: string
  password?: string
  full_name: string
  is_active?: boolean
  user_source: 1 | 2 | 3 | 4 | 5
}

export interface UpdateUserRequest {
  email?: string
  username?: string
  phone: string
  password?: string
  full_name?: string
  is_active?: boolean
  user_source?: 1 | 2 | 3 | 4 | 5
}

export interface UserListParams {
  skip?: number
  limit?: number
}

// 获取认证头
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token')
  return {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
  }
}

// 获取用户列表
export const getUserList = async (params?: UserListParams): Promise<User[]> => {
  try {
    const response = await axios.get<ApiResponse<User[]>>(`${API_BASE_URL}/users/`, {
      params,
      headers: getAuthHeaders(),
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取用户列表失败')
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error)
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 获取指定用户信息
export const getUser = async (userId: number): Promise<User> => {
  try {
    const response = await axios.get<ApiResponse<User>>(`${API_BASE_URL}/users/${userId}`, {
      headers: getAuthHeaders(),
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取用户信息失败')
    }
  } catch (error: any) {
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 创建用户
export const createUser = async (data: CreateUserRequest): Promise<User> => {
  try {
    const response = await axios.post<ApiResponse<User>>(`${API_BASE_URL}/users/`, data, {
      headers: getAuthHeaders(),
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '创建用户失败')
    }
  } catch (error: any) {
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 更新用户信息
export const updateUser = async (userId: number, data: UpdateUserRequest): Promise<User> => {
  try {
    const response = await axios.put<ApiResponse<User>>(`${API_BASE_URL}/users/${userId}`, data, {
      headers: getAuthHeaders(),
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '更新用户失败')
    }
  } catch (error: any) {
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 删除用户
export const deleteUser = async (userId: number): Promise<void> => {
  try {
    const response = await axios.delete<ApiResponse<null>>(`${API_BASE_URL}/users/${userId}`, {
      headers: getAuthHeaders(),
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '删除用户失败')
    }
  } catch (error: any) {
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message)
    }
    throw error
  }
}

// 用户来源选项
export const userSourceOptions = [
  { value: 1, label: '企业' },
  { value: 2, label: '个人' },
  { value: 3, label: '学校' },
  { value: 4, label: '医院' },
  { value: 5, label: '政府机构' },
]
