import type { ApiResponse } from '@/types'
import { http } from './request'

// 文件上传响应数据类型
export interface UploadFileResponse {
  file_id: string
  original_filename: string
  cos_key: string
  url: string
  size: number
  content_type: string
  category: string
  is_public: boolean
  uploaded_by: number
  uploaded_at: string
}

// 批量上传响应数据类型
export interface BatchUploadResponse {
  uploaded_files: UploadFileResponse[]
  success_count: number
  error_count: number
  errors: string[]
}

// 文件信息类型
export interface FileInfo {
  key: string
  size: number
  last_modified: string
  etag: string
  content_type: string
  url: string
}

// 下载链接响应类型
export interface DownloadUrlResponse {
  download_url: string
  expires_in: number
  file_info: FileInfo
}

// 文件列表响应类型
export interface FileListResponse {
  files: FileInfo[]
  count: number
  is_truncated: boolean
  search_prefix: string
}

// 文件上传API
export const fileUploadApi = {
  // 上传单个文件
  uploadFile(
    file: File,
    options: {
      category?: string
      is_public?: boolean
      custom_path?: string
      random_rename?: boolean
    } = {},
  ): Promise<ApiResponse<UploadFileResponse>> {
    const formData = new FormData()
    formData.append('file', file)

    if (options.category) {
      formData.append('category', options.category)
    }

    if (options.is_public !== undefined) {
      formData.append('is_public', String(options.is_public))
    }

    if (options.custom_path) {
      formData.append('custom_path', options.custom_path)
    }

    if (options.random_rename !== undefined) {
      formData.append('random_rename', String(options.random_rename))
    }

    return http.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 上传图片（专用接口）
  uploadImage(
    file: File,
    options: {
      is_public?: boolean
      custom_path?: string
      random_rename?: boolean
    } = {},
  ): Promise<ApiResponse<UploadFileResponse>> {
    const formData = new FormData()
    formData.append('file', file)

    if (options.is_public !== undefined) {
      formData.append('is_public', String(options.is_public))
    }

    if (options.custom_path) {
      formData.append('custom_path', options.custom_path)
    }

    if (options.random_rename !== undefined) {
      formData.append('random_rename', String(options.random_rename))
    }

    return http.post('/files/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 批量上传文件
  uploadMultiple(
    files: File[],
    options: {
      category?: string
      is_public?: boolean
      custom_path?: string
      random_rename?: boolean
    } = {},
  ): Promise<ApiResponse<BatchUploadResponse>> {
    const formData = new FormData()

    files.forEach((file) => {
      formData.append('files', file)
    })

    if (options.category) {
      formData.append('category', options.category)
    }

    if (options.is_public !== undefined) {
      formData.append('is_public', String(options.is_public))
    }

    if (options.custom_path) {
      formData.append('custom_path', options.custom_path)
    }

    if (options.random_rename !== undefined) {
      formData.append('random_rename', String(options.random_rename))
    }

    return http.post('/files/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 获取下载链接
  getDownloadUrl(cosKey: string, expires = 3600): Promise<ApiResponse<DownloadUrlResponse>> {
    return http.get(`/files/download/${encodeURIComponent(cosKey)}`, {
      params: { expires },
    })
  },

  // 获取文件信息
  getFileInfo(cosKey: string): Promise<ApiResponse<FileInfo>> {
    return http.get(`/files/info/${encodeURIComponent(cosKey)}`)
  },

  // 删除文件
  deleteFile(cosKey: string): Promise<ApiResponse<{ cos_key: string }>> {
    return http.delete(`/files/delete/${encodeURIComponent(cosKey)}`)
  },

  // 获取文件列表
  listFiles(
    options: {
      category?: string
      prefix?: string
      max_keys?: number
      only_mine?: boolean
    } = {},
  ): Promise<ApiResponse<FileListResponse>> {
    return http.get('/files/list', { params: options })
  },
}

// 数据集类型对应的上传路径配置
export const DATASET_UPLOAD_PATHS = {
  comprehensive_case: 'vet_eval/clinical_case_datasets_imgs',
  medical_image: 'vet_eval/medical_image_datasets_imgs',
  exam_question: 'vet_eval/exam_question_datasets_imgs',
  // 可以根据需要添加更多数据集类型
} as const

export type DatasetType = keyof typeof DATASET_UPLOAD_PATHS

// 便捷方法：根据数据集类型上传图片
export const uploadDatasetImage = (
  file: File,
  datasetType: DatasetType,
): Promise<ApiResponse<UploadFileResponse>> => {
  return fileUploadApi.uploadImage(file, {
    is_public: true,
    custom_path: DATASET_UPLOAD_PATHS[datasetType],
    random_rename: true,
  })
}

// 便捷方法：批量上传数据集图片
export const uploadDatasetImages = (
  files: File[],
  datasetType: DatasetType,
): Promise<ApiResponse<BatchUploadResponse>> => {
  return fileUploadApi.uploadMultiple(files, {
    category: 'images',
    is_public: true,
    custom_path: DATASET_UPLOAD_PATHS[datasetType],
    random_rename: true,
  })
}

// 向后兼容：保留原有的临床病例上传方法
export const uploadClinicalCaseImage = (file: File): Promise<ApiResponse<UploadFileResponse>> => {
  return uploadDatasetImage(file, 'comprehensive_case')
}

// 向后兼容：保留原有的批量上传方法
export const uploadClinicalCaseImages = (
  files: File[],
): Promise<ApiResponse<BatchUploadResponse>> => {
  return uploadDatasetImages(files, 'comprehensive_case')
}
