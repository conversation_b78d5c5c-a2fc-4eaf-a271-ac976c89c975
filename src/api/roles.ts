import authApi from './auth'

export interface Role {
  id: number
  name: string
  display_name: string
  description: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Permission {
  id: number
  name: string
  display_name: string
  description: string
  resource: string
  action: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface RoleWithPermissions extends Role {
  permissions: Permission[]
}

export interface CreateRoleRequest {
  name: string
  display_name: string
  description: string
  is_active?: boolean
}

export interface UpdateRoleRequest {
  name?: string
  display_name?: string
  description?: string
  is_active?: boolean
}

export interface RoleListParams {
  skip?: number
  limit?: number
  is_active?: boolean
}

export interface AssignRoleToUserRequest {
  user_id: number
  role_ids: number[]
}

// 获取角色列表
export const getRoleList = async (params?: RoleListParams): Promise<Role[]> => {
  const response = await authApi.get('/roles/', { params })
  return response.data.data || []
}

// 获取角色详情
export const getRole = async (roleId: number): Promise<Role> => {
  const response = await authApi.get(`/roles/${roleId}`)
  return response.data.data
}

// 获取角色及其权限
export const getRoleWithPermissions = async (roleId: number): Promise<RoleWithPermissions> => {
  const response = await authApi.get(`/roles/${roleId}/with-permissions`)
  return response.data.data
}

// 创建角色
export const createRole = async (data: CreateRoleRequest): Promise<Role> => {
  const response = await authApi.post('/roles/', data)
  return response.data.data
}

// 更新角色
export const updateRole = async (roleId: number, data: UpdateRoleRequest): Promise<Role> => {
  const response = await authApi.put(`/roles/${roleId}`, data)
  return response.data.data
}

// 删除角色
export const deleteRole = async (roleId: number): Promise<void> => {
  await authApi.delete(`/roles/${roleId}`)
}

// 为用户分配角色
export const assignRoleToUser = async (data: AssignRoleToUserRequest): Promise<void> => {
  await authApi.post('/roles/assign-to-user', data)
}

// 从用户移除角色
export const removeRoleFromUser = async (userId: number, roleId: number): Promise<void> => {
  await authApi.delete(`/roles/remove-from-user/${userId}/${roleId}`)
}

// 获取用户角色列表
export const getUserRoles = async (userId: number): Promise<Role[]> => {
  const response = await authApi.get(`/roles/user/${userId}`)
  return response.data.data || []
}

// 获取角色用户列表
export const getRoleUsers = async (roleId: number): Promise<any[]> => {
  const response = await authApi.get(`/roles/${roleId}/users`)
  return response.data.data || []
}

// 为角色分配权限
export const assignPermissionsToRole = async (
  roleId: number,
  permissionIds: number[],
): Promise<void> => {
  await authApi.post(`/roles/${roleId}/assign-permissions`, permissionIds)
}

// 从角色移除权限
export const removePermissionFromRole = async (
  roleId: number,
  permissionId: number,
): Promise<void> => {
  await authApi.delete(`/roles/${roleId}/remove-permission/${permissionId}`)
}

// 获取角色权限列表
export const getRolePermissions = async (roleId: number): Promise<Permission[]> => {
  const response = await authApi.get(`/roles/${roleId}/permissions`)
  return response.data.data || []
}
