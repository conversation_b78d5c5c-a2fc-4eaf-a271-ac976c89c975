import type {
  ApiResponse,
  CreateCategoryRequest,
  CreateDictRequest,
  DataDict,
  DataDictItem,
  DataDictOption,
  DictCategory,
  DictOption,
  DictTreeNode,
  UpdateCategoryRequest,
  UpdateDictRequest,
} from '@/types'
import { http } from './request'

// 数据字典分类管理API
export const dataDictCategoryApi = {
  // 获取所有分类
  getCategories(): Promise<ApiResponse<DictCategory[]>> {
    return http.get('/data-dict/categories')
  },

  // 获取单个分类
  getCategory(categoryCode: string): Promise<ApiResponse<DictCategory>> {
    return http.get(`/data-dict/categories/${categoryCode}`)
  },

  // 创建分类
  createCategory(data: CreateCategoryRequest): Promise<ApiResponse<DictCategory>> {
    return http.post('/data-dict/categories', data)
  },

  // 更新分类
  updateCategory(
    categoryCode: string,
    data: UpdateCategoryRequest,
  ): Promise<ApiResponse<DictCategory>> {
    return http.put(`/data-dict/categories/${categoryCode}`, data)
  },

  // 删除分类（注意：根据需求，前端不允许删除分类）
  deleteCategory(categoryCode: string): Promise<ApiResponse<void>> {
    return http.delete(`/data-dict/categories/${categoryCode}`)
  },
}

// 数据字典数据管理API
export const dataDictDataApi = {
  // 获取指定分类的字典数据
  getDictList(
    category: string,
    params?: {
      parent_id?: number
      page?: number
      size?: number
    },
  ): Promise<ApiResponse<DataDict[]>> {
    return http.get(`/data-dict/${category}`, { params })
  },

  // 获取字典选项（下拉选择用）
  getDictOptions(category: string, parentId?: number): Promise<ApiResponse<DictOption[]>> {
    const params = parentId !== undefined ? { parent_id: parentId } : {}
    return http.get(`/data-dict/${category}/options`, { params })
  },

  // 获取字典树形结构
  getDictTree(category: string): Promise<ApiResponse<DictTreeNode[]>> {
    return http.get(`/data-dict/${category}/tree`)
  },

  // 创建字典数据
  createDict(data: CreateDictRequest): Promise<ApiResponse<DataDict>> {
    return http.post('/data-dict/', data)
  },

  // 更新字典数据
  updateDict(dictId: number, data: UpdateDictRequest): Promise<ApiResponse<DataDict>> {
    return http.put(`/data-dict/${dictId}`, data)
  },

  // 删除字典数据
  deleteDict(dictId: number): Promise<ApiResponse<void>> {
    return http.delete(`/data-dict/${dictId}`)
  },
}

// 级联和关联API
export const dataDictCascadeApi = {
  // 获取级联字典数据
  getCascadeData(parentCategory: string, childCategory: string): Promise<ApiResponse<any>> {
    return http.get(`/data-dict/cascade/${parentCategory}/${childCategory}`)
  },

  // 获取级联选择器数据
  getCascaderData(parentCategory: string, childCategory: string): Promise<ApiResponse<any>> {
    return http.get(`/data-dict/cascader/${parentCategory}/${childCategory}`)
  },

  // 根据父级编码获取子级数据
  getChildrenByParentCode(
    parentCategory: string,
    parentCode: string,
    childCategory: string,
  ): Promise<ApiResponse<DataDict[]>> {
    return http.get(`/data-dict/children/${parentCategory}/${parentCode}/${childCategory}`)
  },
}

// 保持向后兼容的旧API
export const dataDictApi = {
  // 获取字典分类
  getCategories(): Promise<ApiResponse<string[]>> {
    return http.get('/data-dict/categories')
  },

  // 获取字典列表
  getDictList(
    category: string,
    params?: {
      parent_id?: number
      is_active?: boolean
    },
  ): Promise<ApiResponse<DataDictItem[]>> {
    return http.get(`/data-dict/${category}`, { params })
  },

  // 获取字典选项（下拉选择用）
  getDictOptions(category: string, parentId = 0): Promise<ApiResponse<DataDictOption[]>> {
    return http.get(`/data-dict/${category}/options`, {
      params: { parent_id: parentId },
    })
  },

  // 获取字典树形结构
  getDictTree(category: string): Promise<ApiResponse<DataDictItem[]>> {
    return http.get(`/data-dict/${category}/tree`)
  },

  // 创建字典项
  createDictItem(data: {
    category: string
    name: string
    value: string
    parent_id?: number
    sort_order?: number
    description?: string
  }): Promise<ApiResponse<DataDictItem>> {
    return http.post('/data-dict', data)
  },

  // 更新字典项
  updateDictItem(
    id: number,
    data: {
      name?: string
      value?: string
      parent_id?: number
      sort_order?: number
      description?: string
      is_active?: boolean
    },
  ): Promise<ApiResponse<DataDictItem>> {
    return http.put(`/data-dict/${id}`, data)
  },

  // 删除字典项
  deleteDictItem(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/data-dict/${id}`)
  },
}

// 级联选择器专用API
export const getCascaderData = async (
  parentCategory: string,
  childCategory: string,
): Promise<ApiResponse<{ options: any[] }>> => {
  try {
    // 获取父级数据（品类）
    const parentRes = await dataDictApi.getDictOptions(parentCategory)
    if (!parentRes.success) {
      throw new Error('获取父级数据失败')
    }

    // 获取所有子级数据（品种），不传parent_id参数，获取全部
    const childRes = await http.get(`/data-dict/${childCategory}`)
    if (!childRes.success) {
      throw new Error('获取子级数据失败')
    }

    // 构建级联数据结构
    const options = parentRes.data.map((parent: DataDictOption) => {
      const children = childRes.data
        .filter((child: DataDictItem) => {
          // 根据您的数据结构，品种的parent_id对应品类的id
          return child.parent_id === parent.id
        })
        .map((child: DataDictItem) => ({
          value: child.id,
          label: child.name,
        }))

      return {
        value: parent.id,
        label: parent.name,
        children: children,
      }
    })

    return {
      success: true,
      code: 200,
      data: { options },
      message: '获取级联数据成功',
    }
  } catch (error) {
    console.error('获取级联数据失败:', error)
    return {
      success: false,
      code: 500,
      data: { options: [] },
      message: error instanceof Error ? error.message : '获取级联数据失败',
    }
  }
}

// 根据父级代码获取子级数据
export const getChildrenByParentCode = async (
  _parentCategory: string,
  parentCode: string | number,
  childCategory: string,
): Promise<ApiResponse<DataDictItem[]>> => {
  try {
    // 获取所有子级数据
    const response = await http.get(`/data-dict/${childCategory}`)
    if (!response.success) {
      throw new Error('获取子级数据失败')
    }

    // 过滤出属于指定父级的子级数据
    const children = response.data.filter((child: DataDictItem) => {
      // 根据您的数据结构，使用parent_id字段进行匹配
      return child.parent_id === parentCode
    })

    return {
      success: true,
      code: 200,
      data: children,
      message: '获取子级数据成功',
    }
  } catch (error) {
    console.error('获取子级数据失败:', error)
    return {
      success: false,
      code: 500,
      data: [],
      message: error instanceof Error ? error.message : '获取子级数据失败',
    }
  }
}
