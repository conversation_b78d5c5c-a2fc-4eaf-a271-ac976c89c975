import type { ApiResponse } from '@/types'
import { http } from './request'

export interface Permission {
  id: number
  name: string
  display_name?: string
  resource: string
  action: string
  description?: string
  is_active?: boolean
  created_at: string
  updated_at: string
}

export interface UserPermissions {
  permissions: string[] // 权限标识符数组，如 ["user:read", "dataset:write"]
}

export interface CreatePermissionRequest {
  name: string
  resource: string
  action: string
  description?: string
}

export interface PermissionListParams {
  skip?: number
  limit?: number
  resource?: string
  action?: string
  search?: string
  is_active?: boolean
}

export interface CheckPermissionRequest {
  resource: string
  action: string
}

export interface CheckPermissionResponse {
  has_permission: boolean
}

export interface UpdatePermissionRequest {
  name?: string
  resource?: string
  action?: string
  description?: string
  is_active?: boolean
}

export interface PermissionRole {
  id: number
  name: string
  description?: string
}

// 权限管理API
export const permissionApi = {
  // 获取权限列表
  getPermissions(params?: PermissionListParams): Promise<ApiResponse<Permission[]>> {
    return http.get('/permissions/', { params })
  },

  // 创建权限
  createPermission(data: CreatePermissionRequest): Promise<ApiResponse<Permission>> {
    return http.post('/permissions/', data)
  },

  // 更新权限
  updatePermission(id: number, data: UpdatePermissionRequest): Promise<ApiResponse<Permission>> {
    return http.put(`/permissions/${id}/`, data)
  },

  // 删除权限
  deletePermission(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/permissions/${id}/`)
  },

  // 获取我的权限
  getMyPermissions(): Promise<ApiResponse<UserPermissions>> {
    return http.get('/permissions/me')
  },

  // 为角色分配权限
  assignPermissionsToRole(data: {
    role_id: number
    permission_ids: number[]
  }): Promise<ApiResponse<void>> {
    return http.post('/permissions/assign-to-role/', data)
  },

  // 检查用户权限
  checkUserPermission(
    user_id: number,
    data: Omit<CheckPermissionRequest, 'user_id'>,
  ): Promise<ApiResponse<CheckPermissionResponse>> {
    return http.post('/permissions/check', { ...data, user_id })
  },

  // 检查当前用户权限（简化版）
  checkPermission(resource: string, action: string): Promise<ApiResponse<CheckPermissionResponse>> {
    return http.post('/permissions/check', { resource, action })
  },

  // 获取资源列表
  getResources(): Promise<ApiResponse<string[]>> {
    return http.get('/permissions/resources/')
  },

  // 获取资源操作列表
  getResourceActions(resource: string): Promise<ApiResponse<string[]>> {
    return http.get(`/permissions/resources/${resource}/actions/`)
  },

  // 获取权限关联的角色
  getPermissionRoles(permissionId: number): Promise<ApiResponse<PermissionRole[]>> {
    return http.get(`/permissions/${permissionId}/roles`)
  },
}

// 兼容性导出（保持向后兼容）
export const getCurrentUserPermissions = permissionApi.getMyPermissions
export const checkUserPermission = permissionApi.checkUserPermission
export const getResourceList = permissionApi.getResources
export const getResourceActions = permissionApi.getResourceActions

// 修复权限列表获取函数，返回权限数组而不是包装对象
export const getPermissionList = async (params?: PermissionListParams): Promise<Permission[]> => {
  const response = await permissionApi.getPermissions(params)
  return response.data || []
}

export const getPermissionRoles = permissionApi.getPermissionRoles
export const updatePermission = permissionApi.updatePermission
