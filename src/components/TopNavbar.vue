<template>
  <div class="top-navbar">
    <div class="navbar-container">
      <!-- Logo和品牌名 -->
      <div class="navbar-brand">
        <div class="logo">
          <el-icon><Cpu /></el-icon>
        </div>
        <div class="brand-info">
          <span class="brand-text">宠医智评</span>
          <span class="brand-subtitle">PetMed AI Evaluation Platform</span>
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="navbar-nav">
        <div
          v-for="item in navItems"
          :key="item.path"
          class="nav-item"
          :class="{ active: route.path.startsWith(item.path) && item.path !== '/home' }"
          @click="handleNavClick(item)"
        >
          {{ item.title }}
        </div>
      </div>

      <!-- 右侧操作区 -->
      <div class="navbar-actions">
        <!-- 通知 (仅登录用户显示) -->
        <!-- <div v-if="userStore.isLoggedIn" class="action-item">
          <el-badge :value="3" class="notification-badge">
            <el-icon class="notification-icon"><Bell /></el-icon>
          </el-badge>
        </div> -->

        <!-- 未登录状态：显示登录/注册按钮 -->
        <div v-if="!userStore.isLoggedIn" class="auth-buttons">
          <el-button type="primary" plain @click="router.push('/login')" class="login-btn">
            登录
          </el-button>
          <el-button type="primary" @click="router.push('/register')" class="register-btn">
            注册
          </el-button>
        </div>

        <!-- 已登录状态：显示用户信息下拉菜单 -->
        <el-dropdown v-else @command="handleUserCommand" class="user-dropdown">
          <div class="user-info">
            <el-avatar :size="32" class="user-avatar">
              {{ userStore.user?.username?.charAt(0).toUpperCase() || 'U' }}
            </el-avatar>
            <span class="username">{{ userStore.user?.username || '用户' }}</span>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="settings">账户设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePermissions } from '@/composables/usePermissions'
import { useUserStore } from '@/stores/counter'
import { ArrowDown, Cpu } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const { permissions } = usePermissions()

// 导航项配置（包含权限要求）
const allNavItems = [
  { path: '/home', title: '首页', permission: null, requiresAuth: false },
  {
    path: '/app/datasets',
    title: '数据集管理',
    permission: null, // 移除权限限制，始终显示
    requiresAuth: true,
  },
  {
    path: '/app/dataset-reviews',
    title: '数据集审核',
    permission: null, // 移除权限限制，始终显示
    requiresAuth: true,
  },
  {
    path: '/app/models',
    title: 'AI模型榜单',
    permission: null, // 公开展示，增加平台吸引力
    requiresAuth: false,
  },
  {
    path: '/app/evaluations',
    title: '评测结果',
    permission: null, // 公开展示，增加平台吸引力
    requiresAuth: false,
  },
  { path: '/app/system', title: '系统管理', permission: 'SYSTEM_MANAGEMENT', requiresAuth: true }, // 保留系统管理权限控制
  { path: '/app/help', title: '帮助', permission: null, requiresAuth: false },
]

// 根据权限过滤导航项
const navItems = computed(() => {
  return allNavItems.filter((item) => {
    // 没有权限要求的项目直接显示
    if (!item.permission) {
      return true
    }

    // 只有系统管理需要权限检查
    if (item.permission === 'SYSTEM_MANAGEMENT') {
      return permissions.value.canAccessSystemManagement
    }

    return true
  })
})

// 处理导航点击
function handleNavClick(item: any) {
  // 如果需要登录但用户未登录，跳转到登录页
  if (item.requiresAuth && !userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  // 如果用户已登录，检查特定功能的权限
  if (userStore.isLoggedIn && item.requiresAuth) {
    // 检查数据集管理权限
    if (item.path === '/app/datasets' && !permissions.value.canReadDatasets) {
      ElMessage.warning('您没有数据集管理权限，请联系管理员')
      return
    }

    // 检查数据集审核权限
    if (item.path === '/app/dataset-reviews' && !permissions.value.canReviewDatasets) {
      ElMessage.warning('您没有数据集审核权限，请联系管理员')
      return
    }

    // 检查系统管理权限
    if (item.path === '/app/system' && !permissions.value.canAccessSystemManagement) {
      ElMessage.warning('您没有系统管理权限，请联系管理员')
      return
    }
  }

  // 正常跳转
  router.push(item.path)
}

function handleUserCommand(command: string) {
  switch (command) {
    case 'settings':
      router.push('/app/settings')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.top-navbar {
  background: linear-gradient(135deg, #3981f3, #6f4e9f);
  height: 64px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0 24px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.brand-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.brand-text {
  color: white;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
}

.brand-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 400;
  line-height: 1.2;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  justify-content: flex-start;
  margin-left: 48px;
}

.nav-item {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.nav-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  color: white;
  background: rgba(255, 255, 255, 0.15);
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 登录/注册按钮样式 */
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.login-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.6);
  color: white;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.8);
  color: white;
}

.register-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.register-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.notification-icon {
  color: white;
  font-size: 18px;
}

.notification-badge :deep(.el-badge__content) {
  background: #ff4757;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
}

.username {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.dropdown-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 16px;
  }

  .navbar-nav {
    display: none; /* 在小屏幕上隐藏导航菜单 */
  }

  .brand-text {
    font-size: 18px;
  }

  .brand-subtitle {
    font-size: 10px;
  }

  .navbar-actions {
    gap: 8px;
  }

  .auth-buttons {
    gap: 8px;
  }

  .login-btn,
  .register-btn {
    padding: 6px 16px;
    font-size: 13px;
  }

  .username {
    display: none; /* 在小屏幕上隐藏用户名 */
  }
}
</style>
