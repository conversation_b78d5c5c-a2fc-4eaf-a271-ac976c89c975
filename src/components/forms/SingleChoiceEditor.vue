<template>
  <div class="single-choice-editor">
    <el-form :model="questionData" label-width="120px" label-position="top">
      <!-- 基本信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学科分类" required>
            <el-select
              v-model="questionData.subject_id"
              placeholder="请选择学科"
              style="width: 100%"
              clearable
              filterable
            >
              <el-option
                v-for="option in subjects"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="难度等级" required>
            <div class="difficulty-rating">
              <el-rate
                v-model="difficultyRating"
                :max="5"
                show-text
                :texts="['很简单', '简单', '中等', '困难', '很困难']"
                @change="handleDifficultyChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 题目内容 -->
      <el-form-item label="题目内容">
        <el-input
          v-model="questionData.question_content"
          type="textarea"
          :rows="4"
          placeholder="请输入具体的题目问题内容"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <!-- 选项设置 -->
      <el-form-item label="选项设置" required>
        <div class="options-container">
          <div v-for="(option, key) in questionData.options" :key="key" class="option-row">
            <span class="option-label">选项{{ key }}：</span>
            <el-input
              v-model="questionData.options[key]"
              placeholder="请输入选项内容"
              maxlength="200"
              show-word-limit
              class="option-input"
            />
            <div class="option-actions">
              <el-radio
                v-model="questionData.correct_answer.answer"
                :value="key"
                class="correct-radio"
              >
                正确答案
              </el-radio>
              <el-button
                type="danger"
                text
                size="small"
                @click="removeSpecificOption(key)"
                :disabled="optionCount <= 2"
                class="delete-option-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="add-option-section">
            <el-button @click="addOption" :disabled="optionCount >= 6" type="primary" plain>
              <el-icon><Plus /></el-icon>
              添加选项
            </el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 答案解析 -->
      <el-form-item label="答案解析">
        <el-input
          v-model="questionData.answer_explanation"
          type="textarea"
          :rows="3"
          placeholder="请输入答案解析，帮助理解正确答案的原因"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <!-- 题目图片 -->
      <el-form-item label="题目图片">
        <ImageUpload
          v-model="questionImages"
          :max-count="5"
          accept="image/*"
          @change="syncImageData"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { ImageItem } from '@/components/ImageUpload.vue'
import ImageUpload from '@/components/ImageUpload.vue'
import type { DataDictOption, ExamQuestion } from '@/types'
import { Delete, Plus } from '@element-plus/icons-vue'
import { computed, reactive, ref, watch } from 'vue'

interface Props {
  subjects: DataDictOption[]
  difficultyLevels: DataDictOption[]
  modelValue?: ExamQuestion
}

interface QuestionEditorEmits {
  (e: 'update', question: ExamQuestion): void
  (e: 'delete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<QuestionEditorEmits>()

// 题目数据
const questionData = reactive<ExamQuestion>(
  props.modelValue || {
    question_type_id: 1, // 单选题
    subject_id: props.subjects.length > 0 ? props.subjects[0].id : 0,
    difficulty_level_id: props.difficultyLevels.length > 0 ? props.difficultyLevels[0].id : 1,
    question_title: '',
    question_description: '',
    question_content: '',
    options: { A: '', B: '', C: '', D: '' },
    correct_answer: { answer: 'A', type: 'single' }, // 默认选择A
    answer_explanation: '',
    question_images: [],
    question_metadata: {},
    sort_order: 1,
  },
)

// 图片数据
const questionImages = ref<ImageItem[]>([])

// 难度评分（1-5星）- 根据传入的数据初始化
const difficultyRating = ref(() => {
  if (props.modelValue?.difficulty_level_id) {
    // 根据difficulty_level_id找到对应的星级
    const levelIndex = props.difficultyLevels.findIndex(
      (level) => level.id === props.modelValue.difficulty_level_id,
    )
    return levelIndex >= 0 ? levelIndex + 1 : 1
  }
  return 1
})

// 选项数量
const optionCount = computed(() => Object.keys(questionData.options).length)

// 表单验证 - 移除question_content和question_title的必填要求
const isValid = computed(() => {
  return (
    questionData.subject_id > 0 &&
    questionData.correct_answer.answer !== '' &&
    Object.values(questionData.options).every((option) => option.trim() !== '')
  )
})

// 难度等级映射（星级 -> 数据库ID）
const difficultyMapping = computed(() => {
  const mapping: Record<number, number> = {}
  props.difficultyLevels.forEach((level, index) => {
    mapping[index + 1] = level.id
  })
  return mapping
})

// 处理难度变化
const handleDifficultyChange = (value: number) => {
  questionData.difficulty_level_id = difficultyMapping.value[value] || 1
}

// 添加选项
const addOption = () => {
  const keys = Object.keys(questionData.options)
  const nextKey = String.fromCharCode(65 + keys.length) // A, B, C, D, E, F
  questionData.options[nextKey] = ''
}

// 删除选项
const removeOption = () => {
  const keys = Object.keys(questionData.options)
  if (keys.length > 2) {
    const lastKey = keys[keys.length - 1]
    delete questionData.options[lastKey]

    // 如果删除的是正确答案，清空正确答案
    if (questionData.correct_answer.answer === lastKey) {
      questionData.correct_answer.answer = ''
    }
  }
}

// 删除特定选项
const removeSpecificOption = (key: string) => {
  const keys = Object.keys(questionData.options)
  if (keys.length <= 2) return

  // 删除指定选项
  delete questionData.options[key]

  // 如果删除的是正确答案，重置为第一个选项
  if (questionData.correct_answer.answer === key) {
    const remainingKeys = Object.keys(questionData.options)
    questionData.correct_answer.answer = remainingKeys[0]
  }

  // 重新排序选项键值
  const remainingOptions = Object.keys(questionData.options).sort()
  const newOptions: Record<string, string> = {}
  remainingOptions.forEach((oldKey, index) => {
    const newKey = String.fromCharCode(65 + index) // A, B, C, D...
    newOptions[newKey] = questionData.options[oldKey]

    // 更新正确答案的键值
    if (questionData.correct_answer.answer === oldKey) {
      questionData.correct_answer.answer = newKey
    }
  })

  questionData.options = newOptions
}

// 同步图片数据
const syncImageData = () => {
  questionData.question_images = questionImages.value.map((item) => item.url).filter(Boolean)
}

// 防抖更新
let updateTimer: ReturnType<typeof setTimeout> | null = null

// 自动保存 - 当数据变化时触发
const emitUpdate = () => {
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  updateTimer = setTimeout(() => {
    emit('update', { ...questionData })
  }, 100) // 100ms 防抖
}

// 删除题目
const handleDelete = () => {
  emit('delete')
}

// 监听图片数据变化
watch(
  questionImages,
  () => {
    syncImageData()
  },
  { deep: true },
)

// 监听题目数据变化，实现自动保存
watch(
  questionData,
  () => {
    emitUpdate()
  },
  { deep: true, flush: 'post' },
)
</script>

<style scoped>
.single-choice-editor {
  background: transparent;
}

.difficulty-rating {
  display: flex;
  align-items: center;
}

.options-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.option-label {
  font-weight: 500;
  color: #303133;
  min-width: 60px;
  flex-shrink: 0;
}

.option-input {
  flex: 1;
}

.option-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.correct-radio {
  color: #67c23a;
  margin: 0;
}

.delete-option-btn {
  color: #f56c6c;
  padding: 4px;
}

.delete-option-btn:hover {
  background-color: #fef0f0;
}

.add-option-section {
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .single-choice-editor {
    padding: 16px;
  }

  .editor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .editor-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
