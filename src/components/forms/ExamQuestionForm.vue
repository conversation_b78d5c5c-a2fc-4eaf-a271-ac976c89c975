<template>
  <div class="exam-question-form">
    <!-- 题目管理卡片 -->
    <div class="questions-management-card">
      <div class="card-content">
        <!-- 题目列表 -->
        <div class="questions-list">
          <div
            v-for="(question, index) in questions"
            :key="question.id || `question-${index}`"
            class="question-item"
            :class="{ expanded: expandedQuestions.includes(index) }"
          >
            <!-- 题目头部 -->
            <div class="question-header" @click="toggleQuestion(index)">
              <div class="question-info">
                <span class="question-number">第{{ index + 1 }}题</span>
                <el-tag
                  :type="getQuestionTypeTagType(question.question_type_id)"
                  size="small"
                  class="question-type-tag"
                >
                  {{ getQuestionTypeLabel(question.question_type_id) }}
                </el-tag>
                <!-- 题目标题输入框 -->
                <div class="question-title-editor">
                  <input
                    v-model="question.question_title"
                    class="title-input"
                    :class="{ error: titleErrors[index] }"
                    placeholder="请输入题目标题"
                    @input="updateQuestionTitle(index, $event.target.value)"
                    @blur="validateQuestionTitle(index)"
                  />
                  <div v-if="titleErrors[index]" class="title-error">
                    {{ titleErrors[index] }}
                  </div>
                </div>
              </div>
              <div class="question-actions">
                <el-button type="text" size="small" @click.stop="duplicateQuestion(index)">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click.stop="deleteQuestion(index)"
                  class="delete-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
                <el-button type="text" size="small">
                  <el-icon>
                    <ArrowDown v-if="!expandedQuestions.includes(index)" />
                    <ArrowUp v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>

            <!-- 题目详细编辑区域 -->
            <div v-if="expandedQuestions.includes(index)" class="question-content">
              <!-- 根据题目类型显示对应的编辑器 -->
              <SingleChoiceEditor
                v-if="question.question_type_id === 1"
                :subjects="subjects"
                :difficulty-levels="difficultyLevels"
                :model-value="question"
                @update="updateQuestion(index, $event)"
                @delete="deleteQuestion(index)"
              />
              <MultipleChoiceEditor
                v-else-if="question.question_type_id === 2"
                :subjects="subjects"
                :difficulty-levels="difficultyLevels"
                :model-value="question"
                @update="updateQuestion(index, $event)"
                @delete="deleteQuestion(index)"
              />
              <TrueFalseEditor
                v-else-if="question.question_type_id === 3"
                :subjects="subjects"
                :difficulty-levels="difficultyLevels"
                :model-value="question"
                @update="updateQuestion(index, $event)"
                @delete="deleteQuestion(index)"
              />
            </div>
          </div>

          <!-- 空状态 - 只在没有题目时显示 -->
          <div v-if="questions.length === 0" class="empty-state">
            <el-empty description="暂无题目，请点击下方按钮添加题目"> </el-empty>
          </div>

          <div class="add-question-container">
            <el-dropdown @command="handleAddQuestion" placement="top">
              <el-button type="primary">
                添加题目
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="single">
                    <el-icon><Document /></el-icon>
                    添加单选题
                  </el-dropdown-item>
                  <el-dropdown-item command="multiple">
                    <el-icon><DocumentChecked /></el-icon>
                    添加多选题
                  </el-dropdown-item>
                  <el-dropdown-item command="judge">
                    <el-icon><Select /></el-icon>
                    添加判断题
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { dataDictApi } from '@/api'
import type { DataDictOption, ExamQuestion, ExamQuestionData } from '@/types'
import {
  ArrowDown,
  ArrowUp,
  CopyDocument,
  Delete,
  Document,
  DocumentChecked,
  Select,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import MultipleChoiceEditor from './MultipleChoiceEditor.vue'
import SingleChoiceEditor from './SingleChoiceEditor.vue'
import TrueFalseEditor from './TrueFalseEditor.vue'

interface Props {
  modelValue: ExamQuestionData
  datasetType?: string
}

interface Emits {
  (e: 'update:modelValue', value: ExamQuestionData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 数据集元数据
const datasetMetadata = reactive({
  description: '',
  tags: [] as string[],
})

// 常用标签
const commonTags = ref([
  '基础知识',
  '临床诊断',
  '外科手术',
  '内科治疗',
  '预防医学',
  '药理学',
  '解剖学',
  '病理学',
])

// 题目列表
const questions = ref<ExamQuestion[]>([])

// 字典数据
const questionTypes = ref<DataDictOption[]>([])
const subjects = ref<DataDictOption[]>([])
const difficultyLevels = ref<DataDictOption[]>([])

// 界面状态
const expandedQuestions = ref<number[]>([])
const selectedQuestions = ref<number[]>([])
const titleErrors = ref<Record<number, string>>({})

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 创建新题目的默认数据
const createDefaultQuestion = (): ExamQuestion => ({
  question_type_id: 1,
  subject_id: subjects.value.length > 0 ? subjects.value[0].id : 0,
  difficulty_level_id: difficultyLevels.value.length > 0 ? difficultyLevels.value[0].id : 1,
  question_title: '',
  question_description: '',
  question_content: '',
  options: { A: '', B: '' },
  correct_answer: { answer: '', type: 'single' },
  answer_explanation: '',
  question_images: [],
  question_metadata: {},
  sort_order: questions.value.length + 1,
})

// 获取题型标签
const getQuestionTypeLabel = (typeId: number): string => {
  const typeMap: Record<number, string> = {
    1: '单选题',
    2: '多选题',
    3: '判断题',
  }
  return typeMap[typeId] || '未知题型'
}

const getQuestionTypeTagType = (typeId: number): string => {
  const typeMap: Record<number, string> = {
    1: 'primary',
    2: 'warning',
    3: 'success',
  }
  return typeMap[typeId] || 'info'
}

// 加载字典数据
const loadDictData = async () => {
  try {
    const [questionTypesRes, subjectsRes, difficultyRes] = await Promise.all([
      dataDictApi.getDictOptions('question_type'),
      dataDictApi.getDictOptions('exam_subject'),
      dataDictApi.getDictOptions('difficulty_level'),
    ])

    if (questionTypesRes.success) {
      questionTypes.value = questionTypesRes.data || []
    }

    if (subjectsRes.success) {
      subjects.value = subjectsRes.data || []
    }

    if (difficultyRes.success) {
      difficultyLevels.value = difficultyRes.data || []
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 处理添加题目下拉菜单
const handleAddQuestion = (command: string) => {
  // 创建新题目并添加到列表
  const newQuestion = createDefaultQuestion()
  newQuestion.question_type_id = command === 'single' ? 1 : command === 'multiple' ? 2 : 3

  // 设置默认正确答案
  if (command === 'single') {
    newQuestion.correct_answer = { answer: 'A', type: 'single' }
  } else if (command === 'multiple') {
    newQuestion.correct_answer = { answer: ['A'], type: 'multiple' }
  } else if (command === 'judge') {
    newQuestion.correct_answer = { answer: 'A', type: 'single' }
    newQuestion.options = { A: '正确', B: '错误' }
  }

  questions.value.push(newQuestion)
  const newIndex = questions.value.length - 1

  // 自动展开新添加的题目
  expandedQuestions.value.push(newIndex)

  // 设置标题验证错误提示
  titleErrors.value[newIndex] = '题目标题不能为空'
}

// 删除题目
const deleteQuestion = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这道题目吗？', '确认删除', {
      type: 'warning',
    })
    questions.value.splice(index, 1)
    // 重新调整展开状态的索引
    expandedQuestions.value = expandedQuestions.value
      .filter((i) => i !== index)
      .map((i) => (i > index ? i - 1 : i))
    // ElMessage({ message: '题目已删除', type: 'success' })
  } catch {
    // 用户取消删除
  }
}

// 复制题目
const duplicateQuestion = (index: number) => {
  const originalQuestion = questions.value[index]
  const duplicatedQuestion: ExamQuestion = {
    ...originalQuestion,
    id: undefined, // 新题目没有ID
    sort_order: questions.value.length + 1,
  }
  questions.value.splice(index + 1, 0, duplicatedQuestion)
  // ElMessage.success('题目已复制')
}

// 移动题目
const moveQuestion = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1
  if (newIndex < 0 || newIndex >= questions.value.length) return

  // 交换题目位置
  const temp = questions.value[index]
  questions.value[index] = questions.value[newIndex]
  questions.value[newIndex] = temp

  // 更新排序
  questions.value.forEach((q, i) => {
    q.sort_order = i + 1
  })

  ElMessage.success('题目顺序已调整')
}

// 切换题目展开状态
const toggleQuestion = (index: number) => {
  // 使用 nextTick 确保状态更新的原子性
  nextTick(() => {
    if (expandedQuestions.value.includes(index)) {
      expandedQuestions.value = expandedQuestions.value.filter((i) => i !== index)
    } else {
      expandedQuestions.value = [...expandedQuestions.value, index]
    }
  })
}

// 更新题目标题
const updateQuestionTitle = (index: number, title: string) => {
  if (index >= 0 && index < questions.value.length) {
    questions.value[index].question_title = title
    // 清除错误信息
    if (title.trim()) {
      delete titleErrors.value[index]
    }
  }
}

// 验证题目标题
const validateQuestionTitle = (index: number) => {
  const question = questions.value[index]
  if (!question.question_title || question.question_title.trim() === '') {
    titleErrors.value[index] = '题目标题不能为空'
    return false
  }
  delete titleErrors.value[index]
  return true
}

// 更新题目
const updateQuestion = (index: number, question: ExamQuestion) => {
  questions.value[index] = { ...question }
}

// 批量操作
const handleBatchAction = async (command: string) => {
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning('请先选择要操作的题目')
    return
  }

  switch (command) {
    case 'batch-difficulty':
      // 批量设置难度
      try {
        const { value: difficultyId } = await ElMessageBox.prompt(
          '请选择难度等级',
          '批量设置难度',
          {
            inputType: 'select',
            inputOptions: difficultyLevels.value.map((d) => ({ label: d.name, value: d.id })),
          },
        )
        selectedQuestions.value.forEach((index) => {
          questions.value[index].difficulty_level_id = Number(difficultyId)
        })
        ElMessage.success('批量设置难度成功')
      } catch {
        // 用户取消
      }
      break

    case 'batch-subject':
      // 批量设置学科
      try {
        const { value: subjectId } = await ElMessageBox.prompt('请选择学科', '批量设置学科', {
          inputType: 'select',
          inputOptions: subjects.value.map((s) => ({ label: s.name, value: s.id })),
        })
        selectedQuestions.value.forEach((index) => {
          questions.value[index].subject_id = Number(subjectId)
        })
        ElMessage.success('批量设置学科成功')
      } catch {
        // 用户取消
      }
      break

    case 'delete-selected':
      // 批量删除
      try {
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedQuestions.value.length} 道题目吗？`,
          '确认删除',
          { type: 'warning' },
        )
        // 从后往前删除，避免索引变化
        const sortedIndices = selectedQuestions.value.sort((a, b) => b - a)
        sortedIndices.forEach((index) => {
          questions.value.splice(index, 1)
          expandedQuestions.value = expandedQuestions.value.filter((i) => i !== index)
        })

        // 重新调整剩余展开状态的索引
        expandedQuestions.value = expandedQuestions.value.map((i) => {
          const deletedCount = sortedIndices.filter((deletedIndex) => deletedIndex < i).length
          return i - deletedCount
        })
        selectedQuestions.value = []
        ElMessage.success('批量删除成功')
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听数据变化，向上传递
watch(
  [questions, datasetMetadata],
  () => {
    if (!isUpdatingFromProps.value) {
      const examData: ExamQuestionData = {
        questions: questions.value,
        dataset_metadata: {
          total_questions: questions.value.length,
          ...datasetMetadata,
        },
      }
      emit('update:modelValue', examData)
    }
  },
  { deep: true, flush: 'post' },
)

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    // 避免初始化时的循环更新
    if (newValue && newValue !== oldValue && !isUpdatingFromProps.value) {
      isUpdatingFromProps.value = true

      // 处理新的多题目结构
      if (newValue.questions && Array.isArray(newValue.questions)) {
        questions.value = [...newValue.questions]
      } else if (newValue.question_content) {
        // 兼容旧的单题目结构
        const singleQuestion: ExamQuestion = {
          question_type_id: newValue.question_type_id || 1,
          subject_id: newValue.subject_id || 0,
          difficulty_level_id: newValue.difficulty_level_id || 1,
          question_title: newValue.question_title || '',
          question_description: newValue.question_description || '',
          question_content: newValue.question_content || newValue.question_text || '',
          options: newValue.options || { A: '', B: '' },
          correct_answer: newValue.correct_answer || { answer: '', type: 'single' },
          answer_explanation: newValue.answer_explanation || newValue.explanation || '',
          question_images: newValue.question_images || [],
          question_metadata: newValue.question_metadata || {},
          sort_order: 1,
        }
        questions.value = [singleQuestion]
      }

      // 处理数据集元数据
      if (newValue.dataset_metadata) {
        Object.assign(datasetMetadata, newValue.dataset_metadata)
      }

      nextTick(() => {
        isUpdatingFromProps.value = false
      })
    }
  },
  { immediate: false, deep: true },
)

onMounted(() => {
  loadDictData()

  // 初始化数据
  if (props.modelValue) {
    isUpdatingFromProps.value = true

    if (props.modelValue.questions && Array.isArray(props.modelValue.questions)) {
      questions.value = [...props.modelValue.questions]
    } else if (props.modelValue.question_content) {
      // 兼容旧的单题目结构
      const singleQuestion: ExamQuestion = {
        question_type_id: props.modelValue.question_type_id || 1,
        subject_id: props.modelValue.subject_id || 0,
        difficulty_level_id: props.modelValue.difficulty_level_id || 1,
        question_title: props.modelValue.question_title || '',
        question_description: props.modelValue.question_description || '',
        question_content: props.modelValue.question_content || props.modelValue.question_text || '',
        options: props.modelValue.options || { A: '', B: '' },
        correct_answer: props.modelValue.correct_answer || { answer: '', type: 'single' },
        answer_explanation:
          props.modelValue.answer_explanation || props.modelValue.explanation || '',
        question_images: props.modelValue.question_images || [],
        question_metadata: props.modelValue.question_metadata || {},
        sort_order: 1,
      }
      questions.value = [singleQuestion]
    }

    if (props.modelValue.dataset_metadata) {
      Object.assign(datasetMetadata, props.modelValue.dataset_metadata)
    }

    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
})
</script>

<style scoped>
.exam-question-form {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.question-count {
  margin-left: 12px;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 0;
}

.question-item {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.question-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.question-item.expanded {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
  border-radius: 8px 8px 0 0;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.question-number {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.question-type-tag {
  min-width: 60px;
  text-align: center;
}

.question-title-editor {
  flex: 1;
  margin-left: 8px;
  min-width: 0;
}

.title-input {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  color: #606266;
  background: #fff;
  outline: none;
  transition: border-color 0.3s ease;
}

.title-input:focus {
  border-color: #409eff;
}

.title-input.error {
  border-color: #f56c6c;
}

.title-error {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 2px;
  line-height: 1.2;
}

.question-actions {
  display: flex;
  gap: 4px;
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  color: #f56c6c;
  background-color: #fef0f0;
}

.question-content {
  padding: 20px;
  background: #fafbfc;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid #f0f0f0;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.add-question-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-question-form {
    gap: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .card-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .question-info {
    width: 100%;
  }

  .question-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
