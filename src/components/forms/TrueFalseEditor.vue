<template>
  <div class="true-false-editor">
    <el-form :model="questionData" label-width="120px" label-position="top">
      <!-- 基本信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学科分类" required>
            <el-select
              v-model="questionData.subject_id"
              placeholder="请选择学科"
              style="width: 100%"
              clearable
              filterable
            >
              <el-option
                v-for="option in subjects"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="难度等级" required>
            <div class="difficulty-rating">
              <el-rate
                v-model="difficultyRating"
                :max="5"
                show-text
                :texts="['很简单', '简单', '中等', '困难', '很困难']"
                @change="handleDifficultyChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 题目内容 -->
      <el-form-item label="题目内容">
        <el-input
          v-model="questionData.question_content"
          type="textarea"
          :rows="4"
          placeholder="请输入需要判断正误的陈述内容"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <!-- 正确答案 -->
      <el-form-item label="正确答案" required>
        <div class="answer-selection">
          <el-radio-group v-model="questionData.correct_answer.answer" class="answer-radio-group">
            <el-radio value="A" class="answer-radio correct-answer">
              <el-icon class="answer-icon"><Check /></el-icon>
              正确
            </el-radio>
            <el-radio value="B" class="answer-radio wrong-answer">
              <el-icon class="answer-icon"><Close /></el-icon>
              错误
            </el-radio>
          </el-radio-group>
        </div>
      </el-form-item>

      <!-- 答案解析 -->
      <el-form-item label="答案解析">
        <el-input
          v-model="questionData.answer_explanation"
          type="textarea"
          :rows="3"
          placeholder="请输入答案解析，说明判断的依据和原因"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <!-- 题目图片 -->
      <el-form-item label="题目图片">
        <ImageUpload
          v-model="questionImages"
          :max-count="5"
          accept="image/*"
          @change="syncImageData"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { ImageItem } from '@/components/ImageUpload.vue'
import ImageUpload from '@/components/ImageUpload.vue'
import type { DataDictOption, ExamQuestion } from '@/types'
import { Check, Close } from '@element-plus/icons-vue'
import { computed, reactive, ref, watch } from 'vue'

interface Props {
  subjects: DataDictOption[]
  difficultyLevels: DataDictOption[]
  modelValue?: ExamQuestion
}

interface QuestionEditorEmits {
  (e: 'update', question: ExamQuestion): void
  (e: 'delete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<QuestionEditorEmits>()

// 题目数据
const questionData = reactive<ExamQuestion>(
  props.modelValue || {
    question_type_id: 3, // 判断题
    subject_id: props.subjects.length > 0 ? props.subjects[0].id : 0,
    difficulty_level_id: props.difficultyLevels.length > 0 ? props.difficultyLevels[0].id : 1,
    question_title: '',
    question_description: '',
    question_content: '',
    options: { A: '正确', B: '错误' }, // 判断题固定选项
    correct_answer: { answer: 'A', type: 'single' }, // 默认选择正确
    answer_explanation: '',
    question_images: [],
    question_metadata: {},
    sort_order: 1,
  },
)

// 图片数据
const questionImages = ref<ImageItem[]>([])

// 难度评分（1-5星）- 根据传入的数据初始化
const difficultyRating = ref(() => {
  if (props.modelValue?.difficulty_level_id) {
    // 根据difficulty_level_id找到对应的星级
    const levelIndex = props.difficultyLevels.findIndex(
      (level) => level.id === props.modelValue.difficulty_level_id,
    )
    return levelIndex >= 0 ? levelIndex + 1 : 1
  }
  return 1
})

// 表单验证 - 移除question_content和question_title的必填要求
const isValid = computed(() => {
  return questionData.subject_id > 0 && questionData.correct_answer.answer !== ''
})

// 难度等级映射（星级 -> 数据库ID）
const difficultyMapping = computed(() => {
  const mapping: Record<number, number> = {}
  props.difficultyLevels.forEach((level, index) => {
    mapping[index + 1] = level.id
  })
  return mapping
})

// 处理难度变化
const handleDifficultyChange = (value: number) => {
  questionData.difficulty_level_id = difficultyMapping.value[value] || 1
}

// 同步图片数据
const syncImageData = () => {
  questionData.question_images = questionImages.value.map((item) => item.url).filter(Boolean)
}

// 防抖更新
let updateTimer: ReturnType<typeof setTimeout> | null = null

// 自动保存 - 当数据变化时触发
const emitUpdate = () => {
  if (updateTimer) {
    clearTimeout(updateTimer)
  }

  updateTimer = setTimeout(() => {
    emit('update', { ...questionData })
  }, 100) // 100ms 防抖
}

// 删除题目
const handleDelete = () => {
  emit('delete')
}

// 监听图片数据变化
watch(
  questionImages,
  () => {
    syncImageData()
  },
  { deep: true },
)

// 监听题目数据变化，实现自动保存
watch(
  questionData,
  () => {
    emitUpdate()
  },
  { deep: true, flush: 'post' },
)
</script>

<style scoped>
.true-false-editor {
  background: transparent;
}

.difficulty-rating {
  display: flex;
  align-items: center;
}

.answer-selection {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.answer-radio-group {
  display: flex;
  gap: 24px;
}

.answer-radio {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.answer-radio:hover {
  background: white;
  border-color: #e5e7eb;
}

.answer-radio.correct-answer {
  color: #67c23a;
}

.answer-radio.wrong-answer {
  color: #f56c6c;
}

.answer-radio :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: currentColor;
  border-color: currentColor;
}

.answer-icon {
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .true-false-editor {
    padding: 16px;
  }

  .editor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .editor-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .answer-radio-group {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
