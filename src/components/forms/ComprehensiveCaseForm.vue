<template>
  <div class="comprehensive-case-form">
    <el-form
      ref="formRef"
      :model="{ formData, specialtyCategoryIds }"
      :rules="formRules"
      label-width="120px"
      label-position="top"
    >
      <!-- 动物基本信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专科分类" prop="specialty_category_ids">
            <el-select
              v-model="specialtyCategoryIds"
              multiple
              filterable
              placeholder="请选择专科分类"
              style="width: 100%"
            >
              <el-option
                v-for="option in specialtyCategories"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="疾病类型" prop="formData.department_disease_type_id">
            <el-select
              v-model="formData.department_disease_type_id"
              placeholder="请选择疾病类型"
              style="width: 100%"
            >
              <el-option
                v-for="option in departmentDiseaseTypes"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="宠物种类/品种" prop="formData.animal_category_id">
            <el-cascader
              v-model="animalCascadeValue"
              :options="animalCascadeOptions"
              placeholder="请选择宠物种类和品种"
              style="width: 100%"
              @change="handleAnimalCascadeChange"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="体重（kg）" prop="formData.weight">
            <el-input-number
              v-model="formData.weight"
              :min="0"
              :max="200"
              :precision="2"
              placeholder="请输入体重"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年龄" prop="formData.age_value">
            <div style="display: flex; gap: 8px; width: 100%">
              <el-input-number
                v-model="formData.age_value"
                :min="0"
                :max="50"
                :precision="1"
                placeholder="请输入年龄"
                style="flex: 1; min-width: 120px"
              />
              <el-select
                v-model="formData.age_unit_id"
                placeholder="单位"
                style="width: 80px; flex-shrink: 0"
              >
                <el-option
                  v-for="option in ageUnits"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- 占位，保持布局 -->
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="宠物性别" prop="formData.gender_id">
            <el-radio-group v-model="formData.gender_id">
              <el-radio :value="1">公</el-radio>
              <el-radio :value="2">母</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否绝育" prop="data.is_neutered">
            <el-radio-group v-model="formData.is_neutered">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="2">否</el-radio>
              <el-radio :value="3">未知</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 症状与病史 -->
      <el-form-item label="主诉/症状" prop="formData.chief_complaint">
        <el-input
          v-model="formData.chief_complaint"
          type="textarea"
          :rows="4"
          placeholder="请详细描述主诉或症状"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="既往病史" prop="formData.medical_history">
        <el-input
          v-model="formData.medical_history"
          type="textarea"
          :rows="3"
          placeholder="请输入既往病史"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="体格检查" prop="formData.physical_examination">
        <el-input
          v-model="formData.physical_examination"
          type="textarea"
          :rows="4"
          placeholder="请输入体格检查内容"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="实验室检查" prop="formData.laboratory_examination">
        <el-input
          v-model="formData.laboratory_examination"
          type="textarea"
          :rows="4"
          placeholder="请输入实验室检查结果"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <!-- 化验单图片上传 -->
      <el-form-item label="化验单图片" prop="formData.lab_images">
        <ImageUpload
          v-model="labImages"
          :max-count="5"
          :max-size="10"
          :accept-types="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']"
          :upload-path="'vet_eval/clinical_case_datasets_imgs'"
          placeholder="支持 jpg/png/gif 等图片格式，单个文件不超过 10MB，最多上传 5 张"
          @upload-success="handleImageUploadSuccess"
          @upload-error="handleImageUploadError"
          @remove="handleImageRemove"
        />
      </el-form-item>

      <!-- 诊断与治疗 -->
      <el-form-item label="鉴别诊断" prop="formData.differential_diagnosis">
        <el-input
          v-model="formData.differential_diagnosis"
          type="textarea"
          :rows="3"
          placeholder="请输入鉴别诊断"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="诊断结果" prop="formData.diagnosis_result">
        <el-input
          v-model="formData.diagnosis_result"
          type="textarea"
          :rows="3"
          placeholder="请输入诊断结果"
          maxlength="500"
          show-word-limit
          @keydown.enter.prevent
        />
      </el-form-item>

      <el-form-item label="治疗方案/建议" prop="formData.treatment_plan">
        <el-input
          v-model="formData.treatment_plan"
          type="textarea"
          :rows="4"
          placeholder="请详细描述治疗方案或建议"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="用药信息" prop="formData.medication_info">
        <el-input
          v-model="formData.medication_info"
          type="textarea"
          :rows="3"
          placeholder="请输入用药信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="病例总结" prop="formData.case_summary">
        <el-input
          v-model="formData.case_summary"
          type="textarea"
          :rows="4"
          placeholder="请总结本病例的关键点"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { dataDictApi } from '@/api'
import { getCascaderData } from '@/api/dataDict'
import type { DatasetType } from '@/api/fileUpload'
import ImageUpload, { type ImageItem } from '@/components/ImageUpload.vue'
import type { ComprehensiveCaseData, DataDictOption } from '@/types'
import { showError } from '@/utils'
import type { FormInstance, FormRules } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'

// 级联选择器的配置
interface CascaderOption {
  value: number
  label: string
  children?: CascaderOption[]
}

interface Props {
  modelValue: ComprehensiveCaseData
  datasetType?: DatasetType // 数据集类型，用于确定上传路径
}

interface Emits {
  (e: 'update:modelValue', value: ComprehensiveCaseData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 暴露验证方法给父组件
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 清除验证状态
const clearValidate = () => {
  formRef.value?.clearValidate()
}

// 表单数据
const formData = reactive<ComprehensiveCaseData & { differential_diagnosis?: string }>({
  specialty_category_id: undefined,
  department_disease_type_id: undefined,
  animal_category_id: undefined,
  animal_breed_id: undefined,
  gender_id: 1, // 默认选择公
  age_value: undefined,
  age_unit_id: 1, // 默认选择岁
  weight: undefined,
  is_neutered: 2, // 默认选择否（未绝育）
  chief_complaint: '',
  medical_history: '',
  physical_examination: '',
  laboratory_examination: '',
  lab_images: '',
  differential_diagnosis: '',
  diagnosis_result: '',
  treatment_plan: '',
  medication_info: '',
  case_summary: '',
})

// 专科分类多选数据
const specialtyCategoryIds = ref<number[]>([])

// 字典数据
const specialtyCategories = ref<DataDictOption[]>([])
const departmentDiseaseTypes = ref<DataDictOption[]>([])

// 年龄单位数据
const ageUnits = ref<DataDictOption[]>([
  { id: 1, name: '岁', value: 'year' },
  { id: 2, name: '月', value: 'month' },
  { id: 3, name: '周', value: 'week' },
  { id: 4, name: '天', value: 'day' },
])

// 级联选择器相关
const animalCascadeOptions = ref<CascaderOption[]>([])
const animalCascadeValue = ref<number[]>([])

// 级联选择器默认使用 Element Plus 的标准配置
// value: 'value', label: 'label', children: 'children' 是默认值

// 化验单图片列表
const labImages = ref<ImageItem[]>([])

// 表单验证规则
const formRules: FormRules = {
  specialtyCategoryIds: [{ required: true, message: '请选择专科分类', trigger: 'change' }],
  'formData.department_disease_type_id': [
    { required: true, message: '请选择疾病类型', trigger: 'change' },
  ],
  'formData.animal_category_id': [
    { required: true, message: '请选择宠物种类/品种', trigger: 'change' },
  ],
  'formData.weight': [{ required: true, message: '请输入体重', trigger: 'blur' }],
  'formData.age_value': [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  'formData.gender_id': [{ required: true, message: '请选择宠物性别', trigger: 'change' }],
  'formData.chief_complaint': [{ required: true, message: '请输入主诉/症状', trigger: 'blur' }],
  'formData.physical_examination': [{ required: true, message: '请输入体格检查', trigger: 'blur' }],
  'formData.medical_history': [{ required: true, message: '请输入既往病史', trigger: 'blur' }],
  'formData.differential_diagnosis': [
    { required: true, message: '请输入鉴别诊断', trigger: 'blur' },
  ],
  'formData.diagnosis_result': [{ required: true, message: '请输入诊断结果', trigger: 'blur' }],
  'formData.treatment_plan': [{ required: true, message: '请输入治疗方案/建议', trigger: 'blur' }],
}

// 加载字典数据
const loadDictData = async () => {
  try {
    // 专科分类
    const specialtyCategoriesRes = await dataDictApi.getDictOptions('specialty_category')
    if (specialtyCategoriesRes.success) {
      specialtyCategories.value = specialtyCategoriesRes.data
    } else {
      console.error('专科分类数据加载失败:', specialtyCategoriesRes.message)
    }

    // 疾病类型
    try {
      const departmentDiseaseTypesRes = await dataDictApi.getDictOptions('department_disease')
      if (departmentDiseaseTypesRes.success && departmentDiseaseTypesRes.data.length > 0) {
        departmentDiseaseTypes.value = departmentDiseaseTypesRes.data
      } else {
        throw new Error('API返回数据为空或失败')
      }
    } catch (error) {
      // 使用完整的测试数据
      departmentDiseaseTypes.value = [
        { id: 1, name: '内科', value: 'internal_medicine' },
        { id: 2, name: '外科', value: 'surgery' },
        { id: 3, name: '皮肤科', value: 'dermatology' },
        { id: 4, name: '眼科', value: 'ophthalmology' },
        { id: 5, name: '骨科', value: 'orthopedics' },
        { id: 6, name: '心脏科', value: 'cardiology' },
        { id: 7, name: '肿瘤科', value: 'oncology' },
        { id: 8, name: '神经科', value: 'neurology' },
        { id: 9, name: '消化科', value: 'gastroenterology' },
        { id: 10, name: '呼吸科', value: 'pulmonology' },
        { id: 11, name: '泌尿科', value: 'urology' },
        { id: 12, name: '生殖科', value: 'reproductive' },
        { id: 13, name: '急诊科', value: 'emergency' },
        { id: 14, name: '麻醉科', value: 'anesthesiology' },
        { id: 15, name: '影像科', value: 'radiology' },
      ]
    }

    // 加载动物级联数据
    await loadAnimalCascadeData()
  } catch (error) {
    console.error('加载字典数据失败:', error)
    showError('加载字典数据失败')

    // 提供测试数据作为后备
    if (departmentDiseaseTypes.value.length === 0) {
      departmentDiseaseTypes.value = [
        { id: 1, name: '内科', value: 'internal_medicine' },
        { id: 2, name: '外科', value: 'surgery' },
        { id: 3, name: '皮肤科', value: 'dermatology' },
        { id: 4, name: '眼科', value: 'ophthalmology' },
        { id: 5, name: '骨科', value: 'orthopedics' },
        { id: 6, name: '心脏科', value: 'cardiology' },
        { id: 7, name: '肿瘤科', value: 'oncology' },
        { id: 8, name: '神经科', value: 'neurology' },
        { id: 9, name: '消化科', value: 'gastroenterology' },
        { id: 10, name: '呼吸科', value: 'pulmonology' },
        { id: 11, name: '泌尿科', value: 'urology' },
        { id: 12, name: '生殖科', value: 'reproductive' },
        { id: 13, name: '急诊科', value: 'emergency' },
        { id: 14, name: '麻醉科', value: 'anesthesiology' },
        { id: 15, name: '影像科', value: 'radiology' },
      ]
    }

    if (specialtyCategories.value.length === 0) {
      specialtyCategories.value = [
        { id: 1, name: '小动物内科', value: 'small_animal_internal' },
        { id: 2, name: '小动物外科', value: 'small_animal_surgery' },
        { id: 3, name: '大动物医学', value: 'large_animal_medicine' },
        { id: 4, name: '禽病学', value: 'poultry_disease' },
        { id: 5, name: '水产动物医学', value: 'aquatic_animal_medicine' },
      ]
    }
  }
}

// 加载动物级联数据 - 参考示例文件的实现
const loadAnimalCascadeData = async () => {
  try {
    console.log('开始加载动物级联数据...')
    const response = await getCascaderData('animal_category', 'animal_breed')

    if (response.success) {
      animalCascadeOptions.value = response.data.options
    } else {
      showError(response.message || '获取级联数据失败')
    }
  } catch (error) {
    console.error('加载动物级联数据失败:', error)
    showError('加载动物级联数据失败')
  }
}

// 图片上传成功回调
const handleImageUploadSuccess = (_file: ImageItem) => {
  updateLabImagesFormData()
}

// 图片上传失败回调
const handleImageUploadError = (error: string) => {
  console.error('图片上传失败:', error)
}

// 图片删除回调
const handleImageRemove = (_file: ImageItem) => {
  updateLabImagesFormData()
}

// 更新表单中的化验单图片数据
const updateLabImagesFormData = () => {
  const urls = labImages.value
    .filter((img) => img.status === 'success' && img.url)
    .map((img) => img.url)

  const newLabImages = urls.join(',')

  // 只有当值真正改变时才更新
  if (formData.lab_images !== newLabImages) {
    formData.lab_images = newLabImages
  }
}

// 处理动物级联选择变化
const handleAnimalCascadeChange = (value: number[]) => {
  console.log('级联选择变化:', value)

  if (value && value.length >= 2) {
    // 取最后两级作为品类和品种
    const categoryId = value[value.length - 2]
    const breedId = value[value.length - 1]

    formData.animal_category_id = categoryId
    formData.animal_breed_id = breedId

    console.log('设置品类ID:', categoryId, '品种ID:', breedId)
  } else if (value && value.length === 1) {
    // 只选择了品类，清空品种
    formData.animal_category_id = value[0]
    formData.animal_breed_id = null as any
  } else {
    // 清空选择
    formData.animal_category_id = null as any
    formData.animal_breed_id = null as any
  }
}

// 初始化级联选择器的值
const initAnimalCascadeValue = () => {
  if (formData.animal_category_id && formData.animal_breed_id) {
    animalCascadeValue.value = [formData.animal_category_id, formData.animal_breed_id]
    console.log('初始化级联选择器值:', animalCascadeValue.value)
  } else if (formData.animal_category_id) {
    animalCascadeValue.value = [formData.animal_category_id]
  } else {
    animalCascadeValue.value = []
  }
}

// 监听专科分类变化，同步到表单数据
watch(
  specialtyCategoryIds,
  (newValue) => {
    // 将多选的专科分类ID数组转换为单个ID（取第一个）或保持为数组
    // 这里我们保持为数组，但需要在提交时处理
    formData.specialty_category_id = newValue.length > 0 ? newValue[0] : undefined
  },
  { deep: true },
)

// 监听表单数据变化，向上传递
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

// 将字符串格式的图片路径转换为ImageItem数组
const parseLabImages = (labImagesString: string | string[] | null | undefined): ImageItem[] => {
  if (!labImagesString) return []

  let urls: string[] = []

  // 如果已经是数组，直接使用
  if (Array.isArray(labImagesString)) {
    urls = labImagesString.filter((url) => url && url.trim()).map((url) => url.trim())
  }
  // 如果是字符串，按逗号分割
  else if (typeof labImagesString === 'string') {
    urls = labImagesString
      .split(',')
      .filter((url) => url.trim())
      .map((url) => url.trim())
  }

  return urls.map((url, index) => ({
    uid: `existing_${Date.now()}_${index}`,
    name: url.split('/').pop() || `image_${index + 1}`,
    url: url,
    cos_key: '', // 已存在的文件，cos_key为空
    status: 'success' as const,
  }))
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, newValue)
      // 初始化级联选择器的值
      initAnimalCascadeValue()
      // 初始化化验单图片列表
      if (newValue.lab_images) {
        labImages.value = parseLabImages(newValue.lab_images)
      }
      // 初始化专科分类多选值
      if (newValue.specialty_category_id) {
        specialtyCategoryIds.value = Array.isArray(newValue.specialty_category_id)
          ? newValue.specialty_category_id
          : [newValue.specialty_category_id]
      }
    }
  },
  { immediate: true, deep: true },
)

onMounted(() => {
  loadDictData()
})

// 暴露验证方法供父组件调用
defineExpose({
  validate,
  clearValidate,
})
</script>

<style scoped>
.comprehensive-case-form {
  width: 100%;
}
</style>
