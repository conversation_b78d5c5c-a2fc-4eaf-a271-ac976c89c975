<template>
  <div class="question-editor">
    <!-- 基本信息 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="题目类型" required>
          <el-select
            v-model="questionData.question_type_id"
            placeholder="请选择题目类型"
            style="width: 100%"
            @change="handleQuestionTypeChange"
            clearable
          >
            <el-option
              v-for="option in questionTypes"
              :key="option.id"
              :label="option.name"
              :value="option.id"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="学科分类" required>
          <el-select
            v-model="questionData.subject_id"
            placeholder="请选择学科"
            style="width: 100%"
            clearable
            filterable
          >
            <el-option
              v-for="option in subjects"
              :key="option.id"
              :label="option.name"
              :value="option.id"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="难度等级" required>
          <el-select
            v-model="questionData.difficulty_level_id"
            placeholder="请选择难度"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="option in difficultyLevels"
              :key="option.id"
              :label="option.name"
              :value="option.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 题目标题和描述 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="题目标题" required>
          <el-input
            v-model="questionData.question_title"
            placeholder="请输入题目标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 题目图片 -->
    <el-form-item label="题目图片">
      <ImageUpload
        v-model="questionImages"
        :max-count="5"
        :max-size="10"
        :accept-types="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']"
        :upload-path="'vet_eval/exam_question_imgs'"
        placeholder="支持 jpg/png/gif 等图片格式，单个文件不超过 10MB，最多上传 5 张"
        @upload-success="handleImageUploadSuccess"
        @upload-error="handleImageUploadError"
        @remove="handleImageRemove"
      />
    </el-form-item>

    <!-- 动态选项设置 -->
    <el-form-item v-if="showOptionsSection" label="选项设置" required>
      <div class="options-container">
        <!-- 单选题和多选题 -->
        <template v-if="questionData.question_type_id === 1 || questionData.question_type_id === 2">
          <div v-for="(option, key) in questionData.options" :key="key" class="option-item">
            <div class="option-label">选项 {{ key }}:</div>
            <el-input
              v-model="questionData.options[key]"
              placeholder="请输入选项内容"
              maxlength="200"
            />
            <el-button
              v-if="Object.keys(questionData.options || {}).length > 2"
              type="danger"
              link
              @click="removeOption(key)"
            >
              删除
            </el-button>
          </div>

          <el-button
            v-if="Object.keys(questionData.options || {}).length < 6"
            type="primary"
            link
            @click="addOption"
          >
            + 添加选项
          </el-button>
        </template>

        <!-- 判断题 -->
        <template v-else-if="questionData.question_type_id === 3">
          <div class="option-item">
            <div class="option-label">选项 A:</div>
            <el-input v-model="questionData.options.A" readonly value="正确" />
          </div>
          <div class="option-item">
            <div class="option-label">选项 B:</div>
            <el-input v-model="questionData.options.B" readonly value="错误" />
          </div>
        </template>
      </div>
    </el-form-item>

    <!-- 正确答案设置 -->
    <el-form-item v-if="showAnswerSection" label="正确答案" required>
      <!-- 单选题 -->
      <template v-if="questionData.question_type_id === 1 || questionData.question_type_id === 3">
        <el-select
          v-model="questionData.correct_answer.answer"
          placeholder="请选择正确答案"
          style="width: 100%"
        >
          <el-option
            v-for="(option, key) in questionData.options"
            :key="key"
            :label="`${key}. ${option}`"
            :value="key"
          />
        </el-select>
      </template>

      <!-- 多选题 -->
      <template v-else-if="questionData.question_type_id === 2">
        <el-select
          v-model="questionData.correct_answer.answer"
          placeholder="请选择正确答案（可多选）"
          style="width: 100%"
          multiple
        >
          <el-option
            v-for="(option, key) in questionData.options"
            :key="key"
            :label="`${key}. ${option}`"
            :value="key"
          />
        </el-select>
      </template>
    </el-form-item>

    <!-- 答案解析 -->
    <el-form-item label="答案解析">
      <el-input
        v-model="questionData.answer_explanation"
        type="textarea"
        :rows="3"
        placeholder="请输入答案解析，帮助理解正确答案的原因"
        maxlength="1000"
        show-word-limit
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import type { ImageItem } from '@/components/ImageUpload.vue'
import ImageUpload from '@/components/ImageUpload.vue'
import type { DataDictOption, ExamQuestion } from '@/types'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'

interface Props {
  modelValue: ExamQuestion
  questionTypes: DataDictOption[]
  subjects: DataDictOption[]
  difficultyLevels: DataDictOption[]
}

interface Emits {
  (e: 'update:modelValue', value: ExamQuestion): void
  (e: 'update', value: ExamQuestion): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 题目数据
const questionData = reactive<ExamQuestion>({ ...props.modelValue })

// 图片数据
const questionImages = ref<ImageItem[]>([])

// 可用的选项标识
const availableOptionKeys = ['A', 'B', 'C', 'D', 'E', 'F']

// 计算属性
const showOptionsSection = computed(() => {
  return questionData.question_type_id > 0
})

const showAnswerSection = computed(() => {
  return (
    questionData.question_type_id > 0 &&
    questionData.options &&
    Object.keys(questionData.options).length > 0
  )
})

// 题型变化处理
const handleQuestionTypeChange = (questionTypeId: number) => {
  // 重置选项和答案
  if (questionTypeId === 1) {
    // 单选题：默认A、B两个选项
    questionData.options = { A: '', B: '' }
    questionData.correct_answer = { answer: '', type: 'single' }
  } else if (questionTypeId === 2) {
    // 多选题：默认A、B两个选项
    questionData.options = { A: '', B: '' }
    questionData.correct_answer = { answer: [], type: 'multiple' }
  } else if (questionTypeId === 3) {
    // 判断题：固定A（正确）、B（错误）选项
    questionData.options = { A: '正确', B: '错误' }
    questionData.correct_answer = { answer: '', type: 'single' }
  }
}

// 添加选项（仅适用于单选题和多选题）
const addOption = () => {
  if (questionData.question_type_id === 3) return // 判断题不允许添加选项

  const currentKeys = Object.keys(questionData.options || {})
  const nextKey = availableOptionKeys.find((key) => !currentKeys.includes(key))
  if (nextKey && questionData.options) {
    questionData.options[nextKey] = ''
  }
}

// 删除选项（仅适用于单选题和多选题）
const removeOption = (key: string) => {
  if (questionData.question_type_id === 3) return // 判断题不允许删除选项
  if (!questionData.options) return

  delete questionData.options[key]

  // 如果删除的是当前正确答案，清空正确答案
  if (questionData.question_type_id === 1 || questionData.question_type_id === 3) {
    // 单选题和判断题
    if (questionData.correct_answer.answer === key) {
      questionData.correct_answer.answer = ''
    }
  } else if (questionData.question_type_id === 2) {
    // 多选题
    const answers = questionData.correct_answer.answer as string[]
    const index = answers.indexOf(key)
    if (index > -1) {
      answers.splice(index, 1)
    }
  }
}

// 图片上传处理
const handleImageUploadSuccess = (file: ImageItem) => {
  console.log('图片上传成功:', file)
}

const handleImageUploadError = (error: string) => {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败: ' + error)
}

const handleImageRemove = (file: ImageItem) => {
  console.log('图片删除:', file)
}

// 同步图片数据到题目
const syncImageData = () => {
  if (questionImages.value.length > 0) {
    questionData.question_images = questionImages.value
      .filter((img) => img.status === 'success')
      .map((img) => img.url)
  } else {
    questionData.question_images = []
  }
}

// 监听图片数据变化
watch(
  questionImages,
  () => {
    syncImageData()
  },
  { deep: true },
)

// 防止循环更新的标志
const isUpdatingFromParent = ref(false)

// 监听题目数据变化，向上传递
watch(
  questionData,
  (newValue) => {
    if (!isUpdatingFromParent.value) {
      emit('update:modelValue', { ...newValue })
      emit('update', { ...newValue })
    }
  },
  { deep: true, flush: 'post' },
)

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
      isUpdatingFromParent.value = true

      Object.assign(questionData, newValue)

      // 处理图片数据
      if (newValue.question_images && Array.isArray(newValue.question_images)) {
        questionImages.value = newValue.question_images.map((url, index) => ({
          uid: `existing-${index}`,
          name: `image-${index + 1}`,
          url: url,
          cos_key: '',
          status: 'success' as const,
        }))
      }

      nextTick(() => {
        isUpdatingFromParent.value = false
      })
    }
  },
  { immediate: false, deep: true },
)

onMounted(() => {
  // 初始化数据
  if (props.modelValue) {
    isUpdatingFromParent.value = true
    Object.assign(questionData, props.modelValue)

    // 处理图片数据
    if (props.modelValue.question_images && Array.isArray(props.modelValue.question_images)) {
      questionImages.value = props.modelValue.question_images.map((url, index) => ({
        uid: `existing-${index}`,
        name: `image-${index + 1}`,
        url: url,
        cos_key: '',
        status: 'success' as const,
      }))
    }

    nextTick(() => {
      isUpdatingFromParent.value = false
    })
  }
})
</script>

<style scoped>
.question-editor {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-label {
  min-width: 80px;
  font-weight: 500;
  color: #303133;
}

.option-item .el-input {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .question-editor {
    padding: 16px;
  }

  .option-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .option-item .el-input {
    width: 100%;
  }
}
</style>
