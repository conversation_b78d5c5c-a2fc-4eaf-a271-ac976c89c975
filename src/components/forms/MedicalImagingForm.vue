<template>
  <div class="medical-imaging-form">
    <el-form
      ref="formRef"
      :model="{ formData }"
      :rules="formRules"
      label-width="120px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="系统类型" prop="data.system_type_id">
            <el-select
              v-model="formData.system_type_id"
              placeholder="请选择系统类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="option in systemTypes"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="影像类型" prop="data.image_category_id">
            <el-select
              v-model="formData.image_category_id"
              placeholder="请选择影像类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="option in imageCategories"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="影像细节" prop="data.detailed_category_id">
            <el-select
              v-model="formData.detailed_category_id"
              placeholder="请选择影像细节"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="option in imageDetails"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 动物品种级联选择 -->
      <el-form-item label="动物品种" prop="data.animal_breed_id">
        <el-cascader
          v-model="animalCascadeValue"
          :options="animalCascadeOptions"
          :props="{ expandTrigger: 'hover' }"
          placeholder="请选择动物品类和品种"
          style="width: 100%"
          clearable
          @change="handleAnimalCascadeChange"
        />
      </el-form-item>

      <el-form-item label="影像征象" prop="data.image_signs">
        <el-input
          v-model="formData.image_signs"
          type="textarea"
          :rows="3"
          placeholder="请描述影像中观察到的征象和特征"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="详细描述" prop="data.detailed_description">
        <el-input
          v-model="formData.detailed_description"
          type="textarea"
          :rows="4"
          placeholder="请详细描述影像的具体情况、发现和异常"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="诊断建议" prop="data.diagnosis_suggestion">
        <el-input
          v-model="formData.diagnosis_suggestion"
          type="textarea"
          :rows="3"
          placeholder="请提供基于影像的诊断建议"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 影像文件上传 -->
      <el-form-item label="影像文件" prop="data.image_files">
        <ImageUpload
          v-model="imageFiles"
          :max-count="10"
          :max-size="10"
          :accept-types="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'dcm']"
          :upload-path="'vet_eval/medical_image_datasets_imgs'"
          placeholder="支持 jpg/png/gif/dcm 等图片格式，单个文件不超过 10MB，最多上传 10 张"
          @upload-success="handleImageUploadSuccess"
          @upload-error="handleImageUploadError"
          @remove="handleImageRemove"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { dataDictApi } from '@/api'
import { getCascaderData } from '@/api/dataDict'
import type { DatasetType } from '@/api/fileUpload'
import ImageUpload, { type ImageItem } from '@/components/ImageUpload.vue'
import type { DataDictOption, MedicalImagingData } from '@/types'
import { showError } from '@/utils'
import type { FormInstance, FormRules } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'

interface Props {
  modelValue: MedicalImagingData
  datasetType?: DatasetType // 数据集类型，用于确定上传路径
}

interface Emits {
  (e: 'update:modelValue', value: MedicalImagingData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 暴露验证方法给父组件
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
})

// 表单数据
const formData = reactive<MedicalImagingData>({
  system_type_id: undefined,
  image_category_id: undefined,
  detailed_category_id: undefined,
  animal_breed_id: undefined,
  image_signs: '',
  detailed_description: '',
  diagnosis_suggestion: '',
  image_files: '',
})

// 字典数据
const systemTypes = ref<DataDictOption[]>([])
const imageCategories = ref<DataDictOption[]>([])
const imageDetails = ref<DataDictOption[]>([])

// 级联选择器相关
const animalCascadeOptions = ref<any[]>([])
const animalCascadeValue = ref<number[]>([])

// 影像文件列表
const imageFiles = ref<ImageItem[]>([])

// 表单验证规则
const formRules: FormRules = {
  // 可以根据需要添加必填验证规则
  // 'formData.system_type_id': [{ required: true, message: '请选择系统类型', trigger: 'change' }],
  // 'formData.image_category_id': [{ required: true, message: '请选择影像类型', trigger: 'change' }],
}

// 加载字典数据
const loadDictData = async () => {
  try {
    // 系统类型
    const systemTypesRes = await dataDictApi.getDictOptions('system_type')
    if (systemTypesRes.success) {
      systemTypes.value = systemTypesRes.data
    }

    // 影像分类
    const imageCategoriesRes = await dataDictApi.getDictOptions('image_category')
    if (imageCategoriesRes.success) {
      imageCategories.value = imageCategoriesRes.data
    }

    // 影像细节
    const imageDetailsRes = await dataDictApi.getDictOptions('image_detail')
    if (imageDetailsRes.success) {
      imageDetails.value = imageDetailsRes.data
    }

    // 加载动物级联数据
    await loadAnimalCascadeData()
  } catch (error) {
    console.error('加载字典数据失败:', error)
    showError('加载字典数据失败')
  }
}

// 加载动物级联数据
const loadAnimalCascadeData = async () => {
  try {
    const response = await getCascaderData('animal_category', 'animal_breed')

    if (response.success) {
      animalCascadeOptions.value = response.data.options
    } else {
      showError(response.message || '获取级联数据失败')
    }
  } catch (error) {
    console.error('加载动物级联数据失败:', error)
    showError('加载动物级联数据失败')
  }
}

// 处理动物级联选择变化
const handleAnimalCascadeChange = (value: number[]) => {
  console.log('动物级联选择变化:', value)
  if (value && value.length === 2) {
    formData.animal_breed_id = value[1] // 品种ID
    console.log('设置动物品种ID:', formData.animal_breed_id)
  } else {
    formData.animal_breed_id = undefined
  }
}

// 图片上传成功回调
const handleImageUploadSuccess = (file: ImageItem) => {
  console.log('=== 图片上传成功 ===')
  console.log('上传的文件:', file)

  // 使用 nextTick 确保 imageFiles 已经更新
  nextTick(() => {
    updateImageFilesFormData()
  })
}

// 图片上传失败回调
const handleImageUploadError = (error: string) => {
  console.error('图片上传失败:', error)
}

// 图片删除回调
const handleImageRemove = (_file: ImageItem) => {
  // 使用 nextTick 确保 imageFiles 已经更新
  nextTick(() => {
    updateImageFilesFormData()
  })
}

// 更新表单中的影像文件数据
const updateImageFilesFormData = () => {
  console.log('=== updateImageFilesFormData 调用 ===')
  console.log('当前 imageFiles:', imageFiles.value)
  console.log('imageFiles 长度:', imageFiles.value.length)

  // 详细检查每个文件
  imageFiles.value.forEach((img, index) => {
    console.log(`文件 ${index}:`, {
      uid: img.uid,
      name: img.name,
      status: img.status,
      url: img.url,
      hasUrl: !!img.url,
      isSuccess: img.status === 'success',
    })
  })

  const urls = imageFiles.value
    .filter((img) => {
      const isValid = img.status === 'success' && img.url
      console.log(`文件 ${img.name} 是否有效:`, isValid, { status: img.status, url: img.url })
      return isValid
    })
    .map((img) => img.url)

  const newImageFiles = urls.join(',')
  console.log('提取的URLs:', urls)
  console.log('新的 image_files 值:', newImageFiles)
  console.log('当前 formData.image_files:', formData.image_files)
  console.log('formData.image_files 类型:', typeof formData.image_files)

  // 只有当值真正改变时才更新
  if (formData.image_files !== newImageFiles) {
    formData.image_files = newImageFiles
    console.log('✅ 已更新 formData.image_files:', formData.image_files)
  } else {
    console.log('⚠️ 值未改变，跳过更新')
  }
}

// 监听表单数据变化，向上传递
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

// 将字符串格式的图片路径转换为ImageItem数组
const parseImageFiles = (imageFilesString: string | string[] | null | undefined): ImageItem[] => {
  if (!imageFilesString) return []

  let urls: string[] = []

  // 如果已经是数组，直接使用
  if (Array.isArray(imageFilesString)) {
    urls = imageFilesString.filter((url) => url && url.trim()).map((url) => url.trim())
  }
  // 如果是字符串，按逗号分割
  else if (typeof imageFilesString === 'string') {
    urls = imageFilesString
      .split(',')
      .filter((url) => url.trim())
      .map((url) => url.trim())
  }

  return urls.map((url, index) => ({
    uid: `existing_${Date.now()}_${index}`,
    name: url.split('/').pop() || `image_${index + 1}`,
    url: url,
    cos_key: '', // 已存在的文件，cos_key为空
    status: 'success' as const,
  }))
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 先保存 image_files 的值
      const imageFilesValue = newValue.image_files

      // 复制其他属性
      Object.assign(formData, newValue)

      // 确保 image_files 是字符串类型
      if (Array.isArray(imageFilesValue)) {
        formData.image_files = imageFilesValue.join(',')
      } else if (typeof imageFilesValue === 'string') {
        formData.image_files = imageFilesValue
      } else {
        formData.image_files = ''
      }

      // 初始化图片文件列表
      if (imageFilesValue) {
        imageFiles.value = parseImageFiles(imageFilesValue)
      }
    }
  },
  { immediate: true, deep: true },
)

onMounted(() => {
  loadDictData()
})
</script>

<style scoped>
.medical-imaging-form {
  width: 100%;
}
</style>
