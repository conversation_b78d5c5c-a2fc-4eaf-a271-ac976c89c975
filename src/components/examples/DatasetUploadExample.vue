<template>
  <div class="dataset-upload-example">
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>数据集图片上传示例</span>
        </div>
      </template>

      <div class="dataset-type-selector">
        <h3>选择数据集类型</h3>
        <el-radio-group v-model="selectedDatasetType" @change="handleDatasetTypeChange">
          <el-radio value="comprehensive_case">综合性病例数据集</el-radio>
          <el-radio value="medical_image">影像数据集</el-radio>
          <el-radio value="exam_question">考试数据集</el-radio>
        </el-radio-group>

        <div class="upload-path-info">
          <p><strong>当前上传路径：</strong>{{ currentUploadPath }}</p>
        </div>
      </div>

      <div class="upload-section">
        <h3>图片上传</h3>
        <el-upload
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-remove="handleRemove"
          :file-list="fileList"
          list-type="picture-card"
          accept="image/*"
          :limit="10"
          :on-exceed="handleExceed"
          :disabled="uploading"
        >
          <el-icon v-if="!uploading"><Plus /></el-icon>
          <div v-else class="uploading-text">
            <el-icon class="is-loading"><Loading /></el-icon>
            <div>上传中...</div>
          </div>
        </el-upload>

        <div class="upload-tip">
          支持 jpg/png/gif 等图片格式，单个文件不超过 10MB，最多上传 10 张
        </div>
      </div>

      <div class="uploaded-files-section" v-if="uploadedFiles.length > 0">
        <h3>已上传文件</h3>
        <el-table :data="uploadedFiles" style="width: 100%">
          <el-table-column prop="original_filename" label="文件名" />
          <el-table-column prop="size" label="大小" :formatter="formatSize" />
          <el-table-column prop="custom_path" label="存储路径" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" @click="viewFile(scope.row)">查看</el-button>
              <el-button size="small" type="danger" @click="deleteFile(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  DATASET_UPLOAD_PATHS,
  fileUploadApi,
  uploadDatasetImage,
  type DatasetType,
} from '@/api/fileUpload'
import { Loading, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'

// 响应式数据
const selectedDatasetType = ref<DatasetType>('comprehensive_case')
const uploading = ref(false)
const fileList = ref([])
const uploadedFiles = ref([])

// 上传配置
const uploadAction = ref('') // Element Plus 需要，但我们使用自定义上传
const uploadHeaders = ref({
  Authorization: `Bearer ${localStorage.getItem('token') || ''}`,
})

// 当前上传路径
const currentUploadPath = computed(() => {
  return DATASET_UPLOAD_PATHS[selectedDatasetType.value]
})

// 处理数据集类型变化
const handleDatasetTypeChange = (value: DatasetType) => {
  ElMessage.info(`切换到${getDatasetTypeName(value)}，上传路径：${DATASET_UPLOAD_PATHS[value]}`)
}

// 获取数据集类型名称
const getDatasetTypeName = (type: DatasetType) => {
  const names = {
    comprehensive_case: '综合性病例数据集',
    medical_image: '影像数据集',
    exam_question: '考试数据集',
  }
  return names[type]
}

// 上传前验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }

  // 开始自定义上传
  uploading.value = true
  uploadFile(file)
  return false // 阻止默认上传
}

// 自定义上传
const uploadFile = async (file: File) => {
  try {
    const response = await uploadDatasetImage(file, selectedDatasetType.value)

    if (response.success) {
      uploadedFiles.value.push(response.data)
      ElMessage.success(`图片上传成功到 ${response.data.custom_path}`)
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    uploading.value = false
  }
}

// 上传成功回调（Element Plus 需要）
const handleSuccess = () => {
  // 空实现，实际处理在 uploadFile 中
}

// 上传失败回调
const handleError = () => {
  ElMessage.error('上传失败')
  uploading.value = false
}

// 删除文件
const handleRemove = async (file: any) => {
  // 这里可以实现删除逻辑
  console.log('删除文件:', file)
}

// 超出数量限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传10张图片')
}

// 格式化文件大小
const formatSize = (row: any, column: any, cellValue: number) => {
  const size = cellValue / 1024
  return size > 1024 ? (size / 1024).toFixed(2) + ' MB' : size.toFixed(2) + ' KB'
}

// 查看文件
const viewFile = (file: any) => {
  window.open(file.url, '_blank')
}

// 删除文件
const deleteFile = async (file: any) => {
  try {
    await fileUploadApi.deleteFile(file.cos_key)
    uploadedFiles.value = uploadedFiles.value.filter((f) => f.cos_key !== file.cos_key)
    ElMessage.success('文件删除成功!')
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}
</script>

<style scoped>
.dataset-upload-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dataset-type-selector {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}

.dataset-type-selector h3 {
  margin-top: 0;
  color: #303133;
}

.upload-path-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #e1f3d8;
  border-radius: 4px;
}

.upload-path-info p {
  margin: 0;
  color: #67c23a;
  font-weight: 500;
}

.upload-section {
  margin: 30px 0;
}

.upload-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.uploading-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}

.uploading-text .el-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.uploaded-files-section {
  margin-top: 30px;
}

.uploaded-files-section h3 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
