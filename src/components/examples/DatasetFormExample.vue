<template>
  <div class="dataset-form-example">
    <h2>数据集表单示例</h2>
    <p>展示修改后的数据集表单功能</p>

    <div class="example-section">
      <h3>主要修改内容：</h3>
      <ul>
        <li>✅ 将"数据集标题"字段名称更新为"标题"</li>
        <li>✅ 将"数据集描述"字段改为非必填项</li>
        <li>✅ 将"标签"字段设置为必填项</li>
        <li>✅ 专科分类字段支持多选功能和文字模糊检索</li>
        <li>✅ 将"动物品类/品种"字段名称更新为"宠物种类/品种"</li>
        <li>✅ 将"性别"字段名称更新为"宠物性别"，选项为：公、母</li>
        <li>✅ 添加年龄字段（数值+单位）</li>
        <li>✅ 添加鉴别诊断字段</li>
        <li>✅ 修复光标在"诊断结果"输入框内按回车键的问题</li>
        <li>✅ 设置所有必填项的验证规则</li>
      </ul>
    </div>

    <div class="form-container">
      <DatasetForm
        :initial-data="initialData"
        :initial-type="1"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </div>

    <div class="result-section" v-if="submittedData">
      <h3>提交的数据：</h3>
      <pre>{{ JSON.stringify(submittedData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DatasetCreateRequest } from '@/types'
import { ref } from 'vue'
import DatasetForm from '../DatasetForm.vue'

const initialData = ref<Partial<DatasetCreateRequest>>({
  title: '',
  description: '',
  dataset_type: 1,
  tags: [],
  data: {
    specialty_category_id: undefined,
    department_disease_type_id: undefined,
    animal_category_id: undefined,
    animal_breed_id: undefined,
    gender_id: 1,
    age_value: undefined,
    age_unit_id: 1,
    weight: undefined,
    is_neutered: 2,
    chief_complaint: '',
    medical_history: '',
    physical_examination: '',
    laboratory_examination: '',
    lab_images: '',
    diagnosis_result: '',
    treatment_plan: '',
    medication_info: '',
    case_summary: '',
  },
})

const submittedData = ref<DatasetCreateRequest | null>(null)

const handleSubmit = (data: DatasetCreateRequest) => {
  console.log('提交数据:', data)
  submittedData.value = data
  alert('数据提交成功！请查看控制台和下方的数据展示。')
}

const handleCancel = () => {
  console.log('取消操作')
  alert('操作已取消')
}
</script>

<style scoped>
.dataset-form-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.example-section h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.example-section ul {
  list-style: none;
  padding: 0;
}

.example-section li {
  padding: 5px 0;
  color: #606266;
}

.form-container {
  margin: 20px 0;
}

.result-section {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

.result-section h3 {
  color: #0066cc;
  margin-bottom: 15px;
}

.result-section pre {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
