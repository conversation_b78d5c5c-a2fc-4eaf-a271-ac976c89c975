<template>
  <div class="form-validation-example">
    <h2>表单验证联动示例</h2>
    <p>演示父子组件之间的表单验证联动功能</p>

    <div class="example-section">
      <h3>功能说明：</h3>
      <ul>
        <li>✅ 主表单验证：标题、标签等必填项</li>
        <li>✅ 子表单验证：根据数据集类型验证对应的子表单</li>
        <li>✅ 综合性病例表单：专科分类、疾病类型、宠物信息等必填项</li>
        <li>✅ 影像数据表单：系统类型、影像类型等字段验证</li>
        <li>✅ 考试题目表单：至少一道题目，题目标题必填</li>
        <li>✅ 错误提示：具体指出哪个表单验证失败</li>
      </ul>
    </div>

    <div class="test-controls">
      <h3>测试控制：</h3>
      <el-radio-group v-model="selectedType" @change="handleTypeChange">
        <el-radio :label="1">综合性病例数据集</el-radio>
        <el-radio :label="2">影像数据集</el-radio>
        <el-radio :label="3">考试数据集</el-radio>
      </el-radio-group>
    </div>

    <div class="form-container">
      <DatasetForm
        :key="formKey"
        :initial-data="initialData"
        :initial-type="selectedType"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </div>

    <div class="result-section" v-if="submittedData">
      <h3>提交成功的数据：</h3>
      <el-alert
        title="表单验证通过！"
        type="success"
        :closable="false"
        show-icon
        style="margin-bottom: 16px"
      />
      <pre>{{ JSON.stringify(submittedData, null, 2) }}</pre>
    </div>

    <div class="validation-tips">
      <h3>验证测试提示：</h3>
      <el-alert
        title="测试建议"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>1. 尝试不填写标题直接提交，应该显示"请完善表单信息"</p>
          <p>2. 填写标题但不填写子表单必填项，应该显示具体的子表单错误信息</p>
          <p>3. 对于综合性病例，尝试不选择专科分类、疾病类型等必填项</p>
          <p>4. 对于考试数据集，尝试不添加题目或题目标题为空</p>
          <p>5. 所有必填项都填写完整后，应该能成功提交</p>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import DatasetForm from '@/components/DatasetForm.vue'
import type { DatasetCreateRequest, DatasetType } from '@/types'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const selectedType = ref<DatasetType>(1)
const formKey = ref(0)
const submittedData = ref<DatasetCreateRequest | null>(null)

const initialData = ref<Partial<DatasetCreateRequest>>({
  title: '',
  description: '这是一个测试数据集',
  tags: [],
  data: {},
})

const handleTypeChange = () => {
  // 重置表单
  submittedData.value = null
  formKey.value++
  
  // 根据类型设置不同的初始数据
  if (selectedType.value === 1) {
    initialData.value.data = {}
  } else if (selectedType.value === 2) {
    initialData.value.data = {
      system_type_id: undefined,
      image_category_id: undefined,
      detailed_category_id: undefined,
      animal_breed_id: undefined,
      image_signs: '',
      detailed_description: '',
      diagnosis_suggestion: '',
      image_files: '',
    }
  } else if (selectedType.value === 3) {
    initialData.value.data = {
      questions: [],
      dataset_metadata: {
        total_questions: 0,
        description: '',
        tags: [],
      },
    }
  }
}

const handleSubmit = (data: DatasetCreateRequest) => {
  console.log('✅ 表单验证通过，提交数据:', data)
  submittedData.value = data
  ElMessage.success('表单提交成功！所有验证都通过了。')
}

const handleCancel = () => {
  console.log('❌ 用户取消操作')
  ElMessage.info('操作已取消')
}

// 初始化
handleTypeChange()
</script>

<style scoped>
.form-validation-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.example-section h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.example-section ul {
  list-style: none;
  padding: 0;
}

.example-section li {
  padding: 5px 0;
  color: #606266;
}

.test-controls {
  background: #fff9e6;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #ffd666;
}

.test-controls h3 {
  color: #e6a23c;
  margin-bottom: 15px;
}

.form-container {
  margin: 20px 0;
}

.result-section {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #67c23a;
}

.result-section h3 {
  color: #67c23a;
  margin-bottom: 15px;
}

.result-section pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
}

.validation-tips {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #409eff;
}

.validation-tips h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.validation-tips p {
  margin: 8px 0;
  line-height: 1.5;
}
</style>
