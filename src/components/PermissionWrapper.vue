<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="showFallback">
    <slot name="fallback">
      <div class="permission-denied">
        <el-empty 
          description="您没有权限访问此内容"
          :image-size="80"
        />
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermissions } from '@/composables/usePermissions'
import type { PermissionKey } from '@/types'

interface Props {
  // 需要的权限（单个或多个）
  permission?: PermissionKey | PermissionKey[]
  // 资源和操作（用于动态权限检查）
  resource?: string
  action?: string
  // 权限检查模式：'any' 表示有任意一个权限即可，'all' 表示需要所有权限
  mode?: 'any' | 'all'
  // 是否显示无权限时的占位内容
  showFallback?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'any',
  showFallback: false
})

const { hasPermission, hasAnyPermission, hasAllPermissions, hasResourcePermission } = usePermissions()

// 计算是否有访问权限
const hasAccess = computed(() => {
  // 如果指定了资源和操作，使用资源权限检查
  if (props.resource && props.action) {
    return hasResourcePermission(props.resource, props.action)
  }
  
  // 如果没有指定权限，默认允许访问
  if (!props.permission) {
    return true
  }
  
  // 处理权限数组
  const permissions = Array.isArray(props.permission) ? props.permission : [props.permission]
  
  // 根据模式检查权限
  if (props.mode === 'all') {
    return hasAllPermissions(permissions)
  } else {
    return hasAnyPermission(permissions)
  }
})
</script>

<style scoped>
.permission-denied {
  padding: 20px;
  text-align: center;
  color: #909399;
}
</style>
