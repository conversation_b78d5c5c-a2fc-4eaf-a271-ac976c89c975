<template>
  <el-button
    v-if="hasAccess"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot />
  </el-button>
  <el-tooltip
    v-else-if="showTooltip"
    content="您没有权限执行此操作"
    placement="top"
  >
    <el-button
      v-bind="$attrs"
      disabled
      @click.prevent
    >
      <slot />
    </el-button>
  </el-tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermissions } from '@/composables/usePermissions'
import type { PermissionKey } from '@/types'

interface Props {
  // 需要的权限（单个或多个）
  permission?: PermissionKey | PermissionKey[]
  // 资源和操作（用于动态权限检查）
  resource?: string
  action?: string
  // 权限检查模式：'any' 表示有任意一个权限即可，'all' 表示需要所有权限
  mode?: 'any' | 'all'
  // 无权限时是否显示禁用状态的按钮（带提示）
  showTooltip?: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'any',
  showTooltip: false
})

const emit = defineEmits<Emits>()

const { hasPermission, hasAnyPermission, hasAllPermissions, hasResourcePermission } = usePermissions()

// 计算是否有访问权限
const hasAccess = computed(() => {
  // 如果指定了资源和操作，使用资源权限检查
  if (props.resource && props.action) {
    return hasResourcePermission(props.resource, props.action)
  }
  
  // 如果没有指定权限，默认允许访问
  if (!props.permission) {
    return true
  }
  
  // 处理权限数组
  const permissions = Array.isArray(props.permission) ? props.permission : [props.permission]
  
  // 根据模式检查权限
  if (props.mode === 'all') {
    return hasAllPermissions(permissions)
  } else {
    return hasAnyPermission(permissions)
  }
})

const handleClick = (event: MouseEvent) => {
  if (hasAccess.value) {
    emit('click', event)
  }
}
</script>

<style scoped>
/* 继承父组件的样式 */
</style>
