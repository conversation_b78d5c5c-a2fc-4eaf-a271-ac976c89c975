<template>
  <div class="dataset-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
    >
      <!-- 基础信息 -->
      <div class="form-section">
        <h3 class="section-title">基础信息</h3>

        <el-form-item label="标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="formData.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option v-for="tag in commonTags" :key="tag" :label="tag" :value="tag" />
          </el-select>
        </el-form-item>
      </div>

      <!-- 数据内容 -->
      <div class="form-section">
        <h3 class="section-title">数据内容</h3>

        <!-- 综合性病例数据 -->
        <template v-if="formData.dataset_type === 1">
          <ComprehensiveCaseForm
            ref="comprehensiveCaseFormRef"
            v-model="formData.data"
            :dataset-type="'comprehensive_case'"
          />
        </template>

        <!-- 影像数据 -->
        <template v-if="formData.dataset_type === 2">
          <MedicalImagingForm
            ref="medicalImagingFormRef"
            v-model="formData.data"
            :dataset-type="'medical_image'"
          />
        </template>

        <!-- 考试题目数据 -->
        <template v-if="formData.dataset_type === 3">
          <ExamQuestionForm
            ref="examQuestionFormRef"
            v-model="formData.data as any"
            :dataset-type="'exam_question'"
          />
        </template>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { DatasetCreateRequest, DatasetData, DatasetType } from '@/types'
import { message } from '@/utils/message'
import { type FormInstance, type FormRules } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import ComprehensiveCaseForm from './forms/ComprehensiveCaseForm.vue'
import ExamQuestionForm from './forms/ExamQuestionForm.vue'
import MedicalImagingForm from './forms/MedicalImagingForm.vue'

interface Props {
  initialData?: Partial<DatasetCreateRequest>
  initialType?: DatasetType
  isEdit?: boolean
}

interface Emits {
  (e: 'submit', data: DatasetCreateRequest): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()
const comprehensiveCaseFormRef = ref()
const medicalImagingFormRef = ref()
const examQuestionFormRef = ref()

// 表单数据
const formData = reactive<DatasetCreateRequest>({
  title: '',
  description: '',
  dataset_type: 1,
  tags: [],
  data: {} as DatasetData,
})

// 常用标签
const commonTags = ref([
  '犬',
  '猫',
  '内科',
  '外科',
  '影像',
  '诊断',
  '治疗',
  '预防',
  'X光',
  'CT',
  'MRI',
  '超声',
  '病理',
  '血液',
  '生化',
  '免疫',
])

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度应在2-100个字符之间', trigger: 'blur' },
  ],
  tags: [{ required: true, message: '请选择或输入标签', trigger: 'change' }],
}

// 方法

const handleCancel = () => {
  emit('cancel')
}

const handleSubmit = async () => {
  try {
    // 验证主表单
    await formRef.value?.validate()

    // 根据数据集类型验证对应的子表单
    let childFormValid = true
    let childFormErrorMessage = '请完善表单信息'

    if (formData.dataset_type === 1) {
      // 验证综合性病例表单
      if (comprehensiveCaseFormRef.value) {
        childFormValid = await comprehensiveCaseFormRef.value.validate()
        if (!childFormValid) {
          childFormErrorMessage = '请完善综合性病例表单信息'
        }
      }
    } else if (formData.dataset_type === 2) {
      // 验证影像数据表单
      if (medicalImagingFormRef.value && medicalImagingFormRef.value.validate) {
        childFormValid = await medicalImagingFormRef.value.validate()
        if (!childFormValid) {
          childFormErrorMessage = '请完善影像数据表单信息'
        }
      }
      console.log('影像数据集 image_files:', (formData.data as any)?.image_files)
    } else if (formData.dataset_type === 3) {
      // 验证考试题目表单
      if (examQuestionFormRef.value && examQuestionFormRef.value.validate) {
        childFormValid = await examQuestionFormRef.value.validate()
        if (!childFormValid) {
          childFormErrorMessage = '请完善考试题目表单信息'
        }
      }
    }

    // 如果子表单验证失败，抛出错误
    if (!childFormValid) {
      throw new Error(childFormErrorMessage)
    }

    // 所有验证通过，提交表单
    emit('submit', { ...formData })
  } catch (error) {
    // 显示错误信息
    const errorMessage = error instanceof Error ? error.message : '请完善表单信息'
    message.error(errorMessage)
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.initialData) {
    Object.assign(formData, props.initialData)
  }

  if (props.initialType) {
    formData.dataset_type = props.initialType
  }

  // 确保考试题目类型有questions数组
  if (formData.dataset_type === 3 && (!formData.data || !(formData.data as any).questions)) {
    formData.data = { questions: [] } as DatasetData
  }
}

// 监听初始数据变化
watch(() => props.initialData, initFormData, { immediate: true, deep: true })

onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.dataset-form {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
  padding: 24px;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #4285f4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 0;
  border-top: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-form {
    max-width: 100%;
    padding: 16px;
  }

  .form-section {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .el-button {
    width: 100%;
  }
}
</style>
