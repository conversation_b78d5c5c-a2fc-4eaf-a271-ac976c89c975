<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据集审核"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="review-modal"
  >
    <div v-if="dataset" class="review-content">
      <!-- 数据集基本信息 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>数据集标题：</label>
            <span>{{ dataset.title }}</span>
          </div>
          <div class="info-item">
            <label>数据集类型：</label>
            <el-tag :type="getTypeTagType(dataset.dataset_type)">
              {{ getTypeLabel(dataset.dataset_type) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(dataset.created_at) }}</span>
          </div>
          <div class="info-item">
            <label>更新时间：</label>
            <span>{{ formatDate(dataset.updated_at) }}</span>
          </div>
        </div>

        <div class="info-item full-width">
          <label>数据集描述：</label>
          <p class="description">{{ dataset.description }}</p>
        </div>

        <div class="info-item full-width" v-if="dataset.tags && dataset.tags.length">
          <label>标签：</label>
          <div class="tags">
            <el-tag v-for="tag in dataset.tags" :key="tag" size="small">
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 数据内容详情 -->
      <div class="data-section">
        <h3 class="section-title">数据内容</h3>

        <!-- 综合性病例数据 -->
        <template v-if="dataset.dataset_type === 1">
          <div class="data-detail">
            <div class="detail-grid">
              <div class="detail-item">
                <label>动物类型ID：</label>
                <span>{{ dataset.data.animal_type_id }}</span>
              </div>
              <div class="detail-item">
                <label>年龄（月）：</label>
                <span>{{ dataset.data.age_months }}</span>
              </div>
              <div class="detail-item" v-if="dataset.data.weight_kg">
                <label>体重（kg）：</label>
                <span>{{ dataset.data.weight_kg }}</span>
              </div>
            </div>
            <div class="detail-item full-width">
              <label>症状描述：</label>
              <p>{{ dataset.data.symptoms }}</p>
            </div>
            <div class="detail-item full-width">
              <label>诊断结果：</label>
              <p>{{ dataset.data.diagnosis }}</p>
            </div>
            <div class="detail-item full-width">
              <label>治疗方案：</label>
              <p>{{ dataset.data.treatment }}</p>
            </div>
            <div class="detail-item full-width" v-if="dataset.data.outcome">
              <label>治疗结果：</label>
              <p>{{ dataset.data.outcome }}</p>
            </div>
          </div>
        </template>

        <!-- 影像数据 -->
        <template v-if="dataset.dataset_type === 2">
          <div class="data-detail">
            <div class="detail-grid">
              <div class="detail-item">
                <label>动物类型ID：</label>
                <span>{{ dataset.data.animal_type_id }}</span>
              </div>
              <div class="detail-item">
                <label>影像类型ID：</label>
                <span>{{ dataset.data.image_type_id }}</span>
              </div>
              <div class="detail-item">
                <label>影像细节ID：</label>
                <span>{{ dataset.data.image_detail_id }}</span>
              </div>
              <div class="detail-item">
                <label>检查部位：</label>
                <span>{{ dataset.data.body_part }}</span>
              </div>
            </div>
            <div class="detail-item full-width">
              <label>影像描述：</label>
              <p>{{ dataset.data.image_description }}</p>
            </div>
            <div class="detail-item full-width">
              <label>影像发现：</label>
              <p>{{ dataset.data.findings }}</p>
            </div>
            <div class="detail-item full-width" v-if="dataset.data.image_url">
              <label>影像预览：</label>
              <div class="image-preview">
                <el-image
                  :src="dataset.data.image_url"
                  fit="contain"
                  style="width: 300px; height: 200px"
                  :preview-src-list="[dataset.data.image_url]"
                />
              </div>
            </div>
          </div>
        </template>

        <!-- 考试题目数据 -->
        <template v-if="dataset.dataset_type === 3">
          <div class="data-detail">
            <div class="detail-grid">
              <div class="detail-item">
                <label>题目类型ID：</label>
                <span>{{ dataset.data.question_type_id }}</span>
              </div>
              <div class="detail-item">
                <label>学科ID：</label>
                <span>{{ dataset.data.subject_id }}</span>
              </div>
              <div class="detail-item">
                <label>难度等级ID：</label>
                <span>{{ dataset.data.difficulty_level_id }}</span>
              </div>
            </div>
            <div class="detail-item full-width">
              <label>题目内容：</label>
              <p>{{ dataset.data.question_text }}</p>
            </div>
            <div class="detail-item full-width">
              <label>选项：</label>
              <div class="options-list">
                <div v-for="(option, key) in dataset.data.options" :key="key" class="option-item">
                  <strong>{{ key }}.</strong> {{ option }}
                </div>
              </div>
            </div>
            <div class="detail-item full-width">
              <label>正确答案：</label>
              <div class="correct-answer">
                <el-tag type="success">{{ dataset.data.correct_answer.answer }}</el-tag>
                <span class="answer-type"
                  >（{{ dataset.data.correct_answer.type === 'single' ? '单选' : '多选' }}）</span
                >
              </div>
            </div>
            <div class="detail-item full-width">
              <label>答案解析：</label>
              <p>{{ dataset.data.explanation }}</p>
            </div>
          </div>
        </template>
      </div>

      <!-- 审核操作 -->
      <div class="review-section">
        <h3 class="section-title">审核操作</h3>

        <el-form ref="reviewFormRef" :model="reviewForm" :rules="reviewRules" label-width="100px">
          <el-form-item label="审核结果" prop="review_status">
            <el-radio-group v-model="reviewForm.review_status">
              <el-radio :label="1" size="large">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
                通过
              </el-radio>
              <el-radio :label="2" size="large">
                <el-icon color="#f56c6c"><CircleClose /></el-icon>
                拒绝
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审核意见" prop="review_comment">
            <el-input
              v-model="reviewForm.review_comment"
              type="textarea"
              :rows="3"
              placeholder="请输入审核意见（可选）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item v-if="reviewForm.review_status === 2" label="拒绝原因" prop="review_reason">
            <el-input
              v-model="reviewForm.review_reason"
              type="textarea"
              :rows="3"
              placeholder="请详细说明拒绝原因（必填）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmitReview" :loading="submitting">
          提交审核
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reviewApi } from '@/api'
import type { Dataset, DatasetType, ReviewRequest } from '@/types'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'

interface Props {
  visible: boolean
  dataset: Dataset | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'review-submitted'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const reviewFormRef = ref<FormInstance>()

// 响应式数据
const submitting = ref(false)

// 审核表单
const reviewForm = reactive<ReviewRequest>({
  review_status: 1,
  review_comment: '',
  review_reason: '',
})

// 表单验证规则
const reviewRules: FormRules = {
  review_status: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  review_reason: [
    {
      validator: (rule, value, callback) => {
        if (reviewForm.review_status === 2 && !value) {
          callback(new Error('拒绝时必须填写拒绝原因'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 方法
const getTypeLabel = (type: DatasetType) => {
  const labels: Record<DatasetType, string> = {
    [1]: '综合性病例',
    [2]: '影像数据集',
    [3]: '考试题目',
  }
  return labels[type] || '未知类型'
}

const getTypeTagType = (type: DatasetType) => {
  const types: Record<DatasetType, string> = {
    [1]: 'primary',
    [2]: 'success',
    [3]: 'warning',
  }
  return types[type] || ''
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleString()
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const handleSubmitReview = async () => {
  if (!props.dataset) return

  try {
    await reviewFormRef.value?.validate()

    submitting.value = true

    await reviewApi.reviewDataset(props.dataset.id, reviewForm)

    ElMessage.success('审核提交成功')
    emit('review-submitted')
    resetForm()
  } catch (error: any) {
    if (error !== 'validation failed') {
      console.error('提交审核失败:', error)
      ElMessage.error('提交审核失败')
    }
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  reviewForm.review_status = 1
  reviewForm.review_comment = ''
  reviewForm.review_reason = ''
  reviewFormRef.value?.clearValidate()
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm()
    }
  },
)
</script>

<style scoped>
.review-modal :deep(.el-dialog) {
  max-width: 1200px;
}

.review-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-section,
.data-section,
.review-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #4285f4;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
  flex-shrink: 0;
}

.description {
  margin: 8px 0 0 0;
  line-height: 1.5;
  color: #6b7280;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.data-detail {
  width: 100%;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
  flex-shrink: 0;
}

.detail-item p {
  margin: 8px 0 0 0;
  line-height: 1.5;
  color: #6b7280;
}

.image-preview {
  margin-top: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  background: white;
}

.options-list {
  margin-top: 8px;
}

.option-item {
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.option-item:last-child {
  border-bottom: none;
}

.correct-answer {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.answer-type {
  color: #6b7280;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .review-modal :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .review-content {
    max-height: 60vh;
  }
}
</style>
