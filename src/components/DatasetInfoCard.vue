<template>
  <div class="dataset-info-card">
    <div class="card-header">
      <h3 class="card-title">
        <el-icon><Document /></el-icon>
        数据集详情信息
      </h3>
      <el-tag :type="getStatusType(dataset.dataset_status)" class="status-tag">
        {{ dataset.status_name || getStatusName(dataset.dataset_status) }}
      </el-tag>
    </div>

    <div class="card-content">
      <!-- 基础信息 -->
      <div class="info-section">
        <h4 class="section-title">基础信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">数据集ID:</span>
            <span class="value">{{ dataset.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">数据集类型:</span>
            <span class="value">{{
              dataset.dataset_type_name || getTypeName(dataset.dataset_type)
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建者ID:</span>
            <span class="value">{{ dataset.creator_id }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(dataset.created_at) }}</span>
          </div>
          <div class="info-item">
            <span class="label">更新时间:</span>
            <span class="value">{{ formatDate(dataset.updated_at) }}</span>
          </div>
          <div class="info-item" v-if="dataset.tags && dataset.tags.length > 0">
            <span class="label">标签:</span>
            <div class="tags-container">
              <el-tag v-for="tag in dataset.tags" :key="tag" size="small" class="tag-item">
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 综合性病例详细信息 -->
      <div v-if="dataset.dataset_type === 1 && dataset.data" class="info-section">
        <h4 class="section-title">病例详细信息</h4>
        <div class="case-details">
          <div class="detail-row" v-if="(dataset.data as any).specialty_category_id">
            <span class="label">专科分类ID:</span>
            <span class="value">{{ (dataset.data as any).specialty_category_id }}</span>
          </div>
          <div class="detail-row" v-if="(dataset.data as any).animal_category_id">
            <span class="label">动物品类ID:</span>
            <span class="value">{{ (dataset.data as any).animal_category_id }}</span>
          </div>
          <div class="detail-row" v-if="(dataset.data as any).animal_breed_id">
            <span class="label">动物品种ID:</span>
            <span class="value">{{ (dataset.data as any).animal_breed_id }}</span>
          </div>
          <div class="detail-row" v-if="(dataset.data as any).gender_id">
            <span class="label">性别ID:</span>
            <span class="value">{{ (dataset.data as any).gender_id }}</span>
          </div>
          <div class="detail-row" v-if="(dataset.data as any).age_value">
            <span class="label">年龄:</span>
            <span class="value"
              >{{ (dataset.data as any).age_value }} (单位ID:
              {{ (dataset.data as any).age_unit_id }})</span
            >
          </div>
          <div class="detail-row" v-if="(dataset.data as any).weight">
            <span class="label">体重:</span>
            <span class="value">{{ (dataset.data as any).weight }} kg</span>
          </div>
          <div class="detail-row" v-if="(dataset.data as any).is_neutered !== null">
            <span class="label">是否绝育:</span>
            <span class="value">{{ (dataset.data as any).is_neutered ? '是' : '否' }}</span>
          </div>
          <div
            class="detail-row"
            v-if="(dataset.data as any).case_labels && (dataset.data as any).case_labels.length > 0"
          >
            <span class="label">病例标签:</span>
            <div class="labels-container">
              <el-tag
                v-for="label in (dataset.data as any).case_labels"
                :key="label"
                size="small"
                type="info"
                class="label-item"
              >
                {{ label }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 病例内容 -->
        <div class="case-content">
          <div class="content-item" v-if="(dataset.data as any).chief_complaint">
            <h5 class="content-title">主诉:</h5>
            <p class="content-text">{{ (dataset.data as any).chief_complaint }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).medical_history">
            <h5 class="content-title">病史:</h5>
            <p class="content-text">{{ (dataset.data as any).medical_history }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).physical_examination">
            <h5 class="content-title">体格检查:</h5>
            <p class="content-text">{{ (dataset.data as any).physical_examination }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).laboratory_examination">
            <h5 class="content-title">实验室检查:</h5>
            <p class="content-text">{{ (dataset.data as any).laboratory_examination }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).diagnosis_result">
            <h5 class="content-title">诊断结果:</h5>
            <p class="content-text">{{ (dataset.data as any).diagnosis_result }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).treatment_plan">
            <h5 class="content-title">治疗方案:</h5>
            <p class="content-text">{{ (dataset.data as any).treatment_plan }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).medication_info">
            <h5 class="content-title">用药信息:</h5>
            <p class="content-text">{{ (dataset.data as any).medication_info }}</p>
          </div>
          <div class="content-item" v-if="(dataset.data as any).case_summary">
            <h5 class="content-title">病例总结:</h5>
            <p class="content-text">{{ (dataset.data as any).case_summary }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Dataset, DatasetStatus, DatasetType } from '@/types'
import { Document } from '@element-plus/icons-vue'

interface Props {
  dataset: Dataset
}

defineProps<Props>()

// 获取状态类型
const getStatusType = (status: DatasetStatus) => {
  switch (status) {
    case 1:
      return 'info' // 草稿
    case 2:
      return 'warning' // 待审核
    case 3:
      return 'success' // 已通过
    case 4:
      return 'danger' // 已拒绝
    case 5:
      return 'info' // 已归档
    default:
      return 'info'
  }
}

// 获取状态名称
const getStatusName = (status: DatasetStatus) => {
  switch (status) {
    case 1:
      return '草稿'
    case 2:
      return '待审核'
    case 3:
      return '已通过'
    case 4:
      return '已拒绝'
    case 5:
      return '已归档'
    default:
      return '未知'
  }
}

// 获取类型名称
const getTypeName = (type: DatasetType) => {
  switch (type) {
    case 1:
      return '综合性病例数据集'
    case 2:
      return '影像数据集'
    case 3:
      return '考试数据集'
    default:
      return '未知类型'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.dataset-info-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.status-tag {
  font-weight: 500;
}

.info-section {
  margin-bottom: 32px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #4285f4;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #374151;
  word-break: break-all;
}

.tags-container,
.labels-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item,
.label-item {
  margin: 0;
}

.case-details {
  margin-bottom: 24px;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.case-content {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
}

.content-item {
  margin-bottom: 20px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #4b5563;
}

.content-text {
  margin: 0;
  color: #374151;
  line-height: 1.6;
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
