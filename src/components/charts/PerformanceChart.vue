<template>
  <div class="chart-container">
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <v-chart
      v-else
      ref="chartRef"
      :option="chartOption"
      :loading="loading"
      class="chart"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'
import { Loading } from '@element-plus/icons-vue'

// 注册 ECharts 组件
use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

interface Props {
  data?: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: null,
  loading: false,
})

const chartRef = ref()

// 默认数据
const defaultData = {
  dates: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'],
  models: [
    {
      name: 'GPT-4',
      scores: [85, 87, 89, 91, 93, 95],
    },
    {
      name: 'Claude-3',
      scores: [82, 84, 86, 88, 90, 92],
    },
    {
      name: 'Gemini Pro',
      scores: [80, 82, 84, 86, 88, 90],
    },
  ],
}

const chartOption = computed(() => {
  const data = props.data || defaultData

  return {
    title: {
      text: '模型性能趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`
        })
        return result
      },
    },
    legend: {
      data: data.models.map((model: any) => model.name),
      top: 30,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.dates,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      axisLabel: {
        color: '#666',
      },
    },
    yAxis: {
      type: 'value',
      min: 70,
      max: 100,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      axisLabel: {
        color: '#666',
        formatter: '{value}%',
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
    },
    series: data.models.map((model: any, index: number) => ({
      name: model.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
      },
      itemStyle: {
        color: getColor(index),
      },
      areaStyle: {
        opacity: 0.1,
        color: getColor(index),
      },
      data: model.scores,
    })),
  }
})

function getColor(index: number): string {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#c45656']
  return colors[index % colors.length]
}

// 监听数据变化，重新渲染图表
watch(
  () => props.data,
  () => {
    if (chartRef.value) {
      chartRef.value.resize()
    }
  },
  { deep: true },
)
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.chart-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
