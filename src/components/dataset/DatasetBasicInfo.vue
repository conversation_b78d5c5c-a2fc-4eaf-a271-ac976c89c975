<template>
  <div class="dataset-basic-info">
    <h3 class="section-title">基本信息</h3>

    <div class="info-container">
      <div class="info-grid">
        <div class="info-item">
          <label>数据集标题：</label>
          <span class="info-value">{{ dataset.title }}</span>
        </div>

        <div class="info-item">
          <label>数据集类型：</label>
          <el-tag :type="getTypeTagType(dataset.dataset_type)" size="large">
            {{ getTypeLabel(dataset.dataset_type) }}
          </el-tag>
        </div>

        <div class="info-item">
          <label>状态：</label>
          <el-tag :type="getStatusTagType(dataset.dataset_status)" size="large">
            {{ getStatusLabel(dataset.dataset_status) }}
          </el-tag>
        </div>

        <div class="info-item">
          <label>创建时间：</label>
          <span class="info-value">{{ formatDate(dataset.created_at) }}</span>
        </div>

        <div class="info-item">
          <label>更新时间：</label>
          <span class="info-value">{{ formatDate(dataset.updated_at) }}</span>
        </div>

        <div class="info-item" v-if="(dataset as any).creator_name">
          <label>创建者：</label>
          <span class="info-value">{{ (dataset as any).creator_name }}</span>
        </div>
      </div>

      <!-- 描述信息 -->
      <div class="info-item full-width" v-if="dataset.description">
        <label>数据集描述：</label>
        <div class="description-content">
          <p class="description-text">{{ dataset.description }}</p>
        </div>
      </div>

      <!-- 标签 -->
      <div class="info-item full-width" v-if="dataset.tags && dataset.tags.length">
        <label>标签：</label>
        <div class="tags-container">
          <el-tag v-for="tag in dataset.tags" :key="tag" size="small" class="tag-item">
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Dataset, DatasetStatus, DatasetType } from '@/types'
import { formatDate } from '@/utils'

interface Props {
  dataset: Dataset
}

defineProps<Props>()

// 获取类型标签样式
const getTypeTagType = (type: DatasetType) => {
  const typeMap = {
    1: 'primary', // 综合性病例
    2: 'success', // 影像数据
    3: 'warning', // 考试题目
  }
  return typeMap[type] || 'info'
}

// 获取类型标签文本
const getTypeLabel = (type: DatasetType) => {
  const typeMap = {
    1: '综合性病例',
    2: '影像数据',
    3: '考试题目',
  }
  return typeMap[type] || '未知类型'
}

// 获取状态标签样式
const getStatusTagType = (status: DatasetStatus) => {
  const statusMap = {
    1: 'info', // 草稿
    2: 'warning', // 待审核
    3: 'success', // 已发布
    4: 'danger', // 已拒绝
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: DatasetStatus) => {
  const statusMap = {
    1: '草稿',
    2: '待审核',
    3: '已发布',
    4: '已拒绝',
  }
  return statusMap[status] || '未知状态'
}
</script>

<style scoped>
.dataset-basic-info {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto 24px auto;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #4285f4;
}

.info-container {
  border-radius: 8px;
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  margin-bottom: 16px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
}

.description-content {
  margin-top: 8px;
}

.description-text {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag-item {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-basic-info {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .info-container {
    padding: 16px;
  }

  .description-text {
    padding: 10px;
    font-size: 13px;
  }
}
</style>
