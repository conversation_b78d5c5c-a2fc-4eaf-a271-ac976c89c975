<template>
  <div class="comprehensive-case-display">
    <div class="data-detail">
      <!-- 基本信息网格 -->
      <div class="detail-grid">
        <div class="detail-item" v-if="data.animal_category_id || data.animal_type_id">
          <label>动物类型ID：</label>
          <span>{{ data.animal_category_id || data.animal_type_id }}</span>
        </div>

        <div class="detail-item" v-if="data.animal_breed_id">
          <label>动物品种ID：</label>
          <span>{{ data.animal_breed_id }}</span>
        </div>

        <div class="detail-item" v-if="data.age_months || data.age_value">
          <label>年龄：</label>
          <span>{{ data.age_months || data.age_value }}{{ data.age_months ? '月' : '' }}</span>
        </div>

        <div class="detail-item" v-if="data.weight_kg || data.weight">
          <label>体重（kg）：</label>
          <span>{{ data.weight_kg || data.weight }}</span>
        </div>

        <div class="detail-item" v-if="(data as any).gender">
          <label>性别：</label>
          <span>{{ (data as any).gender }}</span>
        </div>

        <div class="detail-item" v-if="(data as any).breed">
          <label>品种：</label>
          <span>{{ (data as any).breed }}</span>
        </div>
      </div>

      <!-- 病史信息 -->
      <div class="detail-item full-width" v-if="data.medical_history">
        <label>病史：</label>
        <p class="content-text">{{ data.medical_history }}</p>
      </div>

      <!-- 临床症状 -->
      <div class="detail-item full-width" v-if="(data as any).clinical_symptoms">
        <label>临床症状：</label>
        <p class="content-text">{{ (data as any).clinical_symptoms }}</p>
      </div>

      <!-- 体格检查 -->
      <div class="detail-item full-width" v-if="data.physical_examination">
        <label>体格检查：</label>
        <p class="content-text">{{ data.physical_examination }}</p>
      </div>

      <!-- 实验室检查 -->
      <div class="detail-item full-width" v-if="(data as any).laboratory_tests">
        <label>实验室检查：</label>
        <p class="content-text">{{ (data as any).laboratory_tests }}</p>
      </div>

      <!-- 诊断结果 -->
      <div class="detail-item full-width" v-if="data.diagnosis">
        <label>诊断结果：</label>
        <p class="content-text diagnosis">{{ data.diagnosis }}</p>
      </div>

      <!-- 治疗方案 -->
      <div class="detail-item full-width" v-if="data.treatment_plan">
        <label>治疗方案：</label>
        <p class="content-text">{{ data.treatment_plan }}</p>
      </div>

      <!-- 预后评估 */
      <div class="detail-item full-width" v-if="data.prognosis">
        <label>预后评估：</label>
        <p class="content-text">{{ data.prognosis }}</p>
      </div>

      <!-- 化验单图片 -->
      <div class="detail-item full-width" v-if="data.lab_images">
        <label>化验单图片：</label>
        <ImageGallery :images="parseImageUrls(data.lab_images)" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ComprehensiveCaseData } from '@/types'
import ImageGallery from './ImageGallery.vue'

interface Props {
  data: ComprehensiveCaseData
  datasetType?: number
}

defineProps<Props>()

// 解析图片URL字符串为数组
const parseImageUrls = (imageString: string | string[] | null | undefined): string[] => {
  if (!imageString) return []

  // 如果已经是数组，直接返回
  if (Array.isArray(imageString)) {
    return imageString.filter((url) => url && url.trim()).map((url) => url.trim())
  }

  // 如果是字符串，按逗号分割
  if (typeof imageString === 'string') {
    return imageString
      .split(',')
      .filter((url) => url.trim())
      .map((url) => url.trim())
  }

  return []
}
</script>

<style scoped>
.comprehensive-case-display {
  width: 100%;
}

.data-detail {
  border-radius: 8px;
  padding: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  max-width: 900px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  margin-bottom: 16px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.detail-item span {
  color: #303133;
  font-size: 14px;
}

.content-text {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.content-text.diagnosis {
  background: #f0f9ff;
  border-color: #409eff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .data-detail {
    padding: 16px;
  }

  .content-text {
    padding: 10px;
    font-size: 13px;
  }
}
</style>
