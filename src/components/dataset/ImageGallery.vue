<template>
  <div class="image-gallery">
    <div v-if="images.length === 0" class="no-images">
      <el-empty description="暂无图片" :image-size="80" />
    </div>

    <div v-else class="gallery-grid">
      <div v-for="(imageUrl, index) in images" :key="index" class="image-item">
        <el-image
          :src="imageUrl"
          fit="cover"
          class="gallery-image"
          :preview-src-list="images"
          :initial-index="index"
          :lazy="true"
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>加载失败</span>
            </div>
          </template>

          <template #placeholder>
            <div class="image-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </template>
        </el-image>

        <!-- 图片序号 -->
        <div class="image-index">{{ index + 1 }}</div>

        <!-- 图片操作按钮 -->
        <div class="image-actions">
          <el-button type="primary" size="small" circle @click="previewImage(index)">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 图片统计信息 -->
    <div v-if="images.length > 0" class="gallery-info">
      <span class="image-count">共 {{ images.length }} 张图片</span>
      <span class="gallery-tip">点击图片可预览大图</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Loading, Picture, ZoomIn } from '@element-plus/icons-vue'
import { ref } from 'vue'

interface Props {
  images: string[]
  maxHeight?: string
  columns?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxHeight: '400px',
  columns: 4,
})

const showViewer = ref(false)
const currentIndex = ref(0)

// 预览图片
const previewImage = (index: number) => {
  currentIndex.value = index
  showViewer.value = true
}
</script>

<style scoped>
.image-gallery {
  width: 100%;
}

.no-images {
  padding: 40px 0;
  text-align: center;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
  max-width: 800px;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-item:hover .image-actions {
  opacity: 1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #c0c4cc;
  background: #f5f7fa;
}

.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-error span {
  font-size: 12px;
}

.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  background: #f5f7fa;
}

.image-loading .el-icon {
  font-size: 20px;
  margin-bottom: 8px;
}

.image-loading span {
  font-size: 12px;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.image-index {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.image-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #e4e7ed;
  color: #909399;
  font-size: 14px;
}

.image-count {
  font-weight: 500;
  color: #606266;
}

.gallery-tip {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }

  .image-index {
    top: 4px;
    left: 4px;
    padding: 2px 6px;
    font-size: 10px;
  }

  .gallery-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
