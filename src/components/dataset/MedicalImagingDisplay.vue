<template>
  <div class="medical-imaging-display">
    <div class="data-detail">
      <!-- 基本信息网格 -->
      <div class="detail-grid">
        <div class="detail-item" v-if="data.system_type_id">
          <label>系统类型ID：</label>
          <span>{{ data.system_type_id }}</span>
        </div>

        <div class="detail-item" v-if="data.image_category_id">
          <label>影像分类ID：</label>
          <span>{{ data.image_category_id }}</span>
        </div>

        <div class="detail-item" v-if="data.detailed_category_id">
          <label>详细分类ID：</label>
          <span>{{ data.detailed_category_id }}</span>
        </div>

        <div class="detail-item" v-if="data.animal_breed_id">
          <label>动物品种ID：</label>
          <span>{{ data.animal_breed_id }}</span>
        </div>
      </div>

      <!-- 影像征象 -->
      <div class="detail-item full-width" v-if="data.image_signs">
        <label>影像征象：</label>
        <p class="content-text">{{ data.image_signs }}</p>
      </div>

      <!-- 详细描述 -->
      <div class="detail-item full-width" v-if="data.detailed_description">
        <label>详细描述：</label>
        <p class="content-text">{{ data.detailed_description }}</p>
      </div>

      <!-- 诊断建议 -->
      <div class="detail-item full-width" v-if="data.diagnosis_suggestion">
        <label>诊断建议：</label>
        <p class="content-text diagnosis">{{ data.diagnosis_suggestion }}</p>
      </div>

      <!-- 影像文件 -->
      <div class="detail-item full-width" v-if="data.image_files">
        <label>影像文件：</label>
        <ImageGallery :images="parseImageUrls(data.image_files)" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MedicalImagingData } from '@/types'
import ImageGallery from './ImageGallery.vue'

interface Props {
  data: MedicalImagingData
  datasetType?: number
}

defineProps<Props>()

// 解析图片URL字符串为数组
const parseImageUrls = (imageString: string | string[] | null | undefined): string[] => {
  if (!imageString) return []

  // 如果已经是数组，直接返回
  if (Array.isArray(imageString)) {
    return imageString.filter((url) => url && url.trim()).map((url) => url.trim())
  }

  // 如果是字符串，按逗号分割
  if (typeof imageString === 'string') {
    return imageString
      .split(',')
      .filter((url) => url.trim())
      .map((url) => url.trim())
  }

  return []
}
</script>

<style scoped>
.medical-imaging-display {
  width: 100%;
}

.data-detail {
  border-radius: 8px;
  padding: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  max-width: 900px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  margin-bottom: 16px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.detail-item span {
  color: #303133;
  font-size: 14px;
}

.content-text {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.content-text.diagnosis {
  background: #f0f9ff;
  border-color: #409eff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .data-detail {
    padding: 16px;
  }

  .content-text {
    padding: 10px;
    font-size: 13px;
  }
}
</style>
