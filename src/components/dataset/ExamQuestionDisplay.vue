<template>
  <div class="exam-question-display">
    <!-- 考试信息头部 -->
    <div class="exam-header">
      <div class="exam-info">
        <h2 class="exam-title">
          <el-icon><Document /></el-icon>
          考试题目预览
        </h2>
        <div class="dataset-meta" v-if="datasetMetadata">
          <div class="meta-item" v-if="datasetMetadata.description">
            <span class="meta-label">数据集描述：</span>
            <span class="meta-value">{{ datasetMetadata.description }}</span>
          </div>
          <div class="meta-item" v-if="datasetMetadata.tags && datasetMetadata.tags.length > 0">
            <span class="meta-label">标签：</span>
            <div class="meta-tags">
              <el-tag v-for="tag in datasetMetadata.tags" :key="tag" size="small" class="tag-item">
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="exam-stats">
        <div class="stat-item">
          <div class="stat-number">{{ questions.length }}</div>
          <div class="stat-label">总题数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ questionTypeStats.single }}</div>
          <div class="stat-label">单选题</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ questionTypeStats.multiple }}</div>
          <div class="stat-label">多选题</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ questionTypeStats.judge }}</div>
          <div class="stat-label">判断题</div>
        </div>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <div class="filter-group">
        <el-select v-model="filterType" placeholder="按题型筛选" clearable style="width: 120px">
          <el-option label="单选题" :value="1" />
          <el-option label="多选题" :value="2" />
          <el-option label="判断题" :value="3" />
        </el-select>
        <el-select
          v-model="filterDifficulty"
          placeholder="按难度筛选"
          clearable
          style="width: 120px"
        >
          <el-option label="简单" :value="1" />
          <el-option label="中等" :value="2" />
          <el-option label="困难" :value="3" />
        </el-select>
      </div>
      <div class="view-options">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button value="preview">预览模式</el-radio-button>
          <el-radio-button value="answer">答案模式</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 题目列表 -->
    <div class="questions-container">
      <div
        v-for="(question, index) in filteredQuestions"
        :key="question.id || `question-${index}`"
        class="question-card"
      >
        <!-- 题目头部 -->
        <div class="question-header">
          <div class="question-number">{{ getQuestionDisplayIndex(question, index) }}</div>
          <div class="question-meta">
            <el-tag
              :type="getQuestionTypeTagType(question.question_type_id)"
              size="small"
              class="type-tag"
            >
              {{ getQuestionTypeLabel(question.question_type_id) }}
            </el-tag>
            <el-tag
              :type="getDifficultyTagType(question.difficulty_level_id)"
              size="small"
              class="difficulty-tag"
            >
              {{ getDifficultyLabel(question.difficulty_level_id) }}
            </el-tag>
          </div>
        </div>

        <!-- 题目内容 -->
        <div class="question-content">
          <div v-if="question.question_title" class="question-title">
            {{ question.question_title }}
          </div>
          <div v-if="question.question_description" class="question-description">
            {{ question.question_description }}
          </div>
          <div class="question-text">{{ question.question_content }}</div>

          <!-- 题目图片 -->
          <div
            v-if="question.question_images && question.question_images.length > 0"
            class="question-images"
          >
            <ImageGallery :images="question.question_images" />
          </div>
        </div>

        <!-- 选项 -->
        <div
          v-if="question.options && Object.keys(question.options).length > 0"
          class="question-options"
        >
          <div
            v-for="(option, key) in question.options"
            :key="key"
            class="option-item"
            :class="{
              'correct-option': viewMode === 'answer' && isCorrectOption(question, key),
              'wrong-option':
                viewMode === 'answer' &&
                !isCorrectOption(question, key) &&
                hasWrongAnswer(question, key),
            }"
          >
            <span class="option-label">{{ key }}.</span>
            <span class="option-text">{{ option }}</span>
            <el-icon
              v-if="viewMode === 'answer' && isCorrectOption(question, key)"
              class="correct-icon"
            >
              <Check />
            </el-icon>
          </div>
        </div>

        <!-- 答案和解析（答案模式下显示） -->
        <div v-if="viewMode === 'answer'" class="question-answer">
          <div class="correct-answer">
            <span class="answer-label">正确答案：</span>
            <template v-if="Array.isArray(question.correct_answer.answer)">
              <el-tag
                v-for="answer in question.correct_answer.answer"
                :key="answer"
                type="success"
                size="small"
                class="answer-tag"
              >
                {{ answer }}
              </el-tag>
            </template>
            <template v-else>
              <el-tag type="success" size="small">
                {{ question.correct_answer.answer }}
              </el-tag>
            </template>
          </div>

          <div v-if="question.answer_explanation" class="explanation">
            <span class="explanation-label">答案解析：</span>
            <p class="explanation-text">{{ question.answer_explanation }}</p>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredQuestions.length === 0" class="empty-state">
        <el-empty description="没有找到符合条件的题目" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ImageGallery from '@/components/dataset/ImageGallery.vue'
import type { ExamQuestion, ExamQuestionData } from '@/types'
import { Check, Document } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'

interface Props {
  data: ExamQuestionData
  datasetType?: number
}

const props = defineProps<Props>()

// 界面状态
const filterType = ref<number | null>(null)
const filterDifficulty = ref<number | null>(null)
const viewMode = ref<'preview' | 'answer'>('preview')

// 计算属性
const questions = computed(() => {
  if (props.data.questions && Array.isArray(props.data.questions)) {
    return props.data.questions
  } else if (props.data.question_content) {
    // 兼容旧的单题目结构
    const singleQuestion: ExamQuestion = {
      question_type_id: props.data.question_type_id || 1,
      subject_id: props.data.subject_id || 0,
      difficulty_level_id: props.data.difficulty_level_id || 1,
      question_title: props.data.question_title || '',
      question_description: props.data.question_description || '',
      question_content: props.data.question_content || props.data.question_text || '',
      options: props.data.options || { A: '', B: '' },
      correct_answer: props.data.correct_answer || { answer: '', type: 'single' },
      answer_explanation: props.data.answer_explanation || props.data.explanation || '',
      question_images: props.data.question_images || [],
      question_metadata: props.data.question_metadata || {},
      sort_order: 1,
    }
    return [singleQuestion]
  }
  return []
})

const datasetMetadata = computed(() => {
  return props.data.dataset_metadata || null
})

const questionTypeStats = computed(() => {
  const stats = { single: 0, multiple: 0, judge: 0 }
  questions.value.forEach((q) => {
    switch (q.question_type_id) {
      case 1:
        stats.single++
        break
      case 2:
        stats.multiple++
        break
      case 3:
        stats.judge++
        break
    }
  })
  return stats
})

const filteredQuestions = computed(() => {
  let filtered = questions.value

  if (filterType.value !== null) {
    filtered = filtered.filter((q) => q.question_type_id === filterType.value)
  }

  if (filterDifficulty.value !== null) {
    filtered = filtered.filter((q) => q.difficulty_level_id === filterDifficulty.value)
  }

  return filtered
})

// 获取题目显示序号
const getQuestionDisplayIndex = (question: ExamQuestion, index: number): string => {
  const originalIndex = questions.value.findIndex((q) => q === question)
  return `${originalIndex + 1}`
}

// 判断是否为正确选项
const isCorrectOption = (question: ExamQuestion, optionKey: string): boolean => {
  if (!question.correct_answer) return false

  const correctAnswer = question.correct_answer.answer

  if (Array.isArray(correctAnswer)) {
    // 多选题
    return correctAnswer.includes(optionKey)
  } else if (typeof correctAnswer === 'string') {
    // 单选题或判断题
    return correctAnswer === optionKey
  }

  return false
}

// 判断是否有错误答案（用于样式显示）
const hasWrongAnswer = (question: ExamQuestion, optionKey: string): boolean => {
  // 这里可以根据实际需求实现，比如显示用户的错误选择
  return false
}

// 获取题型标签
const getQuestionTypeLabel = (typeId: number): string => {
  const typeMap: Record<number, string> = {
    1: '单选题',
    2: '多选题',
    3: '判断题',
  }
  return typeMap[typeId] || '未知题型'
}

const getQuestionTypeTagType = (typeId: number): string => {
  const typeMap: Record<number, string> = {
    1: 'primary',
    2: 'warning',
    3: 'success',
  }
  return typeMap[typeId] || 'info'
}

// 获取难度标签
const getDifficultyLabel = (difficultyId: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难',
  }
  return difficultyMap[difficultyId] || '未知难度'
}

const getDifficultyTagType = (difficultyId: number): string => {
  const typeMap: Record<number, string> = {
    1: 'success',
    2: 'warning',
    3: 'danger',
  }
  return typeMap[difficultyId] || 'info'
}
</script>

<style scoped>
.exam-question-display {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 2px solid #f0f0f0;
}

.exam-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.exam-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.meta-value {
  color: #303133;
}

.meta-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  margin: 0;
}

.exam-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  min-width: 60px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.questions-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
}

.question-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.question-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.type-tag,
.difficulty-tag {
  font-weight: 500;
}

.question-content {
  margin-bottom: 16px;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.question-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
  font-style: italic;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
  margin-bottom: 12px;
}

.question-images {
  margin-top: 12px;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: #f8f9fa;
}

.option-item.correct-option {
  background: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.option-item.wrong-option {
  background: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.option-label {
  font-weight: 600;
  min-width: 24px;
}

.option-text {
  flex: 1;
}

.correct-icon {
  color: #67c23a;
  font-size: 18px;
}

.question-answer {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
  margin-top: 16px;
}

.correct-answer {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.answer-label {
  font-weight: 500;
  color: #303133;
}

.answer-tag {
  margin-right: 4px;
}

.explanation {
  margin-top: 12px;
}

.explanation-label {
  font-weight: 500;
  color: #303133;
  display: block;
  margin-bottom: 8px;
}

.explanation-text {
  color: #606266;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-question-display {
    padding: 16px;
  }

  .exam-header {
    flex-direction: column;
    gap: 16px;
  }

  .exam-stats {
    justify-content: space-around;
    width: 100%;
  }

  .filter-toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
