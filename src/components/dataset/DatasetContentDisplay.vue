<template>
  <div class="dataset-content-display">
    <h3 class="section-title">数据内容</h3>

    <!-- 数据为空时的提示 -->
    <div v-if="!data" class="no-data-tip">
      <el-empty description="暂无数据内容" />
    </div>

    <!-- 根据数据集类型动态渲染对应的显示组件 -->
    <component v-else :is="displayComponent" :data="data" :dataset-type="datasetType" />
  </div>
</template>

<script setup lang="ts">
import type { DatasetType } from '@/types'
import { computed } from 'vue'
import ComprehensiveCaseDisplay from './ComprehensiveCaseDisplay.vue'
import ExamQuestionDisplay from './ExamQuestionDisplay.vue'
import MedicalImagingDisplay from './MedicalImagingDisplay.vue'

interface Props {
  data: any
  datasetType: DatasetType
}

const props = defineProps<Props>()

// 根据数据集类型选择对应的显示组件
const displayComponent = computed(() => {
  const componentMap = {
    1: ComprehensiveCaseDisplay, // 综合性病例
    2: MedicalImagingDisplay, // 影像数据
    3: ExamQuestionDisplay, // 考试题目
  }

  return componentMap[props.datasetType] || null
})
</script>

<style scoped>
.dataset-content-display {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #4285f4;
}

.no-data-tip {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-content-display {
    padding: 16px;
  }
}
</style>
