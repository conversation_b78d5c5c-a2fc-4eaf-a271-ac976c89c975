<template>
  <div class="user-management">
    <!-- 搜索和筛选 -->
    <div class="sys-filter-section">
      <div class="sys-filter-row">
        <div class="filter-left">
          <div class="sys-search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户名或邮箱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="sys-filter-controls">
            <el-select
              v-model="selectedUserSource"
              placeholder="用户类型"
              clearable
              @change="loadUsers"
              style="width: 140px"
            >
              <el-option
                v-for="option in userSourceOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <el-select
              v-model="selectedStatus"
              placeholder="状态"
              clearable
              @change="loadUsers"
              style="width: 120px"
            >
              <el-option label="激活" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>
          </div>
        </div>

        <div class="filter-right">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="sys-table-section hover-scrollbar">
      <el-table :data="filteredUsers" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="username" label="用户名" width="150" />

        <el-table-column prop="email" label="邮箱" width="200" />

        <el-table-column prop="phone" label="手机号" width="130" />

        <el-table-column prop="full_name" label="真实姓名" width="120" />

        <el-table-column prop="user_source" label="用户类型" width="120">
          <template #default="{ row }">
            {{ getUserSourceLabel(row.user_source) }}
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="sys-btn-group">
              <el-button type="primary" link @click="editUser(row)">编辑</el-button>
              <el-button type="success" link @click="assignRoles(row)">分配角色</el-button>
              <el-button type="danger" link @click="deleteUser(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="sys-pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingUser ? '编辑用户' : '新增用户'"
      width="600px"
      @close="resetForm"
    >
      <el-form ref="userFormRef" :model="userForm" :rules="userRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="真实姓名" prop="full_name">
          <el-input v-model="userForm.full_name" placeholder="请输入真实姓名" />
        </el-form-item>

        <el-form-item label="用户类型" prop="user_source">
          <el-select
            v-model="userForm.user_source"
            placeholder="请选择用户类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in userSourceOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="!editingUser" label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="userForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="sys-dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveUser" :loading="saving">
            {{ editingUser ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 角色分配对话框 -->
    <el-dialog
      v-model="showRoleDialog"
      :title="`为用户 ${selectedUser?.username} 分配角色`"
      width="500px"
      @close="resetRoleForm"
    >
      <div class="role-assignment-content">
        <div class="role-selection">
          <el-form-item label="选择角色">
            <el-select
              v-model="selectedRoleIds"
              placeholder="请选择角色"
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 100%"
              v-loading="rolesLoading"
            >
              <el-option
                v-for="role in availableRoles"
                :key="role.id"
                :label="role.display_name || role.name"
                :value="role.id"
              >
                <span style="float: left">{{ role.display_name || role.name }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 12px">
                  {{ role.description }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRoleDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRoleAssignment" :loading="saving"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { assignRoleToUser, getRoleList, getUserRoles, type Role } from '@/api/roles'
import {
  deleteUser as apiDeleteUser,
  createUser,
  getUserList,
  updateUser,
  userSourceOptions,
  type CreateUserRequest,
  type UpdateUserRequest,
  type User,
} from '@/api/users'
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const users = ref<User[]>([])
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const selectedUserSource = ref<number | ''>('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 角色分配相关数据
const showRoleDialog = ref(false)
const selectedUser = ref<User | null>(null)
const availableRoles = ref<Role[]>([])
const selectedRoleIds = ref<number[]>([])
const rolesLoading = ref(false)

// 对话框相关
const showCreateDialog = ref(false)
const editingUser = ref<User | null>(null)
const userFormRef = ref<FormInstance>()

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  phone: '',
  full_name: '',
  user_source: 2 as 1 | 2 | 3 | 4 | 5, // 默认为个人
  password: '',
  is_active: true,
})

// 表单验证规则
const userRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
  ],
  full_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  password: [{ min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }],
}

// 计算属性
const filteredUsers = computed(() => {
  let result = users.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (user) =>
        user.username.toLowerCase().includes(keyword) ||
        user.email.toLowerCase().includes(keyword) ||
        user.full_name?.toLowerCase().includes(keyword),
    )
  }

  // 用户类型过滤
  if (selectedUserSource.value !== '') {
    result = result.filter((user) => user.user_source === selectedUserSource.value)
  }

  // 状态过滤
  if (selectedStatus.value !== '') {
    const isActive = selectedStatus.value === 'true'
    result = result.filter((user) => user.is_active === isActive)
  }

  return result
})

// 方法
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
    }
    const data = await getUserList(params)
    users.value = data
    total.value = data.length
  } catch (error: any) {
    console.error('加载用户列表失败:', error)
    ElMessage.error(error.message || '加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索在计算属性中处理
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedUserSource.value = ''
  selectedStatus.value = ''
  loadUsers()
}

const editUser = (user: User) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    email: user.email || '',
    phone: user.phone,
    full_name: user.full_name,
    user_source: user.user_source,
    password: '',
    is_active: user.is_active,
  })
  showCreateDialog.value = true
}

const saveUser = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    saving.value = true

    if (editingUser.value) {
      // 更新用户
      const updateData: UpdateUserRequest = {
        username: userForm.username,
        email: userForm.email || undefined,
        phone: userForm.phone,
        full_name: userForm.full_name,
        user_source: userForm.user_source,
        is_active: userForm.is_active,
      }
      await updateUser(editingUser.value.id, updateData)
      ElMessage.success('用户更新成功')
    } else {
      // 创建用户
      const createData: CreateUserRequest = {
        username: userForm.username,
        email: userForm.email || undefined,
        phone: userForm.phone,
        full_name: userForm.full_name,
        user_source: userForm.user_source,
        password: userForm.password || undefined,
        is_active: userForm.is_active,
      }
      await createUser(createData)
      ElMessage.success('用户创建成功')
    }

    showCreateDialog.value = false
    loadUsers()
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

const deleteUser = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await apiDeleteUser(user.id)
    ElMessage.success('用户删除成功')
    loadUsers()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const resetForm = () => {
  editingUser.value = null
  Object.assign(userForm, {
    username: '',
    email: '',
    phone: '',
    full_name: '',
    user_source: 2, // 默认为个人
    password: '',
    is_active: true,
  })
  userFormRef.value?.resetFields()
}

// 角色分配相关方法
const assignRoles = async (user: User) => {
  selectedUser.value = user
  showRoleDialog.value = true

  try {
    rolesLoading.value = true

    // 并行加载可用角色和用户当前角色
    const [roles, userRoles] = await Promise.all([
      getRoleList({ is_active: true }),
      getUserRoles(user.id),
    ])

    availableRoles.value = roles
    selectedRoleIds.value = userRoles.map((role) => role.id)
  } catch (error: any) {
    ElMessage.error(error.message || '加载角色信息失败')
  } finally {
    rolesLoading.value = false
  }
}

const saveRoleAssignment = async () => {
  if (!selectedUser.value) return

  try {
    saving.value = true

    await assignRoleToUser({
      user_id: selectedUser.value.id,
      role_ids: selectedRoleIds.value,
    })

    ElMessage.success('角色分配成功')
    showRoleDialog.value = false
  } catch (error: any) {
    ElMessage.error(error.message || '角色分配失败')
  } finally {
    saving.value = false
  }
}

const resetRoleForm = () => {
  selectedUser.value = null
  availableRoles.value = []
  selectedRoleIds.value = []
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadUsers()
}

const getUserSourceLabel = (source: number) => {
  const option = userSourceOptions.find((opt) => opt.value === source)
  return option?.label || source.toString()
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}
.sys-filter-section {
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e3e8;
}
.sys-filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.filter-left {
  display: flex;
  gap: 10px;
  flex: 1;
}
.sys-filter-controls {
  display: flex;
  gap: 10px;
  flex: 1;
}
/* 使用系统管理统一样式，大部分样式已在全局定义 */

/* 角色分配对话框样式 */
.role-assignment-content {
  .role-selection {
    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
    }
  }
}
</style>
