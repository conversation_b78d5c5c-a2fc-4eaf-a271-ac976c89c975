<template>
  <div class="data-dict-management">
    <!-- 主要内容区域 -->
    <div class="module-content">
      <el-card shadow="never" class="content-card">
        <template #header>
          <div class="card-header">
            <div class="header-info">
              <span class="card-title">数据字典管理</span>
              <span class="card-description">管理系统中的数据字典分类和字典项，支持分层级管理</span>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="handleRefresh">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>
        <div class="dict-container">
          <!-- 左侧分类列表 -->
          <div class="category-panel">
            <div class="panel-header">
              <span class="panel-title">字典分类</span>
              <el-button type="primary" size="small" @click="handleAddCategory">
                <el-icon><Plus /></el-icon>
                新增分类
              </el-button>
            </div>

            <div class="category-list">
              <div
                v-for="category in categories"
                :key="category.id"
                :class="['category-item', { active: selectedCategory?.id === category.id }]"
                @click="selectCategory(category)"
              >
                <div class="category-info">
                  <div class="category-name">{{ category.name }}</div>
                </div>
                <div class="category-actions">
                  <el-button type="primary" link size="small" @click.stop="editCategory(category)">
                    编辑
                  </el-button>
                </div>
              </div>

              <el-empty v-if="categories.length === 0" description="暂无分类数据" />
            </div>
          </div>

          <!-- 右侧字典数据列表 -->
          <div class="dict-panel">
            <div class="panel-header">
              <span class="panel-title">
                字典数据
                <span v-if="selectedCategory" class="selected-category">
                  ({{ selectedCategory.name }})
                </span>
              </span>
              <div class="panel-actions">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索字典项..."
                  clearable
                  style="width: 200px; margin-right: 12px"
                  @input="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="!selectedCategory"
                  @click="handleAddDict"
                >
                  <el-icon><Plus /></el-icon>
                  新增字典项
                </el-button>
              </div>
            </div>

            <div class="dict-content">
              <el-table
                v-if="selectedCategory"
                :data="filteredDictList"
                v-loading="dictLoading"
                stripe
                style="width: 100%"
              >
                <el-table-column prop="code" label="编码" width="120" />
                <el-table-column prop="name" label="名称" width="150" />
                <el-table-column prop="description" label="描述" show-overflow-tooltip />
                <el-table-column prop="sort_order" label="排序" width="80" />
                <el-table-column prop="is_active" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.is_active === 1 ? 'success' : 'danger'">
                      {{ row.is_active === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" width="160">
                  <template #default="{ row }">
                    {{ formatDate(row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="{ row }">
                    <div class="dict-btn-group">
                      <el-button type="primary" link size="small" @click="editDict(row)">
                        编辑
                      </el-button>
                      <el-button type="danger" link size="small" @click="deleteDict(row)">
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <div v-else class="no-category-selected">
                <el-empty description="请从左侧选择一个分类查看字典数据">
                  <template #image>
                    <el-icon size="64" color="#c0c4cc">
                      <FolderOpened />
                    </el-icon>
                  </template>
                </el-empty>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      :title="categoryDialogTitle"
      width="500px"
      @close="resetCategoryForm"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="分类编码" prop="code">
          <el-input
            v-model="categoryForm.code"
            placeholder="请输入分类编码"
            :disabled="isEditingCategory"
          />
          <div class="form-tip">编码创建后不可修改，建议使用英文下划线格式</div>
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述（可选）"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="categoryForm.sort_order"
            :min="0"
            :max="9999"
            placeholder="排序值"
          />
          <div class="form-tip">数值越小排序越靠前</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="categorySaving" @click="saveCategoryForm">
            {{ categorySaving ? '保存中...' : '确定' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 字典数据编辑对话框 -->
    <el-dialog
      v-model="dictDialogVisible"
      :title="dictDialogTitle"
      width="600px"
      @close="resetDictForm"
    >
      <el-form ref="dictFormRef" :model="dictForm" :rules="dictRules" label-width="100px">
        <el-form-item label="所属分类" prop="category_id">
          <el-input :value="selectedCategory?.name" disabled placeholder="请先选择分类" />
        </el-form-item>
        <el-form-item label="字典编码" prop="code">
          <el-input v-model="dictForm.code" placeholder="请输入字典编码" />
          <div class="form-tip">编码在同一分类下必须唯一，建议使用英文下划线格式</div>
        </el-form-item>
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="dictForm.name" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item label="父级字典" prop="parent_id">
          <el-select
            v-model="dictForm.parent_id"
            placeholder="请选择父级字典（可选）"
            clearable
            style="width: 100%"
          >
            <el-option label="无父级（顶级）" :value="0" />
            <el-option
              v-for="dict in parentDictOptions"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
          <div class="form-tip">选择父级字典可以建立层级关系</div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="dictForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入字典描述（可选）"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="dictForm.sort_order"
            :min="0"
            :max="9999"
            placeholder="排序值"
          />
          <div class="form-tip">数值越小排序越靠前</div>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-radio-group v-model="dictForm.is_active">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="扩展数据" prop="extra_data">
          <el-input
            v-model="dictForm.extra_data_str"
            type="textarea"
            :rows="3"
            placeholder="请输入JSON格式的扩展数据（可选）"
          />
          <div class="form-tip">可以存储额外的JSON数据，如配置信息等</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dictDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="dictSaving" @click="saveDictForm">
            {{ dictSaving ? '保存中...' : '确定' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { dataDictCategoryApi, dataDictDataApi } from '@/api/dataDict'
import type {
  CreateCategoryRequest,
  CreateDictRequest,
  DataDict,
  DictCategory,
  UpdateCategoryRequest,
  UpdateDictRequest,
} from '@/types'
import { FolderOpened, Plus, Refresh, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, ref } from 'vue'

// 响应式数据
const categories = ref<DictCategory[]>([])
const selectedCategory = ref<DictCategory | null>(null)
const dictList = ref<DataDict[]>([])
const searchKeyword = ref('')
const categoriesLoading = ref(false)
const dictLoading = ref(false)

// 分类对话框相关
const categoryDialogVisible = ref(false)
const categoryFormRef = ref<FormInstance>()
const categorySaving = ref(false)
const isEditingCategory = ref(false)
const editingCategoryCode = ref('')

// 字典数据对话框相关
const dictDialogVisible = ref(false)
const dictFormRef = ref<FormInstance>()
const dictSaving = ref(false)
const isEditingDict = ref(false)
const editingDictId = ref(0)
const parentDictOptions = ref<DataDict[]>([])

// 分类表单数据
const categoryForm = ref({
  code: '',
  name: '',
  description: '',
  sort_order: 0,
})

// 字典数据表单数据
const dictForm = ref({
  category_id: 0,
  parent_id: 0,
  code: '',
  name: '',
  description: '',
  sort_order: 0,
  is_active: 1,
  extra_data_str: '', // 用于编辑JSON字符串
})

// 分类表单验证规则
const categoryRules: FormRules = {
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    {
      pattern: /^[a-z][a-z0-9_]*$/,
      message: '编码必须以小写字母开头，只能包含小写字母、数字和下划线',
      trigger: 'blur',
    },
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  sort_order: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' },
  ],
}

// 字典数据表单验证规则
const dictRules: FormRules = {
  code: [
    { required: true, message: '请输入字典编码', trigger: 'blur' },
    {
      pattern: /^[a-z][a-z0-9_]*$/,
      message: '编码必须以小写字母开头，只能包含小写字母、数字和下划线',
      trigger: 'blur',
    },
  ],
  name: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
    { min: 1, max: 100, message: '字典名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  sort_order: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' },
  ],
  is_active: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

// 计算属性
const filteredDictList = computed(() => {
  if (!searchKeyword.value) return dictList.value
  return dictList.value.filter(
    (item) =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.code.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (item.description &&
        item.description.toLowerCase().includes(searchKeyword.value.toLowerCase())),
  )
})

const categoryDialogTitle = computed(() => {
  return isEditingCategory.value ? '编辑分类' : '新增分类'
})

const dictDialogTitle = computed(() => {
  return isEditingDict.value ? '编辑字典项' : '新增字典项'
})

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 加载分类列表
const loadCategories = async () => {
  try {
    categoriesLoading.value = true
    const response = await dataDictCategoryApi.getCategories()

    if (response.success) {
      // 检查数据结构
      const categoriesData = (response.data as any).categories || response.data
      if (Array.isArray(categoriesData)) {
        categories.value = categoriesData.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
      } else {
        console.error('API返回的数据不是数组:', response.data)
        ElMessage.error('数据格式错误')
        return
      }
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  } finally {
    categoriesLoading.value = false
  }
}

// 选择分类
const selectCategory = async (category: DictCategory) => {
  selectedCategory.value = category
  await loadDictList()
}

// 加载字典数据列表
const loadDictList = async () => {
  if (!selectedCategory.value) return

  try {
    dictLoading.value = true
    const response = await dataDictDataApi.getDictList(selectedCategory.value.code)
    if (response.success) {
      dictList.value = response.data.sort((a, b) => a.sort_order - b.sort_order)
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
    ElMessage.error('加载字典数据失败')
  } finally {
    dictLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 刷新数据
const handleRefresh = async () => {
  await loadCategories()
  if (selectedCategory.value) {
    await loadDictList()
  }
}

// 重置分类表单
const resetCategoryForm = () => {
  categoryForm.value = {
    code: '',
    name: '',
    description: '',
    sort_order: 0,
  }
  isEditingCategory.value = false
  editingCategoryCode.value = ''
  categoryFormRef.value?.clearValidate()
}

// 新增分类
const handleAddCategory = () => {
  resetCategoryForm()
  categoryDialogVisible.value = true
}

// 编辑分类
const editCategory = (category: DictCategory) => {
  resetCategoryForm()
  isEditingCategory.value = true
  editingCategoryCode.value = category.code
  categoryForm.value = {
    code: category.code,
    name: category.name,
    description: category.description || '',
    sort_order: category.sort_order,
  }
  categoryDialogVisible.value = true
}

// 保存分类表单
const saveCategoryForm = async () => {
  if (!categoryFormRef.value) return

  try {
    const valid = await categoryFormRef.value.validate()
    if (!valid) return

    categorySaving.value = true

    if (isEditingCategory.value) {
      // 更新分类
      const updateData: UpdateCategoryRequest = {
        name: categoryForm.value.name,
        description: categoryForm.value.description,
        sort_order: categoryForm.value.sort_order,
      }
      const response = await dataDictCategoryApi.updateCategory(
        editingCategoryCode.value,
        updateData,
      )
      if (response.success) {
        ElMessage.success('分类更新成功')
        categoryDialogVisible.value = false
        await loadCategories()
      }
    } else {
      // 创建分类
      const createData: CreateCategoryRequest = {
        code: categoryForm.value.code,
        name: categoryForm.value.name,
        description: categoryForm.value.description,
        sort_order: categoryForm.value.sort_order,
      }
      const response = await dataDictCategoryApi.createCategory(createData)
      if (response.success) {
        ElMessage.success('分类创建成功')
        categoryDialogVisible.value = false
        await loadCategories()
      }
    }
  } catch (error) {
    console.error('保存分类失败:', error)
    ElMessage.error('保存失败')
  } finally {
    categorySaving.value = false
  }
}

// 重置字典数据表单
const resetDictForm = () => {
  dictForm.value = {
    category_id: selectedCategory.value?.id || 0,
    parent_id: 0,
    code: '',
    name: '',
    description: '',
    sort_order: 0,
    is_active: 1,
    extra_data_str: '',
  }
  isEditingDict.value = false
  editingDictId.value = 0
  dictFormRef.value?.clearValidate()
}

// 加载父级字典选项
const loadParentDictOptions = async () => {
  if (!selectedCategory.value) return

  try {
    const response = await dataDictDataApi.getDictList(selectedCategory.value.code)
    if (response.success) {
      // 过滤掉当前编辑的字典项，避免自己作为自己的父级
      parentDictOptions.value = response.data.filter(
        (item) => !isEditingDict.value || item.id !== editingDictId.value,
      )
    }
  } catch (error) {
    console.error('加载父级字典选项失败:', error)
  }
}

// 新增字典项
const handleAddDict = async () => {
  if (!selectedCategory.value) {
    ElMessage.warning('请先选择一个分类')
    return
  }

  resetDictForm()
  await loadParentDictOptions()
  dictDialogVisible.value = true
}

// 编辑字典项
const editDict = async (dict: DataDict) => {
  if (!selectedCategory.value) return

  resetDictForm()
  isEditingDict.value = true
  editingDictId.value = dict.id

  // 填充表单数据
  dictForm.value = {
    category_id: dict.category_id,
    parent_id: dict.parent_id,
    code: dict.code,
    name: dict.name,
    description: dict.description || '',
    sort_order: dict.sort_order,
    is_active: dict.is_active,
    extra_data_str: dict.extra_data ? JSON.stringify(dict.extra_data, null, 2) : '',
  }

  await loadParentDictOptions()
  dictDialogVisible.value = true
}

// 保存字典数据表单
const saveDictForm = async () => {
  if (!dictFormRef.value || !selectedCategory.value) return

  try {
    const valid = await dictFormRef.value.validate()
    if (!valid) return

    dictSaving.value = true

    // 处理扩展数据
    let extraData = null
    if (dictForm.value.extra_data_str.trim()) {
      try {
        extraData = JSON.parse(dictForm.value.extra_data_str)
      } catch (error) {
        ElMessage.error('扩展数据格式不正确，请输入有效的JSON格式')
        return
      }
    }

    if (isEditingDict.value) {
      // 更新字典项
      const updateData: UpdateDictRequest = {
        name: dictForm.value.name,
        description: dictForm.value.description,
        sort_order: dictForm.value.sort_order,
        is_active: dictForm.value.is_active,
        extra_data: extraData,
      }
      const response = await dataDictDataApi.updateDict(editingDictId.value, updateData)
      if (response.success) {
        ElMessage.success('字典项更新成功')
        dictDialogVisible.value = false
        await loadDictList()
      }
    } else {
      // 创建字典项
      const createData: CreateDictRequest = {
        category_id: selectedCategory.value.id,
        parent_id: dictForm.value.parent_id,
        code: dictForm.value.code,
        name: dictForm.value.name,
        description: dictForm.value.description,
        sort_order: dictForm.value.sort_order,
        is_active: dictForm.value.is_active,
        extra_data: extraData,
      }
      const response = await dataDictDataApi.createDict(createData)
      if (response.success) {
        ElMessage.success('字典项创建成功')
        dictDialogVisible.value = false
        await loadDictList()
      }
    }
  } catch (error) {
    console.error('保存字典项失败:', error)
    ElMessage.error('保存失败')
  } finally {
    dictSaving.value = false
  }
}

// 删除字典项
const deleteDict = async (dict: DataDict) => {
  try {
    await ElMessageBox.confirm(`确定要删除字典项 "${dict.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await dataDictDataApi.deleteDict(dict.id)
    if (response.success) {
      ElMessage.success('删除成功')
      await loadDictList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字典项失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.data-dict-management {
  width: 100%;
  height: 100%;
}

/* 内容区域样式 */
.module-content {
  height: 100%;
}

.content-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.header-info {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 4px;
}

.card-description {
  font-size: 13px;
  color: #909399;
  display: block;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.dict-container {
  display: flex;
  height: calc(100vh - 200px);
  gap: 0;
  overflow: hidden;
}

/* 左侧分类面板 */
.category-panel {
  width: 250px;
  min-width: 250px;
  max-width: 250px;
  flex-shrink: 0;
  border-right: 1px solid #ebeef5;
  padding-right: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.selected-category {
  font-size: 14px;
  color: #409eff;
  font-weight: normal;
}

.category-list {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 12px;
  margin-bottom: 8px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition:
    background-color 0.3s,
    border-color 0.3s;
  background-color: #ffffff;
  box-sizing: border-box;
}

.category-item:hover {
  border-color: #409eff;
  background-color: #ffffff;
}

.category-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  word-break: break-all;
  line-height: 1.4;
}

.category-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.category-item:hover .category-actions {
  opacity: 1;
}

/* 右侧字典面板 */
.dict-panel {
  flex: 1;
  min-width: 0;
  padding-left: 16px;
  overflow: hidden;
}

.panel-actions {
  display: flex;
  align-items: center;
}

.dict-content {
  height: calc(100% - 60px);
  overflow: hidden;
}

.dict-content .el-table {
  width: 100% !important;
}

.no-category-selected {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dict-btn-group {
  display: flex;
  gap: 8px;
}

/* 表单样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dict-container {
    flex-direction: column;
    height: auto;
  }

  .category-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ebeef5;
    padding-right: 0;
    padding-bottom: 16px;
    margin-bottom: 16px;
  }

  .dict-panel {
    padding-left: 0;
  }

  .category-list {
    height: 200px;
  }
}
</style>
