<template>
  <div class="data-permission-management">
    <div class="module-header">
      <div class="header-info">
        <h2 class="module-title">数据权限管理</h2>
        <p class="module-description">管理用户和角色的数据访问权限，控制数据范围和可见性</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleAddPermission">
          <el-icon><Plus /></el-icon>
          新增权限
        </el-button>
      </div>
    </div>

    <div class="module-content">
      <el-card shadow="never" class="content-card">
        <template #header>
          <div class="card-header">
            <span>数据权限列表</span>
            <el-button text @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>

        <div class="table-container hover-scrollbar">
          <el-table :data="permissionData" style="width: 100%" v-loading="loading">
            <el-table-column prop="name" label="权限名称" width="200" />
            <el-table-column prop="type" label="权限类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.type)">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="scope" label="数据范围" width="150" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-switch v-model="row.status" @change="handleStatusChange(row)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
                <el-button text type="danger" @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

// 数据权限接口定义
interface DataPermission {
  id: string
  name: string
  type: string
  scope: string
  description: string
  status: boolean
}

const loading = ref(false)
const permissionData = ref<DataPermission[]>([
  {
    id: '1',
    name: '部门数据权限',
    type: '部门级',
    scope: '本部门及下级',
    description: '可查看本部门及下级部门的所有数据',
    status: true,
  },
  {
    id: '2',
    name: '个人数据权限',
    type: '个人级',
    scope: '仅本人',
    description: '只能查看个人相关的数据',
    status: true,
  },
  {
    id: '3',
    name: '全局数据权限',
    type: '全局级',
    scope: '全部数据',
    description: '可查看系统中的所有数据',
    status: false,
  },
])

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    全局级: 'danger',
    部门级: 'warning',
    个人级: 'success',
  }
  return typeMap[type] || 'info'
}

const handleAddPermission = () => {
  ElMessage.info('添加数据权限功能开发中...')
}

const handleEdit = (row: DataPermission) => {
  ElMessage.info(`编辑权限: ${row.name}`)
}

const handleDelete = (row: DataPermission) => {
  ElMessage.warning(`删除权限: ${row.name}`)
}

const handleStatusChange = (row: DataPermission) => {
  ElMessage.success(`权限状态已${row.status ? '启用' : '禁用'}`)
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}
</script>

<style scoped>
.data-permission-management {
  width: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 24px;
}

.header-info {
  flex: 1;
}

.module-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.module-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.module-content {
  width: 100%;
}

.content-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.table-container {
  width: 100%;
  overflow-x: auto; /* 允许水平滚动 */
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .module-title {
    color: #f9fafb;
  }

  .module-description {
    color: #9ca3af;
  }

  .content-card {
    border-color: #374151;
  }
}
</style>
