<template>
  <div class="permission-management">
    <!-- 权限统计 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Key /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ permissions.length }}</div>
          <div class="stat-label">总权限数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ activePermissionsCount }}</div>
          <div class="stat-label">激活权限</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon resources">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ resourceOptions.length }}</div>
          <div class="stat-label">资源类型</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon actions">
          <el-icon><Operation /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ actionOptions.length }}</div>
          <div class="stat-label">操作类型</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="sys-filter-section">
      <div class="sys-filter-row">
        <div class="filter-left">
          <div class="sys-search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索权限名称、描述或资源..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="sys-filter-controls">
            <el-select
              v-model="selectedResource"
              placeholder="资源类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="resource in resourceOptions"
                :key="resource"
                :label="resource"
                :value="resource"
              />
            </el-select>

            <el-select
              v-model="selectedAction"
              placeholder="操作类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="action in actionOptions"
                :key="action"
                :label="action"
                :value="action"
              />
            </el-select>

            <el-select v-model="selectedStatus" placeholder="状态" clearable @change="handleSearch">
              <el-option label="激活" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>
          </div>
        </div>

        <div class="filter-right">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="refreshPermissions">
            <el-icon><Refresh /></el-icon>
            刷新权限
          </el-button>
        </div>
      </div>
    </div>

    <!-- 权限列表 -->
    <div class="sys-table-section hover-scrollbar">
      <el-table :data="filteredPermissions" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="name" label="权限标识" width="200" />

        <el-table-column
          prop="description"
          label="权限描述"
          min-width="200"
          show-overflow-tooltip
        />

        <el-table-column prop="resource" label="资源" width="150">
          <template #default="{ row }">
            <el-tag type="info">{{ row.resource || '未分类' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="action" label="操作" width="120">
          <template #default="{ row }">
            <el-tag>{{ row.action || '未定义' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="editPermission(row)">编辑</el-button>
            <el-button type="primary" link @click="viewPermissionDetails(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 编辑权限对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑权限" width="600px" @close="resetForm">
      <el-form
        ref="permissionFormRef"
        :model="permissionForm"
        :rules="permissionRules"
        label-width="100px"
      >
        <el-form-item label="权限标识">
          <el-input v-model="permissionForm.name" readonly />
        </el-form-item>

        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>

        <el-form-item label="资源" prop="resource">
          <el-input v-model="permissionForm.resource" placeholder="请输入资源名称" />
        </el-form-item>

        <el-form-item label="操作" prop="action">
          <el-input v-model="permissionForm.action" placeholder="请输入操作名称" />
        </el-form-item>

        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="permissionForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="savePermission" :loading="saving"> 更新 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="权限详情" width="700px">
      <div class="permission-detail" v-if="currentPermission">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>权限ID:</label>
              <span>{{ currentPermission.id }}</span>
            </div>
            <div class="detail-item">
              <label>权限标识:</label>
              <span>{{ currentPermission.name }}</span>
            </div>
            <div class="detail-item">
              <label>权限描述:</label>
              <span>{{ currentPermission.description || '无描述' }}</span>
            </div>
            <div class="detail-item">
              <label>资源:</label>
              <span>{{ currentPermission.resource || '未分类' }}</span>
            </div>
            <div class="detail-item">
              <label>操作:</label>
              <span>{{ currentPermission.action || '未定义' }}</span>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <el-tag :type="currentPermission.is_active ? 'success' : 'danger'">
                {{ currentPermission.is_active ? '激活' : '禁用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(currentPermission.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间:</label>
              <span>{{ formatDate(currentPermission.updated_at) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>关联角色</h3>
          <div class="roles-list" v-loading="rolesLoading">
            <el-tag v-for="role in permissionRoles" :key="role.id" class="role-tag" type="primary">
              {{ role.name }}
            </el-tag>
            <span v-if="permissionRoles.length === 0" class="no-data">暂无关联角色</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getPermissionList,
  getPermissionRoles,
  updatePermission,
  type Permission,
  type UpdatePermissionRequest,
} from '@/api/permissions'
import { CircleCheck, FolderOpened, Key, Operation, Refresh, Search } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const permissions = ref<Permission[]>([])
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const selectedResource = ref('')
const selectedAction = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const editingPermission = ref<Permission | null>(null)
const currentPermission = ref<Permission | null>(null)
const permissionFormRef = ref<FormInstance>()

// 权限详情相关
const permissionRoles = ref<any[]>([])
const rolesLoading = ref(false)

// 表单数据
const permissionForm = reactive({
  name: '',
  description: '',
  resource: '',
  action: '',
  is_active: true,
})

// 表单验证规则
const permissionRules: FormRules = {
  description: [{ max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }],
  resource: [{ max: 50, message: '资源名称不能超过 50 个字符', trigger: 'blur' }],
  action: [{ max: 50, message: '操作名称不能超过 50 个字符', trigger: 'blur' }],
}

// 计算属性
const filteredPermissions = computed(() => {
  // 现在筛选在服务端处理，直接返回权限列表
  return Array.isArray(permissions.value) ? permissions.value : []
})

// 筛选选项数据
const allResourceOptions = ref<string[]>([])
const allActionOptions = ref<string[]>([])

const resourceOptions = computed(() => allResourceOptions.value)
const actionOptions = computed(() => allActionOptions.value)

const activePermissionsCount = computed(() => {
  if (!Array.isArray(permissions.value)) {
    return 0
  }
  return permissions.value.filter((p) => p.is_active).length
})

// 方法
const loadFilterOptions = async () => {
  try {
    // 加载所有权限来构建筛选选项
    const allPermissions = await getPermissionList({ limit: 1000 }) // 获取大量数据来构建选项

    const resources = new Set<string>()
    const actions = new Set<string>()

    allPermissions.forEach((permission) => {
      if (permission.resource) {
        resources.add(permission.resource)
      }
      if (permission.action) {
        actions.add(permission.action)
      }
    })

    allResourceOptions.value = Array.from(resources).sort()
    allActionOptions.value = Array.from(actions).sort()
  } catch (error) {
    console.error('加载筛选选项失败:', error)
  }
}

const loadPermissions = async () => {
  loading.value = true
  try {
    const params: any = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
    }

    // 添加筛选参数
    if (selectedResource.value) {
      params.resource = selectedResource.value
    }
    if (selectedAction.value) {
      params.action = selectedAction.value
    }
    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }
    if (selectedStatus.value !== '') {
      params.is_active = selectedStatus.value === 'true'
    }

    const data = await getPermissionList(params)
    permissions.value = data

    // 由于后端没有返回总数，我们需要估算总数
    // 如果返回的数据长度小于pageSize，说明这是最后一页
    if (data.length < pageSize.value) {
      total.value = (currentPage.value - 1) * pageSize.value + data.length
    } else {
      // 如果返回的数据长度等于pageSize，说明可能还有更多数据
      // 我们设置一个较大的总数，让分页组件显示"下一页"按钮
      total.value = currentPage.value * pageSize.value + 1
    }
  } catch (error: any) {
    ElMessage.error(error.message || '加载权限列表失败')
  } finally {
    loading.value = false
  }
}

const refreshPermissions = () => {
  loadPermissions()
  ElMessage.success('权限列表已刷新')
}

const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  loadPermissions()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedResource.value = ''
  selectedAction.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
  loadPermissions()
}

const editPermission = (permission: Permission) => {
  editingPermission.value = permission
  Object.assign(permissionForm, {
    name: permission.name,
    description: permission.description,
    resource: permission.resource,
    action: permission.action,
    is_active: permission.is_active,
  })
  showEditDialog.value = true
}

const savePermission = async () => {
  if (!permissionFormRef.value || !editingPermission.value) return

  try {
    await permissionFormRef.value.validate()
    saving.value = true

    const updateData: UpdatePermissionRequest = {
      description: permissionForm.description,
      resource: permissionForm.resource,
      action: permissionForm.action,
      is_active: permissionForm.is_active,
    }

    await updatePermission(editingPermission.value.id, updateData)
    ElMessage.success('权限更新成功')
    showEditDialog.value = false
    loadPermissions()
  } catch (error: any) {
    ElMessage.error(error.message || '更新失败')
  } finally {
    saving.value = false
  }
}

const viewPermissionDetails = async (permission: Permission) => {
  currentPermission.value = permission
  showDetailDialog.value = true

  // 加载关联角色
  try {
    rolesLoading.value = true
    const response = await getPermissionRoles(permission.id)
    permissionRoles.value = response.data || []
  } catch (error: any) {
    ElMessage.error(error.message || '加载关联角色失败')
    permissionRoles.value = []
  } finally {
    rolesLoading.value = false
  }
}

const resetForm = () => {
  editingPermission.value = null
  Object.assign(permissionForm, {
    name: '',
    description: '',
    resource: '',
    action: '',
    is_active: true,
  })
  permissionFormRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadPermissions()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadPermissions()
}

// 工具方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadFilterOptions()
  loadPermissions()
})
</script>

<style scoped>
.permission-management {
  width: 100%;
}

.sys-filter-section {
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e3e8;
}

.sys-filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.filter-left {
  display: flex;
  gap: 10px;
  flex: 1;
}

.sys-filter-controls {
  display: flex;
  gap: 10px;
  flex: 1;
}

/* 使用系统管理统一样式，大部分样式已在全局定义 */

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.stat-icon.resources {
  background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
}

.stat-icon.actions {
  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 表格样式已在全局定义 */

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.permission-detail {
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
}

.detail-item span {
  color: #303133;
}

.roles-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  align-items: flex-start;
}

.role-tag {
  margin: 0;
}

.no-data {
  color: #909399;
  font-style: italic;
}

/* 响应式设计已在全局定义 */

@media (max-width: 768px) {
  .stats-section {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
