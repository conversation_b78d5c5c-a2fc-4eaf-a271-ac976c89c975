<template>
  <div class="role-management">
    <!-- 搜索和筛选 -->
    <div class="sys-filter-section">
      <div class="sys-filter-row">
        <div class="filter-left">
          <div class="sys-search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索角色名称或描述..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="sys-filter-controls">
            <el-select
              v-model="selectedStatus"
              placeholder="状态"
              clearable
              @change="loadRoles"
              style="width: 120px"
            >
              <el-option label="激活" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>
          </div>
        </div>

        <div class="filter-right">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="sys-table-section hover-scrollbar">
      <el-table :data="filteredRoles" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="name" label="角色标识" width="150" />

        <el-table-column prop="display_name" label="角色名称" width="150" />

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column label="权限数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="info">{{ getRolePermissionCount(row.id) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="用户数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="success">{{ getRoleUserCount(row.id) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.is_active ? 'success' : 'danger'"
              size="small"
              class="sys-status-tag"
            >
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="sys-btn-group">
              <el-button type="primary" link @click="editRole(row)">编辑</el-button>
              <el-button type="primary" link @click="manageRolePermissions(row)">权限</el-button>
              <el-button type="primary" link @click="manageRoleUsers(row)">用户</el-button>
              <el-button type="danger" link @click="deleteRole(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="sys-pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRole ? '编辑角色' : '新增角色'"
      width="600px"
      @close="resetForm"
    >
      <el-form ref="roleFormRef" :model="roleForm" :rules="roleRules" label-width="100px">
        <el-form-item label="角色标识" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色标识（英文）" />
        </el-form-item>

        <el-form-item label="角色名称" prop="display_name">
          <el-input v-model="roleForm.display_name" placeholder="请输入角色显示名称" />
        </el-form-item>

        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="roleForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="sys-dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRole" :loading="saving">
            {{ editingRole ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      v-model="showPermissionDialog"
      title="角色权限管理"
      width="800px"
      @close="resetPermissionDialog"
    >
      <div class="permission-management">
        <div class="permission-header">
          <h3>为角色 "{{ currentRole?.display_name }}" 分配权限</h3>
          <p>选择该角色应该拥有的权限</p>
        </div>

        <div class="permission-content" v-loading="permissionLoading">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTreeData"
            :props="{ children: 'children', label: 'label' }"
            show-checkbox
            node-key="id"
            :default-checked-keys="selectedPermissionIds"
            @check="handlePermissionCheck"
          />
        </div>
      </div>

      <template #footer>
        <div class="sys-dialog-footer">
          <el-button @click="showPermissionDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRolePermissions" :loading="permissionSaving">
            保存权限
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户管理对话框 -->
    <el-dialog v-model="showUserDialog" title="角色用户管理" width="800px" @close="resetUserDialog">
      <div class="user-management-dialog">
        <div class="user-header">
          <h3>为角色 "{{ currentRole?.display_name }}" 管理用户</h3>
          <p>选择拥有该角色的用户</p>
        </div>

        <div class="user-content" v-loading="userLoading">
          <!-- 搜索和添加用户 -->
          <div class="user-search-section">
            <div class="search-bar">
              <el-input
                v-model="userSearchKeyword"
                placeholder="搜索用户名或ID"
                clearable
                @input="handleUserSearch"
                style="width: 300px; margin-right: 12px"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" @click="showAddUserDialog = true">
                <el-icon><Plus /></el-icon>
                添加用户
              </el-button>
            </div>
          </div>

          <!-- 已分配用户列表 -->
          <div class="assigned-users-section">
            <h4>已分配用户 ({{ roleUsers.length }})</h4>
            <el-table :data="roleUsers" stripe>
              <el-table-column prop="id" label="用户ID" width="80" />
              <el-table-column prop="username" label="用户名" width="150" />
              <el-table-column prop="email" label="邮箱" />
              <el-table-column prop="display_name" label="显示名称" width="120" />
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button
                    type="danger"
                    link
                    @click="removeUserFromRole(row)"
                    :loading="userSaving"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div v-if="roleUsers.length === 0" class="empty-state">
              <el-empty description="该角色暂无分配用户" />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="sys-dialog-footer">
          <el-button @click="showUserDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加用户对话框 -->
    <el-dialog
      v-model="showAddUserDialog"
      title="添加用户到角色"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="add-user-content">
        <div class="search-section">
          <el-input
            v-model="addUserSearchKeyword"
            placeholder="搜索用户名、邮箱或ID"
            clearable
            @input="handleAddUserSearch"
            style="margin-bottom: 16px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="available-users-section">
          <h4>可添加用户</h4>
          <el-table
            :data="filteredAvailableUsers"
            stripe
            max-height="400"
            @selection-change="handleUserSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="60" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="display_name" label="显示名称" width="120" />
          </el-table>

          <div v-if="filteredAvailableUsers.length === 0" class="empty-state">
            <el-empty description="没有找到可添加的用户" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="sys-dialog-footer">
          <el-button @click="showAddUserDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="addSelectedUsersToRole"
            :loading="userSaving"
            :disabled="selectedUsersToAdd.length === 0"
          >
            添加选中用户 ({{ selectedUsersToAdd.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getPermissionList, type Permission } from '@/api/permissions'
import {
  deleteRole as apiDeleteRole,
  assignPermissionsToRole,
  assignRoleToUser,
  createRole,
  getRoleList,
  getRolePermissions,
  getRoleUsers,
  removeRoleFromUser,
  updateRole,
  type CreateRoleRequest,
  type Role,
  type UpdateRoleRequest,
} from '@/api/roles'
import { getUserList } from '@/api/users'
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const roles = ref<Role[]>([])
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const showCreateDialog = ref(false)
const showPermissionDialog = ref(false)
const showUserDialog = ref(false)
const editingRole = ref<Role | null>(null)
const currentRole = ref<Role | null>(null)
const roleFormRef = ref<FormInstance>()

// 权限管理相关
const permissionLoading = ref(false)
const permissionSaving = ref(false)
const allPermissions = ref<Permission[]>([])
const rolePermissions = ref<Permission[]>([])
const selectedPermissionIds = ref<number[]>([])
const permissionTreeRef = ref()

// 用户管理相关
const userLoading = ref(false)
const userSaving = ref(false)
const allUsers = ref<any[]>([])
const roleUsers = ref<any[]>([])
const selectedUserIds = ref<number[]>([])
const showAddUserDialog = ref(false)
const userSearchKeyword = ref('')
const addUserSearchKeyword = ref('')
const selectedUsersToAdd = ref<any[]>([])

// 统计数据
const rolePermissionCounts = ref<Record<number, number>>({})
const roleUserCounts = ref<Record<number, number>>({})

// 表单数据
const roleForm = reactive({
  name: '',
  display_name: '',
  description: '',
  is_active: true,
})

// 表单验证规则
const roleRules: FormRules = {
  name: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: '角色标识必须以字母开头，只能包含字母、数字和下划线',
      trigger: 'blur',
    },
  ],
  display_name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  description: [{ max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }],
}

// 计算属性
const filteredRoles = computed(() => {
  let result = roles.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (role) =>
        role.name.toLowerCase().includes(keyword) ||
        role.display_name.toLowerCase().includes(keyword) ||
        role.description?.toLowerCase().includes(keyword),
    )
  }

  // 状态过滤
  if (selectedStatus.value !== '') {
    const isActive = selectedStatus.value === 'true'
    result = result.filter((role) => role.is_active === isActive)
  }

  return result
})

// 权限树数据
const permissionTreeData = computed(() => {
  // 将权限按资源分组
  const groups: Record<string, Permission[]> = {}
  allPermissions.value.forEach((permission) => {
    const resource = permission.resource || '其他'
    if (!groups[resource]) {
      groups[resource] = []
    }
    groups[resource].push(permission)
  })

  // 转换为树形结构
  return Object.keys(groups).map((resource) => ({
    id: `group_${resource}`,
    label: resource,
    children: groups[resource].map((permission) => ({
      id: permission.id,
      label: `${permission.name} (${permission.description || ''})`,
    })),
  }))
})

// 可添加用户列表（排除已分配的用户）
const availableUsers = computed(() => {
  const assignedUserIds = new Set(roleUsers.value.map((u) => u.id))
  return allUsers.value.filter((user) => !assignedUserIds.has(user.id))
})

// 过滤后的可添加用户列表
const filteredAvailableUsers = computed(() => {
  if (!addUserSearchKeyword.value) {
    return availableUsers.value
  }

  const keyword = addUserSearchKeyword.value.toLowerCase()
  return availableUsers.value.filter(
    (user) =>
      user.username?.toLowerCase().includes(keyword) ||
      user.email?.toLowerCase().includes(keyword) ||
      user.display_name?.toLowerCase().includes(keyword) ||
      user.id.toString().includes(keyword),
  )
})

// 方法
const loadRoles = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
    }
    const data = await getRoleList(params)
    roles.value = Array.isArray(data) ? data : []
    total.value = roles.value.length

    // 加载统计数据
    await loadRoleStats()
  } catch (error: any) {
    console.error('加载角色列表失败:', error)
    ElMessage.error(error.message || '加载角色列表失败')
    roles.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const loadRoleStats = async () => {
  try {
    const permissionCounts: Record<number, number> = {}
    const userCounts: Record<number, number> = {}

    for (const role of roles.value) {
      try {
        const [permissions, users] = await Promise.all([
          getRolePermissions(role.id),
          getRoleUsers(role.id),
        ])
        permissionCounts[role.id] = Array.isArray(permissions) ? permissions.length : 0
        userCounts[role.id] = Array.isArray(users) ? users.length : 0
      } catch (error) {
        permissionCounts[role.id] = 0
        userCounts[role.id] = 0
      }
    }

    rolePermissionCounts.value = permissionCounts
    roleUserCounts.value = userCounts
  } catch (error) {
    console.error('加载角色统计数据失败:', error)
  }
}

const getRolePermissionCount = (roleId: number) => {
  return rolePermissionCounts.value[roleId] || 0
}

const getRoleUserCount = (roleId: number) => {
  return roleUserCounts.value[roleId] || 0
}

const handleSearch = () => {
  // 搜索在计算属性中处理
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedStatus.value = ''
  loadRoles()
}

const editRole = (role: Role) => {
  editingRole.value = role
  Object.assign(roleForm, {
    name: role.name,
    display_name: role.display_name,
    description: role.description,
    is_active: role.is_active,
  })
  showCreateDialog.value = true
}

const saveRole = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    saving.value = true

    if (editingRole.value) {
      // 更新角色
      const updateData: UpdateRoleRequest = {
        display_name: roleForm.display_name,
        description: roleForm.description,
        is_active: roleForm.is_active,
      }
      await updateRole(editingRole.value.id, updateData)
      ElMessage.success('角色更新成功')
    } else {
      // 创建角色
      const createData: CreateRoleRequest = {
        name: roleForm.name,
        display_name: roleForm.display_name,
        description: roleForm.description,
        is_active: roleForm.is_active,
      }
      await createRole(createData)
      ElMessage.success('角色创建成功')
    }

    showCreateDialog.value = false
    loadRoles()
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

const deleteRole = async (role: Role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.display_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await apiDeleteRole(role.id)
    ElMessage.success('角色删除成功')
    loadRoles()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const resetForm = () => {
  editingRole.value = null
  Object.assign(roleForm, {
    name: '',
    display_name: '',
    description: '',
    is_active: true,
  })
  roleFormRef.value?.resetFields()
}

// 权限管理方法
const manageRolePermissions = async (role: Role) => {
  currentRole.value = role
  showPermissionDialog.value = true

  try {
    permissionLoading.value = true

    // 加载所有权限和角色权限
    const [allPerms, rolePerms] = await Promise.all([
      getPermissionList(),
      getRolePermissions(role.id),
    ])

    // 确保数据是数组
    const safeAllPerms = Array.isArray(allPerms) ? allPerms : []
    const safeRolePerms = Array.isArray(rolePerms) ? rolePerms : []

    allPermissions.value = safeAllPerms
    // 将角色权限转换为统一的Permission格式
    rolePermissions.value = safeRolePerms.map((p) => ({
      id: p.id,
      name: p.name,
      resource: p.resource,
      action: p.action,
      description: p.description,
      created_at: p.created_at,
      updated_at: p.updated_at,
    }))
    selectedPermissionIds.value = safeRolePerms.map((p) => p.id)
  } catch (error: any) {
    ElMessage.error(error.message || '加载权限数据失败')
  } finally {
    permissionLoading.value = false
  }
}

const handlePermissionCheck = (_data: any, checked: any) => {
  // 获取所有选中的权限ID（排除分组节点）
  const checkedKeys = checked.checkedKeys.filter((key: any) => {
    // 确保key是字符串类型，然后检查是否为分组节点
    const keyStr = String(key)
    return !keyStr.startsWith('group_')
  })
  selectedPermissionIds.value = checkedKeys.map((key: any) => {
    // 如果key已经是数字，直接返回；否则转换为数字
    return typeof key === 'number' ? key : parseInt(String(key))
  })
}

const saveRolePermissions = async () => {
  if (!currentRole.value) return

  try {
    permissionSaving.value = true

    // 直接使用选中的权限ID列表进行批量分配
    await assignPermissionsToRole(currentRole.value.id, selectedPermissionIds.value)

    ElMessage.success('权限分配成功')
    showPermissionDialog.value = false
    loadRoleStats() // 重新加载统计数据
  } catch (error: any) {
    ElMessage.error(error.message || '权限分配失败')
  } finally {
    permissionSaving.value = false
  }
}

const resetPermissionDialog = () => {
  currentRole.value = null
  allPermissions.value = []
  rolePermissions.value = []
  selectedPermissionIds.value = []
}

// 用户管理方法
const manageRoleUsers = async (role: Role) => {
  currentRole.value = role
  showUserDialog.value = true

  try {
    userLoading.value = true
    await loadRoleUsersData()
  } catch (error: any) {
    console.error('加载用户数据失败:', error)
    ElMessage.error(error.message || '加载用户数据失败')
  } finally {
    userLoading.value = false
  }
}

const saveRoleUsers = async () => {
  if (!currentRole.value) return

  try {
    userSaving.value = true

    const currentUserIds = roleUsers.value.map((u) => u.id)
    const newUserIds = selectedUserIds.value

    // 找出需要添加和移除的用户
    const toAdd = newUserIds.filter((id) => !currentUserIds.includes(id))
    const toRemove = currentUserIds.filter((id) => !newUserIds.includes(id))

    // 执行用户分配和移除
    const promises = []

    for (const userId of toAdd) {
      promises.push(assignRoleToUser({ user_id: userId, role_ids: [currentRole.value.id] }))
    }

    for (const userId of toRemove) {
      promises.push(removeRoleFromUser(userId, currentRole.value.id))
    }

    await Promise.all(promises)

    ElMessage.success('用户分配成功')
    showUserDialog.value = false
    loadRoleStats() // 重新加载统计数据
  } catch (error: any) {
    ElMessage.error(error.message || '用户分配失败')
  } finally {
    userSaving.value = false
  }
}

const resetUserDialog = () => {
  currentRole.value = null
  allUsers.value = []
  roleUsers.value = []
  selectedUserIds.value = []
}

// 用户搜索处理
const handleUserSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleAddUserSearch = () => {
  // 搜索逻辑在计算属性中处理
}

// 处理用户选择变化
const handleUserSelectionChange = (selection: any[]) => {
  selectedUsersToAdd.value = selection
}

// 从角色移除用户
const removeUserFromRole = async (user: any) => {
  if (!currentRole.value) return

  try {
    userSaving.value = true
    await removeRoleFromUser(user.id, currentRole.value.id)
    ElMessage.success(`已将用户 ${user.username} 从角色中移除`)

    // 重新加载角色用户列表
    await loadRoleUsersData()
    await loadRoles() // 刷新角色统计
  } catch (error: any) {
    ElMessage.error(error.message || '移除用户失败')
  } finally {
    userSaving.value = false
  }
}

// 添加选中用户到角色
const addSelectedUsersToRole = async () => {
  if (!currentRole.value || selectedUsersToAdd.value.length === 0) return

  try {
    userSaving.value = true

    // 批量添加用户到角色
    for (const user of selectedUsersToAdd.value) {
      await assignRoleToUser({
        user_id: user.id,
        role_ids: [currentRole.value.id],
      })
    }

    ElMessage.success(`成功添加 ${selectedUsersToAdd.value.length} 个用户到角色`)
    showAddUserDialog.value = false
    selectedUsersToAdd.value = []
    addUserSearchKeyword.value = ''

    // 重新加载数据
    await loadRoleUsersData()
    await loadRoles() // 刷新角色统计
  } catch (error: any) {
    ElMessage.error(error.message || '添加用户失败')
  } finally {
    userSaving.value = false
  }
}

// 加载角色用户数据
const loadRoleUsersData = async () => {
  if (!currentRole.value) return

  try {
    const [allUsersData, roleUsersData] = await Promise.all([
      getUserList(),
      getRoleUsers(currentRole.value.id),
    ])

    // 确保数据是数组
    const safeAllUsersData = Array.isArray(allUsersData) ? allUsersData : []
    const safeRoleUsersData = Array.isArray(roleUsersData) ? roleUsersData : []

    allUsers.value = safeAllUsersData
    roleUsers.value = safeRoleUsersData
  } catch (error: any) {
    console.error('加载用户数据失败:', error)
    ElMessage.error(error.message || '加载用户数据失败')
    allUsers.value = []
    roleUsers.value = []
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadRoles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadRoles()
}

// 工具方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.role-management {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}
.sys-filter-section {
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e3e8;
}
.sys-filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.filter-left {
  display: flex;
  gap: 10px;
  flex: 1;
}
/* 使用系统管理统一样式，大部分样式已在全局定义 */

.permission-management,
.user-management-dialog {
  padding: 16px 0;
}

.permission-header,
.user-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.permission-header h3,
.user-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.permission-header p,
.user-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.permission-content,
.user-content {
  min-height: 300px;
}

/* 响应式设计已在全局定义 */

.user-search-section {
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
}

.assigned-users-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-weight: 600;
}

.add-user-content {
  max-height: 500px;
}

.available-users-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-weight: 600;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>
