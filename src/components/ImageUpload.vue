<template>
  <div class="image-upload-container">
    <el-upload
      :action="uploadAction"
      :headers="uploadHeaders"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :file-list="fileList"
      list-type="picture-card"
      :accept="acceptTypesString"
      :limit="maxCount"
      :on-exceed="handleExceed"
      :disabled="disabled || uploadingCount > 0"
      :drag="enableDrag"
      :class="{ 'upload-drag': enableDrag }"
      multiple
      :auto-upload="false"
      :on-change="handleFileChange"
    >
      <template #default>
        <div v-if="uploadingCount === 0" class="upload-trigger">
          <el-icon><Plus /></el-icon>
          <div v-if="showText" class="upload-text">选择图片</div>
          <div v-if="showText" class="upload-hint">支持多选</div>
        </div>
        <div v-else class="uploading-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          <div class="uploading-text">上传中...</div>
          <div class="uploading-count">{{ uploadingCount }}个文件</div>
        </div>
      </template>

      <template #tip>
        <div class="upload-tip">
          {{ tipText }}
        </div>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览" width="80%" center>
      <div class="preview-container">
        <img :src="dialogImageUrl" alt="Preview Image" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { fileUploadApi } from '@/api/fileUpload'
import type { ApiResponse } from '@/types'
import { showError, showSuccess, showWarning } from '@/utils'
import { Loading, Plus } from '@element-plus/icons-vue'
import { computed, ref, watch } from 'vue'

// 上传文件响应类型
interface UploadFileResponse {
  file_id: string
  original_filename: string
  cos_key: string
  url: string
  size: number
  content_type: string
  category: string
  is_public: boolean
  uploaded_by: number
  uploaded_at: string
}

// 图片数据类型
export interface ImageItem {
  uid: string
  name: string
  url: string
  cos_key: string
  status: 'success' | 'uploading' | 'error'
}

// 组件属性
interface Props {
  modelValue: ImageItem[]
  maxCount?: number
  maxSize?: number // MB
  acceptTypes?: string[]
  uploadPath?: string
  disabled?: boolean
  enableDrag?: boolean
  showText?: boolean
  placeholder?: string
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: ImageItem[]): void
  (e: 'upload-success', file: ImageItem): void
  (e: 'upload-error', error: string): void
  (e: 'remove', file: ImageItem): void
}

const props = withDefaults(defineProps<Props>(), {
  maxCount: 5,
  maxSize: 10,
  acceptTypes: () => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  uploadPath: 'uploads/images',
  disabled: false,
  enableDrag: false,
  showText: true,
  placeholder: '点击或拖拽上传图片',
})

const emit = defineEmits<Emits>()

// 响应式数据
const uploadingCount = ref(0)
const images = ref<ImageItem[]>([])

// 预览相关
const dialogVisible = ref(false)
const dialogImageUrl = ref('')

// 计算属性
const uploadAction = ref('') // Element Plus 需要，但我们使用自定义上传
const uploadHeaders = ref({
  Authorization: `Bearer ${localStorage.getItem('token') || ''}`,
})

const acceptTypesString = computed(() => {
  return props.acceptTypes.map((type) => `.${type}`).join(',')
})

const fileList = computed(() => {
  return images.value.map((img) => ({
    uid: parseInt(img.uid),
    name: img.name,
    url: img.url,
    status: img.status === 'error' ? ('fail' as const) : (img.status as any),
  }))
})

const tipText = computed(() => {
  if (props.placeholder) {
    return props.placeholder
  }

  const typeText = props.acceptTypes.join('/')
  const sizeText = `${props.maxSize}MB`
  const countText = `${props.maxCount}张`

  return `支持 ${typeText} 格式，单个文件不超过 ${sizeText}，最多上传 ${countText}，支持批量选择`
})

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && JSON.stringify(newValue) !== JSON.stringify(images.value)) {
      images.value = [...newValue]
    }
  },
  { immediate: true, deep: true },
)

// 监听内部数据变化，向外传递
watch(
  images,
  (newValue) => {
    if (JSON.stringify(newValue) !== JSON.stringify(props.modelValue)) {
      emit('update:modelValue', [...newValue])
    }
  },
  { deep: true },
)

// 文件选择变化处理（支持批量选择）
const handleFileChange = (file: any, fileList: any[]) => {
  // 获取新选择的文件（排除已经在上传列表中的文件）
  const newFiles = fileList.filter(
    (f) => f.status === 'ready' && !images.value.find((img) => img.name === f.name),
  )

  if (newFiles.length === 0) return

  // 检查总数量限制
  const totalCount = images.value.length + newFiles.length
  if (totalCount > props.maxCount) {
    showError(`最多只能上传${props.maxCount}张图片，当前已有${images.value.length}张`)
    return
  }

  // 批量验证和上传文件
  let validFiles = 0
  newFiles.forEach((fileItem) => {
    const file = fileItem.raw
    if (validateFile(file)) {
      validFiles++
    }
  })

  if (validFiles > 0) {
    uploadingCount.value += validFiles
    newFiles.forEach((fileItem) => {
      const file = fileItem.raw
      if (validateFile(file)) {
        uploadImage(file, false) // 传递false表示不要在uploadImage中增加计数器
      }
    })
  }
}

// 文件验证
const validateFile = (file: File): boolean => {
  // 检查文件类型
  const fileExt = file.name.split('.').pop()?.toLowerCase()
  if (!fileExt || !props.acceptTypes.includes(fileExt)) {
    showError(`只能上传 ${props.acceptTypes.join('/')} 格式的文件!`)
    return false
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    showError(`上传文件大小不能超过 ${props.maxSize}MB!`)
    return false
  }

  return true
}

// 上传前验证（保留用于拖拽上传）
const beforeUpload = (file: File) => {
  if (!validateFile(file)) {
    return false
  }

  // 检查数量限制
  if (images.value.length >= props.maxCount) {
    showError(`最多只能上传${props.maxCount}张图片`)
    return false
  }

  // 开始上传（单文件上传，需要增加计数器）
  uploadingCount.value++
  uploadImage(file, false) // 传递false因为已经手动增加了计数器
  return false // 阻止默认上传，使用自定义上传
}

// 自定义上传
const uploadImage = async (file: File, incrementCounter: boolean = true) => {
  const uid = Date.now().toString() + Math.random().toString(36).substr(2, 9)

  // 添加到列表中，状态为上传中
  const uploadingImage: ImageItem = {
    uid,
    name: file.name,
    url: '',
    cos_key: '',
    status: 'uploading',
  }
  images.value.push(uploadingImage)

  try {
    const response: ApiResponse<UploadFileResponse> = await fileUploadApi.uploadImage(file, {
      is_public: true,
      custom_path: props.uploadPath,
      random_rename: true,
    })

    if (response.success) {
      // 更新图片信息
      const index = images.value.findIndex((img) => img.uid === uid)
      if (index !== -1) {
        images.value[index] = {
          uid,
          name: file.name,
          url: response.data.url,
          cos_key: response.data.cos_key,
          status: 'success',
        }

        emit('upload-success', images.value[index])
      }

      showSuccess('图片上传成功!')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('上传图片失败:', error)

    // 更新状态为失败
    const index = images.value.findIndex((img) => img.uid === uid)
    if (index !== -1) {
      images.value[index].status = 'error'
    }

    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showError(`图片上传失败: ${errorMessage}`)
    emit('upload-error', errorMessage)
  } finally {
    uploadingCount.value--
  }
}

// 上传成功回调（Element Plus 需要，但我们使用自定义上传）
const handleSuccess = () => {
  // 空实现，实际处理在 uploadImage 中
}

// 上传失败回调
const handleError = () => {
  showError('图片上传失败')
  uploadingCount.value--
}

// 删除图片
const handleRemove = async (file: any) => {
  const image = images.value.find((img) => img.uid === file.uid.toString())
  if (image) {
    try {
      // 如果有 cos_key，尝试删除服务器上的文件（新上传的文件）
      if (image.cos_key) {
        await fileUploadApi.deleteFile(image.cos_key)
      }
      // 对于已存在的文件（cos_key为空），只从列表中移除，不删除服务器文件

      // 从列表中移除
      images.value = images.value.filter((img) => img.uid !== image.uid)

      emit('remove', image)
      showSuccess('图片删除成功')
    } catch (error) {
      console.error('删除图片失败:', error)
      // 即使服务器删除失败，也要从列表中移除
      images.value = images.value.filter((img) => img.uid !== image.uid)
      emit('remove', image)
      showWarning('图片已从列表中移除，但服务器文件删除失败')
    }
  }
}

// 处理文件数量超出限制
const handleExceed = () => {
  showWarning(`最多只能上传${props.maxCount}张图片`)
}

// 处理图片预览
const handlePictureCardPreview = (file: any) => {
  dialogImageUrl.value = file.url || ''
  dialogVisible.value = true
}

// 暴露方法供外部调用
defineExpose({
  clearImages: () => {
    images.value = []
  },
  getImages: () => images.value,
  getSuccessImages: () => images.value.filter((img) => img.status === 'success'),
})
</script>

<style scoped>
.image-upload-container {
  width: 100%;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #8c939d;
  font-size: 14px;
}

.upload-trigger .el-icon {
  font-size: 28px;
  margin-bottom: 6px;
}

.upload-text {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.upload-hint {
  font-size: 10px;
  color: #a8abb2;
}

.uploading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #409eff;
  font-size: 12px;
}

.uploading-indicator .el-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.uploading-text {
  font-size: 12px;
  font-weight: 500;
}

.uploading-count {
  font-size: 10px;
  color: #73767a;
  margin-top: 2px;
}

.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-upload--picture-card) {
  width: 104px;
  height: 104px;
  border-radius: 6px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 104px;
  height: 104px;
  border-radius: 6px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 拖拽上传样式 */
.upload-drag :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border-radius: 6px;
}

/* 图片预览样式 */
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 80px;
    height: 80px;
  }

  .upload-trigger .el-icon {
    font-size: 20px;
    margin-bottom: 3px;
  }

  .upload-text {
    font-size: 10px;
    margin-bottom: 1px;
  }

  .upload-hint {
    font-size: 8px;
  }

  .uploading-text {
    font-size: 10px;
  }

  .uploading-count {
    font-size: 8px;
  }

  .upload-tip {
    font-size: 11px;
  }

  .preview-image {
    max-height: 50vh;
  }
}
</style>
