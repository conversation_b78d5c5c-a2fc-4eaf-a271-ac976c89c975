import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { ElMessage } from 'element-plus'
import { permissionApi } from '@/api/permissions'
import { PERMISSIONS, type PermissionKey } from '@/types'

// 权限检查缓存
const permissionCache = new Map<string, boolean>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

interface CacheItem {
  value: boolean
  timestamp: number
}

// 清理过期缓存
const cleanExpiredCache = () => {
  const now = Date.now()
  for (const [key, item] of permissionCache.entries()) {
    if (now - (item as CacheItem).timestamp > CACHE_DURATION) {
      permissionCache.delete(key)
    }
  }
}

// 检查权限（带缓存）
const checkPermissionWithCache = async (resource: string, action: string): Promise<boolean> => {
  const cacheKey = `${resource}:${action}`
  const cached = permissionCache.get(cacheKey) as CacheItem
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.value
  }
  
  try {
    const response = await permissionApi.checkPermission(resource, action)
    const hasPermission = response.success && response.data.has_permission
    
    permissionCache.set(cacheKey, {
      value: hasPermission,
      timestamp: Date.now()
    })
    
    return hasPermission
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

// 路由权限映射
const routePermissionMap: Record<string, PermissionKey[]> = {
  // 数据集相关路由
  'dataset-overview': [PERMISSIONS.DATASET_READ],
  'dataset-list': [PERMISSIONS.DATASET_READ],
  'dataset-create': [PERMISSIONS.DATASET_WRITE],
  'dataset-edit': [PERMISSIONS.DATASET_WRITE],
  'dataset-detail': [PERMISSIONS.DATASET_READ],
  
  // 数据集审核相关路由
  'dataset-review-overview': [PERMISSIONS.DATASET_REVIEW],
  'dataset-review-list': [PERMISSIONS.DATASET_REVIEW],
  'dataset-review-history': [PERMISSIONS.DATASET_REVIEW],
  
  // 系统管理相关路由
  'system': [PERMISSIONS.SYSTEM_ADMIN, PERMISSIONS.USER_READ, PERMISSIONS.ROLE_READ],
  'user-management': [PERMISSIONS.USER_READ],
  'role-management': [PERMISSIONS.ROLE_READ],
  'permission-management': [PERMISSIONS.PERMISSION_READ],
  
  // 评估相关路由
  'evaluations': [PERMISSIONS.EVALUATION_READ],
  'evaluation-create': [PERMISSIONS.EVALUATION_CREATE],
  'tasks': [PERMISSIONS.EVALUATION_READ],
  
  // 模型相关路由
  'models': [PERMISSIONS.EVALUATION_READ], // 假设查看模型需要评估读取权限
}

// 权限路由守卫
export const createPermissionGuard = () => {
  return async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    // 清理过期缓存
    cleanExpiredCache()
    
    // 检查路由是否需要权限验证
    const routeName = to.name as string
    const requiredPermissions = routePermissionMap[routeName]
    
    // 如果路由不需要特殊权限，直接通过
    if (!requiredPermissions || requiredPermissions.length === 0) {
      next()
      return
    }
    
    // 检查用户是否有任意一个所需权限
    let hasPermission = false
    
    for (const permission of requiredPermissions) {
      const [resource, action] = permission.split(':')
      const result = await checkPermissionWithCache(resource, action)
      
      if (result) {
        hasPermission = true
        break
      }
    }
    
    if (hasPermission) {
      next()
    } else {
      ElMessage.error('您没有访问此页面的权限')
      
      // 根据用户权限重定向到合适的页面
      if (from.name) {
        next(false) // 阻止导航，停留在当前页面
      } else {
        next('/app/home') // 重定向到首页
      }
    }
  }
}

// 检查特定权限的工具函数
export const checkRoutePermission = async (routeName: string): Promise<boolean> => {
  const requiredPermissions = routePermissionMap[routeName]
  
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true
  }
  
  for (const permission of requiredPermissions) {
    const [resource, action] = permission.split(':')
    const result = await checkPermissionWithCache(resource, action)
    
    if (result) {
      return true
    }
  }
  
  return false
}

// 清理权限缓存
export const clearPermissionCache = () => {
  permissionCache.clear()
}

// 预加载权限（可选，用于提升用户体验）
export const preloadPermissions = async () => {
  const commonPermissions = [
    'dataset:read',
    'dataset:write',
    'dataset:review',
    'evaluation:read',
    'user:read',
    'system:admin'
  ]
  
  const promises = commonPermissions.map(permission => {
    const [resource, action] = permission.split(':')
    return checkPermissionWithCache(resource, action)
  })
  
  try {
    await Promise.all(promises)
  } catch (error) {
    console.error('预加载权限失败:', error)
  }
}
