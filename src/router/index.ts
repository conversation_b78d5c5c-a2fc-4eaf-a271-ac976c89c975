import { useUserStore } from '@/stores/counter'
import { createRouter, createWebHistory } from 'vue-router'
import { createPermissionGuard } from './permission-guard'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresAuth: false, title: '登录' },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('@/views/auth/RegisterView.vue'),
      meta: { requiresAuth: false, title: '注册' },
    },
    {
      path: '/',
      name: 'root',
      redirect: '/home',
      meta: { requiresAuth: false },
    },
    {
      path: '/home',
      name: 'home-page',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: false, title: '首页' },
      children: [
        {
          path: '',
          component: () => import('@/views/common/HomeView.vue'),
          meta: { title: '首页' },
        },
      ],
    },

    // 公开展示页面（无需登录）
    {
      path: '/app/models',
      name: 'public-models',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: false, title: 'AI模型榜单' },
      children: [
        {
          path: '',
          component: () => import('@/views/models/ModelListView.vue'),
          meta: { title: 'AI模型榜单' },
        },
      ],
    },
    {
      path: '/app/evaluations',
      name: 'public-evaluations',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: false, title: '评测结果' },
      children: [
        {
          path: '',
          component: () => import('@/views/evaluations/EvaluationResultsView.vue'),
          meta: { title: '评测结果' },
        },
      ],
    },

    {
      path: '/app',
      redirect: '/home',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'tasks',
          name: 'tasks',
          component: () => import('@/views/evaluations/TaskManagementView.vue'),
          meta: { title: '评测任务' },
        },
        // 数据集管理模块
        {
          path: 'datasets',
          name: 'dataset-overview',
          component: () => import('@/views/datasets/DatasetOverviewView.vue'),
          meta: { title: '数据集管理' },
        },
        {
          path: 'datasets/list',
          name: 'dataset-list',
          component: () => import('@/views/datasets/DatasetView.vue'),
          meta: { title: '数据集列表' },
        },
        {
          path: 'datasets/create',
          name: 'dataset-create',
          component: () => import('@/views/datasets/DatasetCreateView.vue'),
          meta: { title: '创建数据集' },
        },
        {
          path: 'datasets/:id/edit',
          name: 'dataset-edit',
          component: () => import('@/views/datasets/DatasetEditView.vue'),
          meta: { title: '编辑数据集' },
        },
        {
          path: 'datasets/:id',
          name: 'dataset-detail',
          component: () => import('@/views/datasets/DatasetDetailView.vue'),
          meta: { title: '数据集详情' },
        },
        // 数据集审核模块
        {
          path: 'dataset-reviews',
          name: 'dataset-review-overview',
          component: () => import('@/views/reviews/DatasetReviewOverviewView.vue'),
          meta: { title: '数据集审核' },
        },
        {
          path: 'dataset-reviews/list',
          name: 'dataset-review-list',
          component: () => import('@/views/reviews/DatasetReviewListView.vue'),
          meta: { title: '审核列表' },
        },
        {
          path: 'dataset-reviews/history',
          name: 'dataset-review-history',
          component: () => import('@/views/reviews/DatasetReviewHistoryView.vue'),
          meta: { title: '审核历史' },
        },
        {
          path: 'help',
          name: 'help',
          component: () => import('@/views/common/HelpView.vue'),
          meta: { title: '帮助' },
        },
        {
          path: 'dict-test',
          name: 'dict-test',
          component: () => import('@/views/test/DictTestView.vue'),
          meta: { title: '字典测试' },
        },

        // 系统管理模块
        {
          path: 'system',
          name: 'system',
          component: () => import('@/views/system/SystemManagementView.vue'),
          meta: { title: '系统管理' },
        },
        {
          path: 'profile',
          name: 'profile',
          redirect: '/app/settings',
          meta: { title: '个人资料' },
        },
        {
          path: 'settings',
          name: 'settings',
          component: () => import('@/views/user/SettingsView.vue'),
          meta: { title: '账户设置' },
        },
      ],
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 初始化用户信息
  if (!userStore.user && localStorage.getItem('user')) {
    await userStore.initUser()
  }

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth === true)

  // 公开路由（不需要登录）
  const publicRoutes = ['/login', '/register', '/home', '/', '/app/models', '/app/evaluations']

  // 如果是公开路由或路由明确设置不需要认证，直接通过
  if (publicRoutes.includes(to.path) || !requiresAuth) {
    // 如果已登录用户访问登录页，重定向到首页
    if (to.path === '/login' && userStore.isLoggedIn) {
      next('/home')
    } else {
      next()
    }
    return
  }

  // 对于需要认证的路由，检查登录状态
  if (!userStore.isLoggedIn) {
    next('/login')
  } else {
    next()
  }
})

// 添加权限守卫
router.beforeEach(createPermissionGuard())

export default router
