// 测试辅助函数
import type { Dataset, DatasetStatus, DatasetType } from '@/types'

// 模拟数据生成器
export const generateMockDataset = (type: DatasetType, status: DatasetStatus = 1): Dataset => {
  const baseDataset = {
    id: Math.floor(Math.random() * 1000),
    title: `测试数据集 ${Math.floor(Math.random() * 100)}`,
    description: '这是一个测试用的数据集描述',
    dataset_type: type,
    dataset_status: status,
    creator_id: 1,
    tags: ['测试', '示例'],
    extra_metadata: {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }

  // 根据类型生成不同的数据内容
  switch (type) {
    case 1: // 综合性病例
      return {
        ...baseDataset,
        data: {
          animal_type_id: 1,
          age_months: 24,
          weight_kg: 15.5,
          symptoms: '发热、咳嗽、流鼻涕',
          diagnosis: '犬瘟热',
          treatment: '支持疗法，抗病毒治疗',
          outcome: '康复',
        },
      }

    case 2: // 影像数据集
      return {
        ...baseDataset,
        data: {
          system_type_id: 1,
          image_category_id: 1,
          detailed_category_id: 2,
          animal_breed_id: 1,
          image_signs: '肺部阴影',
          detailed_description: '正位胸部X光片',
          diagnosis_suggestion: '建议进一步检查',
          image_files: 'https://example.com/image.jpg',
        } as any,
      }

    case 3: // 考试题目
      return {
        ...baseDataset,
        data: {
          questions: [
            {
              question_type_id: 1,
              subject_id: 1,
              difficulty_level_id: 2,
              question_title: '犬瘟热症状',
              question_description: '关于犬瘟热的症状',
              question_content: '犬瘟热的主要症状是什么？',
              options: {
                A: '发热',
                B: '咳嗽',
                C: '流鼻涕',
                D: '以上都是',
              },
              correct_answer: {
                answer: 'D',
                type: 'single' as const,
              },
              answer_explanation: '犬瘟热的主要症状包括发热、咳嗽、流鼻涕等',
            },
          ],
        } as any,
      }

    default:
      throw new Error(`不支持的数据集类型: ${type}`)
  }
}

// 数据验证函数
export const validateDatasetData = (type: DatasetType, data: any): boolean => {
  switch (type) {
    case 1: // 综合性病例
      return !!(
        data.animal_type_id &&
        data.age_months &&
        data.symptoms &&
        data.diagnosis &&
        data.treatment
      )

    case 2: // 影像数据集
      return !!(data.animal_type_id && data.image_type_id && data.body_part && data.findings)

    case 3: // 考试题目
      return !!(
        data.question_type_id &&
        data.subject_id &&
        data.question_text &&
        data.options &&
        data.correct_answer &&
        data.explanation
      )

    default:
      return false
  }
}

// 格式化辅助函数
export const formatters = {
  date: (dateStr: string) => {
    if (!dateStr) return '暂无'
    return new Date(dateStr).toLocaleDateString()
  },

  datetime: (dateStr: string) => {
    if (!dateStr) return '暂无'
    return new Date(dateStr).toLocaleString()
  },

  datasetType: (type: DatasetType) => {
    const labels: Record<DatasetType, string> = {
      [1]: '综合性病例',
      [2]: '影像数据集',
      [3]: '考试题目',
    }
    return labels[type] || '未知类型'
  },

  datasetStatus: (status: DatasetStatus) => {
    const labels: Record<DatasetStatus, string> = {
      [1]: '草稿',
      [2]: '待审核',
      [3]: '已通过',
      [4]: '已拒绝',
      [5]: '已归档',
    }
    return labels[status] || '未知状态'
  },
}

// 常量定义
export const DATASET_CONSTANTS = {
  TYPES: {
    COMPREHENSIVE_CASE: 1 as DatasetType,
    MEDICAL_IMAGING: 2 as DatasetType,
    EXAM_QUESTIONS: 3 as DatasetType,
  },

  STATUSES: {
    DRAFT: 1 as DatasetStatus,
    PENDING: 2 as DatasetStatus,
    APPROVED: 3 as DatasetStatus,
    REJECTED: 4 as DatasetStatus,
    ARCHIVED: 5 as DatasetStatus,
  },

  TAG_COLORS: {
    [1]: 'primary',
    [2]: 'success',
    [3]: 'warning',
  },

  STATUS_COLORS: {
    [1]: 'info',
    [2]: 'warning',
    [3]: 'success',
    [4]: 'danger',
    [5]: '',
  },
}
