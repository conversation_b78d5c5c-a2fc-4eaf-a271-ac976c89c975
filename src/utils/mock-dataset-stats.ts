// 模拟数据集统计数据，用于测试
import type { DatasetStats } from '@/types'

export const mockDatasetStats: DatasetStats = {
  total_datasets: 100,
  by_type: {
    "1": 40,
    "2": 35,
    "3": 25
  },
  by_status: {
    "1": 10,  // 草稿
    "2": 15,  // 待审核
    "3": 60,  // 已通过
    "4": 10,  // 已拒绝
    "5": 5    // 已归档
  },
  my_datasets: 20,
  type_details: {
    "1": {
      count: 40,
      last_updated: "2024-01-20T10:30:00Z",
      contributors: 8
    },
    "2": {
      count: 35,
      last_updated: "2024-01-18T15:45:00Z",
      contributors: 12
    },
    "3": {
      count: 25,
      last_updated: "2024-01-22T09:15:00Z",
      contributors: 6
    }
  }
}

// 用于开发测试的函数
export const useDatasetStatsMock = () => {
  return {
    getDatasetStats: () => Promise.resolve({
      success: true,
      data: mockDatasetStats
    })
  }
}
