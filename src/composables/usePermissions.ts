import { ref, computed } from 'vue'
import { permissionApi } from '@/api/permissions'
import { useUserStore } from '@/stores/counter'
import { PERMISSIONS, type PermissionKey } from '@/types'

// 权限管理的组合式API
export function usePermissions() {
  const userStore = useUserStore()

  // 使用store中的权限数据
  const userPermissions = computed(() => userStore.permissions)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 加载用户权限（委托给store）
  const loadUserPermissions = async () => {
    loading.value = true
    error.value = null
    try {
      await userStore.loadUserPermissions()
    } catch (err) {
      console.error('加载用户权限失败:', err)
      error.value = '加载权限失败'
    } finally {
      loading.value = false
    }
  }

  // 检查是否有指定权限
  const hasPermission = (permission: PermissionKey): boolean => {
    if (!userPermissions.value || !Array.isArray(userPermissions.value)) {
      return false
    }
    // 修复：后端返回的是完整的权限对象数组，而前端需要检查权限名称
    if (typeof userPermissions.value[0] === 'object' && userPermissions.value[0] !== null) {
      // 如果是对象数组，检查对象的name属性
      return userPermissions.value.some((perm: { name: string }) => perm.name === permission)
    }
    // 原有逻辑：如果是字符串数组，直接检查
    return userPermissions.value.includes(permission)
  }

  // 检查是否有任意一个权限
  const hasAnyPermission = (permissions: PermissionKey[]): boolean => {
    if (!userPermissions.value || !Array.isArray(userPermissions.value)) {
      return false
    }
    return permissions.some(permission => hasPermission(permission))
  }

  // 检查是否有所有权限
  const hasAllPermissions = (permissions: PermissionKey[]): boolean => {
    if (!userPermissions.value || !Array.isArray(userPermissions.value)) {
      return false
    }
    return permissions.every(permission => hasPermission(permission))
  }

  // 检查资源权限
  const hasResourcePermission = (resource: string, action: string): boolean => {
    const permission = `${resource}:${action}` as PermissionKey
    return hasPermission(permission)
  }

  // 异步检查权限（通过API）
  const checkPermissionAsync = async (resource: string, action: string): Promise<boolean> => {
    try {
      const response = await permissionApi.checkPermission(resource, action)
      return response.success && response.data.has_permission
    } catch (err) {
      console.error('检查权限失败:', err)
      return false
    }
  }

  // 计算属性：各种权限检查
  const permissions = computed(() => ({
    // 用户管理权限
    canReadUsers: hasPermission(PERMISSIONS.USER_READ),
    canWriteUsers: hasPermission(PERMISSIONS.USER_WRITE),
    canDeleteUsers: hasPermission(PERMISSIONS.USER_DELETE),
    canCreateUsers: hasPermission(PERMISSIONS.USER_CREATE),
    canManageUsers: hasAnyPermission([
      PERMISSIONS.USER_READ,
      PERMISSIONS.USER_WRITE,
      PERMISSIONS.USER_DELETE,
      PERMISSIONS.USER_CREATE
    ]),

    // 角色管理权限
    canReadRoles: hasPermission(PERMISSIONS.ROLE_READ),
    canWriteRoles: hasPermission(PERMISSIONS.ROLE_WRITE),
    canDeleteRoles: hasPermission(PERMISSIONS.ROLE_DELETE),
    canCreateRoles: hasPermission(PERMISSIONS.ROLE_CREATE),
    canManageRoles: hasAnyPermission([
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.ROLE_WRITE,
      PERMISSIONS.ROLE_DELETE,
      PERMISSIONS.ROLE_CREATE
    ]),

    // 权限管理权限
    canReadPermissions: hasPermission(PERMISSIONS.PERMISSION_READ),
    canWritePermissions: hasPermission(PERMISSIONS.PERMISSION_WRITE),
    canDeletePermissions: hasPermission(PERMISSIONS.PERMISSION_DELETE),
    canCreatePermissions: hasPermission(PERMISSIONS.PERMISSION_CREATE),
    canManagePermissions: hasAnyPermission([
      PERMISSIONS.PERMISSION_READ,
      PERMISSIONS.PERMISSION_WRITE,
      PERMISSIONS.PERMISSION_DELETE,
      PERMISSIONS.PERMISSION_CREATE
    ]),

    // 数据集权限
    canReadDatasets: hasPermission(PERMISSIONS.DATASET_READ),
    canWriteDatasets: hasPermission(PERMISSIONS.DATASET_WRITE),
    canDeleteDatasets: hasPermission(PERMISSIONS.DATASET_DELETE),
    canReviewDatasets: hasPermission(PERMISSIONS.DATASET_REVIEW),
    canViewDatasetStats: hasPermission(PERMISSIONS.DATASET_STATS),
    canManageDatasets: hasAnyPermission([
      PERMISSIONS.DATASET_READ,
      PERMISSIONS.DATASET_WRITE,
      PERMISSIONS.DATASET_DELETE
    ]),

    // 评估权限
    canReadEvaluations: hasPermission(PERMISSIONS.EVALUATION_READ),
    canWriteEvaluations: hasPermission(PERMISSIONS.EVALUATION_WRITE),
    canDeleteEvaluations: hasPermission(PERMISSIONS.EVALUATION_DELETE),
    canCreateEvaluations: hasPermission(PERMISSIONS.EVALUATION_CREATE),
    canParticipateEvaluations: hasPermission(PERMISSIONS.EVALUATION_PARTICIPATE),
    canManageEvaluations: hasAnyPermission([
      PERMISSIONS.EVALUATION_READ,
      PERMISSIONS.EVALUATION_WRITE,
      PERMISSIONS.EVALUATION_DELETE,
      PERMISSIONS.EVALUATION_CREATE
    ]),

    // 知识库权限
    canReadKnowledge: hasPermission(PERMISSIONS.KNOWLEDGE_READ),
    canWriteKnowledge: hasPermission(PERMISSIONS.KNOWLEDGE_WRITE),
    canDeleteKnowledge: hasPermission(PERMISSIONS.KNOWLEDGE_DELETE),
    canCreateKnowledge: hasPermission(PERMISSIONS.KNOWLEDGE_CREATE),
    canManageKnowledge: hasAnyPermission([
      PERMISSIONS.KNOWLEDGE_READ,
      PERMISSIONS.KNOWLEDGE_WRITE,
      PERMISSIONS.KNOWLEDGE_DELETE,
      PERMISSIONS.KNOWLEDGE_CREATE
    ]),

    // 数据字典权限
    canReadDataDict: hasPermission(PERMISSIONS.DATA_DICT_READ),
    canWriteDataDict: hasPermission(PERMISSIONS.DATA_DICT_WRITE),
    canDeleteDataDict: hasPermission(PERMISSIONS.DATA_DICT_DELETE),
    canManageDataDict: hasAnyPermission([
      PERMISSIONS.DATA_DICT_READ,
      PERMISSIONS.DATA_DICT_WRITE,
      PERMISSIONS.DATA_DICT_DELETE
    ]),

    // 系统管理权限
    isSystemAdmin: hasPermission(PERMISSIONS.SYSTEM_ADMIN),

    // 综合权限检查
    canAccessSystemManagement: hasAnyPermission([
      PERMISSIONS.SYSTEM_ADMIN,
      PERMISSIONS.USER_READ,
      PERMISSIONS.ROLE_READ,
      PERMISSIONS.PERMISSION_READ
    ])
  }))

  // 不需要在mounted时加载权限，因为store会在登录时加载

  return {
    // 状态
    userPermissions,
    loading,
    error,

    // 方法
    loadUserPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasResourcePermission,
    checkPermissionAsync,

    // 计算属性
    permissions
  }
}

// 权限指令工厂函数
export function createPermissionDirective() {
  return {
    mounted(el: HTMLElement, binding: { value: PermissionKey | PermissionKey[] }) {
      const { hasAnyPermission } = usePermissions()

      const checkPermission = () => {
        const permissions = Array.isArray(binding.value) ? binding.value : [binding.value]
        const hasAccess = hasAnyPermission(permissions)

        if (!hasAccess) {
          el.style.display = 'none'
        }
      }

      checkPermission()
    },

    updated(el: HTMLElement, binding: { value: PermissionKey | PermissionKey[] }) {
      const { hasAnyPermission } = usePermissions()

      const checkPermission = () => {
        const permissions = Array.isArray(binding.value) ? binding.value : [binding.value]
        const hasAccess = hasAnyPermission(permissions)

        el.style.display = hasAccess ? '' : 'none'
      }

      checkPermission()
    }
  }
}