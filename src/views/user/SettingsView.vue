<template>
  <div class="user-settings content-container">
    <!-- 现代化头部区域 -->
    <div class="settings-header">
      <div class="header-background"></div>
      <div class="header-content">
        <div class="avatar-container">
          <div class="avatar-wrapper">
            <el-avatar :size="80" class="user-avatar">
              {{ userStore.user?.username?.charAt(0).toUpperCase() || 'U' }}
            </el-avatar>
            <div class="avatar-badge">
              <el-icon class="badge-icon"><User /></el-icon>
            </div>
          </div>
        </div>
        <div class="user-info">
          <h1 class="user-name">
            {{ userStore.user?.full_name || userStore.user?.username || '未知用户' }}
          </h1>
          <p class="user-title">{{ getUserSourceLabel(userStore.user?.user_source) }}</p>
          <div class="user-meta">
            <span class="meta-item">
              <el-icon><Message /></el-icon>
              {{ userStore.user?.email || '未设置邮箱' }}
            </span>
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              注册于 {{ formatDate(userStore.user?.created_at, 'date') }}
            </span>
          </div>
        </div>
        <div class="header-actions">
          <el-tag
            :type="userStore.user?.is_active ? 'success' : 'danger'"
            class="status-tag"
            effect="light"
            size="large"
          >
            <el-icon
              ><CircleCheck v-if="userStore.user?.is_active" /><CircleClose v-else
            /></el-icon>
            {{ userStore.user?.is_active ? '账户激活' : '账户禁用' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon permissions">
          <el-icon><Key /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ userPermissions.length }}</div>
          <div class="stat-label">拥有权限</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon tasks">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">0</div>
          <div class="stat-label">创建任务</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon evaluations">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">0</div>
          <div class="stat-label">参与评测</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon days">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ getDaysFromRegistration() }}</div>
          <div class="stat-label">注册天数</div>
        </div>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="settings-container">
      <el-tabs v-model="activeTab" class="settings-tabs" type="card">
        <!-- 个人资料标签页 -->
        <el-tab-pane label="个人资料" name="profile">
          <template #label>
            <div class="tab-label">
              <el-icon><User /></el-icon>
              <span>个人资料</span>
            </div>
          </template>

          <div class="tab-content">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><Setting /></el-icon>
                基本信息
              </h3>
              <p class="section-subtitle">管理您的个人信息和账户设置</p>
            </div>

            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              class="profile-form"
              :disabled="!editMode"
            >
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">
                    <el-icon><User /></el-icon>
                    用户名
                  </label>
                  <el-form-item prop="username">
                    <el-input
                      v-model="profileForm.username"
                      placeholder="请输入用户名"
                      size="large"
                      class="form-input"
                    />
                  </el-form-item>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <el-icon><Message /></el-icon>
                    邮箱地址
                  </label>
                  <el-form-item prop="email">
                    <el-input
                      v-model="profileForm.email"
                      placeholder="请输入邮箱地址"
                      size="large"
                      class="form-input"
                    />
                  </el-form-item>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <el-icon><Avatar /></el-icon>
                    真实姓名
                  </label>
                  <el-form-item prop="full_name">
                    <el-input
                      v-model="profileForm.full_name"
                      placeholder="请输入真实姓名"
                      size="large"
                      class="form-input"
                    />
                  </el-form-item>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <el-icon><OfficeBuilding /></el-icon>
                    用户类型
                  </label>
                  <el-form-item prop="user_source">
                    <el-select
                      v-model="profileForm.user_source"
                      placeholder="请选择用户类型"
                      size="large"
                      class="form-input"
                    >
                      <el-option
                        v-for="option in userSourceOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>

                <div class="form-group readonly">
                  <label class="form-label">
                    <el-icon><Calendar /></el-icon>
                    注册时间
                  </label>
                  <el-input
                    :value="formatDate(userStore.user?.created_at)"
                    readonly
                    size="large"
                    class="form-input readonly-input"
                  />
                </div>

                <div class="form-group readonly">
                  <label class="form-label">
                    <el-icon><Clock /></el-icon>
                    最后更新
                  </label>
                  <el-input
                    :value="formatDate(userStore.user?.updated_at)"
                    readonly
                    size="large"
                    class="form-input readonly-input"
                  />
                </div>
              </div>

              <div class="form-actions">
                <el-button
                  v-if="!editMode"
                  type="primary"
                  @click="editMode = true"
                  size="large"
                  class="edit-btn"
                >
                  <el-icon><Edit /></el-icon>
                  编辑资料
                </el-button>
                <template v-else>
                  <el-button
                    type="primary"
                    @click="saveProfile"
                    :loading="saving"
                    size="large"
                    class="save-btn"
                  >
                    <el-icon><Check /></el-icon>
                    保存修改
                  </el-button>
                  <el-button @click="cancelEdit" size="large" class="cancel-btn">
                    <el-icon><Close /></el-icon>
                    取消
                  </el-button>
                </template>
              </div>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 安全设置标签页 -->
        <el-tab-pane label="安全设置" name="security">
          <template #label>
            <div class="tab-label">
              <el-icon><Lock /></el-icon>
              <span>安全设置</span>
            </div>
          </template>

          <div class="tab-content">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><Lock /></el-icon>
                密码管理
              </h3>
              <p class="section-subtitle">修改您的登录密码以保护账户安全</p>
            </div>

            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              class="password-form"
            >
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">
                    <el-icon><Lock /></el-icon>
                    当前密码
                  </label>
                  <el-form-item prop="currentPassword">
                    <el-input
                      v-model="passwordForm.currentPassword"
                      type="password"
                      placeholder="请输入当前密码"
                      show-password
                      size="large"
                      class="form-input"
                    />
                  </el-form-item>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <el-icon><Lock /></el-icon>
                    新密码
                  </label>
                  <el-form-item prop="newPassword">
                    <el-input
                      v-model="passwordForm.newPassword"
                      type="password"
                      placeholder="请输入新密码"
                      show-password
                      size="large"
                      class="form-input"
                    />
                  </el-form-item>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <el-icon><Lock /></el-icon>
                    确认新密码
                  </label>
                  <el-form-item prop="confirmNewPassword">
                    <el-input
                      v-model="passwordForm.confirmNewPassword"
                      type="password"
                      placeholder="请再次输入新密码"
                      show-password
                      size="large"
                      class="form-input"
                    />
                  </el-form-item>
                </div>
              </div>

              <div class="form-actions">
                <el-button
                  type="primary"
                  @click="changePassword"
                  :loading="changingPassword"
                  size="large"
                  class="save-btn"
                >
                  <el-icon><Check /></el-icon>
                  修改密码
                </el-button>
                <el-button @click="resetPasswordForm" size="large" class="reset-btn">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
              </div>
            </el-form>

            <!-- 安全提示 -->
            <div class="security-tips">
              <div class="tips-header">
                <el-icon class="tips-icon"><InfoFilled /></el-icon>
                <span class="tips-title">密码安全建议</span>
              </div>
              <ul class="tips-list">
                <li>密码长度至少6个字符</li>
                <li>建议包含大小写字母、数字和特殊字符</li>
                <li>不要使用容易猜测的密码</li>
                <li>定期更换密码以保护账户安全</li>
              </ul>
            </div>
          </div>
        </el-tab-pane>

        <!-- 系统偏好标签页 -->
        <el-tab-pane label="系统偏好" name="preferences">
          <template #label>
            <div class="tab-label">
              <el-icon><Setting /></el-icon>
              <span>系统偏好</span>
            </div>
          </template>

          <div class="tab-content">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><Setting /></el-icon>
                个性化设置
              </h3>
              <p class="section-subtitle">自定义您的使用体验</p>
            </div>

            <div class="preferences-grid">
              <div class="preference-card">
                <div class="preference-header">
                  <el-icon class="preference-icon"><Monitor /></el-icon>
                  <div class="preference-info">
                    <h4 class="preference-title">主题设置</h4>
                    <p class="preference-desc">选择您喜欢的界面主题</p>
                  </div>
                </div>
                <div class="preference-control">
                  <el-radio-group v-model="preferences.theme" size="large">
                    <el-radio-button label="light">浅色</el-radio-button>
                    <el-radio-button label="dark">深色</el-radio-button>
                    <el-radio-button label="auto">自动</el-radio-button>
                  </el-radio-group>
                </div>
              </div>

              <div class="preference-card">
                <div class="preference-header">
                  <el-icon class="preference-icon"><Bell /></el-icon>
                  <div class="preference-info">
                    <h4 class="preference-title">通知设置</h4>
                    <p class="preference-desc">管理系统通知偏好</p>
                  </div>
                </div>
                <div class="preference-control">
                  <el-switch
                    v-model="preferences.notifications"
                    size="large"
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </div>
              </div>

              <div class="preference-card">
                <div class="preference-header">
                  <el-icon class="preference-icon"><Setting /></el-icon>
                  <div class="preference-info">
                    <h4 class="preference-title">语言设置</h4>
                    <p class="preference-desc">选择界面显示语言</p>
                  </div>
                </div>
                <div class="preference-control">
                  <el-select v-model="preferences.language" size="large" style="width: 120px">
                    <el-option label="中文" value="zh-CN" />
                    <el-option label="English" value="en-US" />
                  </el-select>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <el-button
                type="primary"
                @click="savePreferences"
                :loading="savingPreferences"
                size="large"
                class="save-btn"
              >
                <el-icon><Check /></el-icon>
                保存设置
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UpdateUserRequest } from '@/api/users'
import { updateUser, userSourceOptions } from '@/api/users'
import { useUserStore } from '@/stores/counter'
import { showError, showSuccess } from '@/utils'
import {
  Avatar,
  Bell,
  Calendar,
  Check,
  CircleCheck,
  CircleClose,
  Clock,
  Close,
  DataAnalysis,
  Document,
  Edit,
  InfoFilled,
  Key,
  Lock,
  Message,
  Monitor,
  OfficeBuilding,
  RefreshLeft,
  Setting,
  Timer,
  User,
} from '@element-plus/icons-vue'
import { type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

const userStore = useUserStore()

// 响应式数据
const activeTab = ref('profile')
const editMode = ref(false)
const saving = ref(false)
const changingPassword = ref(false)
const savingPreferences = ref(false)
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 表单数据
const profileForm = reactive({
  username: '',
  email: '',
  full_name: '',
  user_source: 'individual' as 'enterprise' | 'individual' | 'school' | 'hospital' | 'government',
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmNewPassword: '',
})

const preferences = reactive({
  theme: 'light',
  notifications: true,
  language: 'zh-CN',
})

// 表单验证规则
const validateConfirmPassword = (_rule: any, value: string, callback: any) => {
  if (passwordForm.newPassword && value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const profileRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  full_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
}

const passwordRules: FormRules = {
  currentPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' },
  ],
  confirmNewPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' },
  ],
}

// 计算属性
const userPermissions = computed(() => userStore.permissions || [])

// 方法
const getUserSourceLabel = (source?: string) => {
  if (!source) return '未设置'
  const option = userSourceOptions.find((opt) => opt.value === source)
  return option?.label || source
}

const initForm = () => {
  if (userStore.user) {
    Object.assign(profileForm, {
      username: userStore.user.username,
      email: userStore.user.email,
      full_name: userStore.user.full_name,
      user_source: userStore.user.user_source,
    })
  }
}

const saveProfile = async () => {
  if (!profileFormRef.value || !userStore.user) return

  try {
    await profileFormRef.value.validate()
    saving.value = true

    const updateData: UpdateUserRequest = {
      username: profileForm.username,
      email: profileForm.email,
      full_name: profileForm.full_name,
      user_source: profileForm.user_source,
    }

    const updatedUser = await updateUser(userStore.user.id, updateData)

    // 更新本地用户信息
    userStore.updateUserInfo(updatedUser)

    showSuccess('个人资料更新成功')
    editMode.value = false
  } catch (error: any) {
    showError(error.message || '更新失败')
  } finally {
    saving.value = false
  }
}

const cancelEdit = () => {
  editMode.value = false
  initForm()
  profileFormRef.value?.resetFields()
}

const changePassword = async () => {
  if (!passwordFormRef.value || !userStore.user) return

  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true

    // 这里应该调用修改密码的API
    // await changeUserPassword(userStore.user.id, {
    //   currentPassword: passwordForm.currentPassword,
    //   newPassword: passwordForm.newPassword,
    // })

    showSuccess('密码修改成功')
    resetPasswordForm()
  } catch (error: any) {
    showError(error.message || '密码修改失败')
  } finally {
    changingPassword.value = false
  }
}

const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  })
  passwordFormRef.value?.resetFields()
}

const savePreferences = async () => {
  try {
    savingPreferences.value = true

    // 这里应该调用保存偏好设置的API
    // await saveUserPreferences(preferences)

    showSuccess('偏好设置保存成功')
  } catch (error: any) {
    showError(error.message || '保存失败')
  } finally {
    savingPreferences.value = false
  }
}

const formatDate = (dateString?: string, type: 'full' | 'date' = 'full') => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (type === 'date') {
    return date.toLocaleDateString('zh-CN')
  }
  return date.toLocaleString('zh-CN')
}

const getDaysFromRegistration = () => {
  if (!userStore.user?.created_at) return 0
  const registrationDate = new Date(userStore.user.created_at)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - registrationDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// 生命周期
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.user-settings {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

/* 现代化头部样式 */
.settings-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 32px;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  padding: 32px;
  gap: 24px;
}

.avatar-container {
  flex-shrink: 0;
}

.avatar-wrapper {
  position: relative;
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  font-size: 28px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 24px;
  height: 24px;
  background: #67c23a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.badge-icon {
  color: white;
  font-size: 12px;
}

.user-info {
  flex: 1;
  color: white;
}

.user-name {
  margin: 0 0 6px 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.user-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  flex-shrink: 0;
}

.status-tag {
  font-weight: 500;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.stat-icon.permissions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.evaluations {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.days {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 13px;
  color: #909399;
  font-weight: 500;
}

/* 设置容器样式 */
.settings-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.settings-tabs {
  --el-tabs-header-height: 60px;
}

.settings-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
}

.settings-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 32px;
}

.settings-tabs :deep(.el-tabs__item) {
  height: 60px;
  line-height: 60px;
  font-weight: 500;
  color: #606266;
  border: none;
  background: transparent;
}

.settings-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  background: white;
  border-radius: 8px 8px 0 0;
  margin-bottom: -1px;
  border: 1px solid #e4e7ed;
  border-bottom: 1px solid white;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-content {
  padding: 32px;
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 表单样式 */
.profile-form,
.password-form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.readonly {
  opacity: 0.7;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-input {
  width: 100%;
}

.readonly-input {
  background-color: #f5f7fa !important;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.edit-btn,
.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.edit-btn:hover,
.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.cancel-btn,
.reset-btn {
  border: 2px solid #dcdfe6;
  background: white;
  color: #606266;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn:hover,
.reset-btn:hover {
  border-color: #c0c4cc;
  transform: translateY(-2px);
}

/* 安全提示样式 */
.security-tips {
  margin-top: 32px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.tips-icon {
  color: #0ea5e9;
  font-size: 18px;
}

.tips-title {
  font-weight: 600;
  color: #0c4a6e;
  font-size: 16px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
  color: #0369a1;
}

.tips-list li {
  margin-bottom: 6px;
  font-size: 14px;
}

/* 偏好设置样式 */
.preferences-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 32px;
}

.preference-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.preference-card:hover {
  background: #f1f3f4;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preference-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preference-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.preference-info {
  flex: 1;
}

.preference-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preference-desc {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.preference-control {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1240px) {
  .user-settings {
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .user-settings {
    padding: 0 12px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    padding: 24px;
    gap: 20px;
  }

  .user-meta {
    flex-direction: column;
    gap: 8px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tab-content {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .preferences-grid {
    gap: 16px;
  }

  .preference-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .preference-header {
    width: 100%;
  }

  .preference-control {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .user-settings {
    padding: 0 8px;
  }

  .header-content {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
  }

  .settings-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 16px;
  }
}
</style>
