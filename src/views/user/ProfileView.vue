<template>
  <div class="profile-page content-container">
    <!-- 现代化头部区域 -->
    <div class="profile-header">
      <div class="header-background"></div>
      <div class="header-content">
        <div class="avatar-container">
          <div class="avatar-wrapper">
            <el-avatar :size="100" class="user-avatar">
              {{ userStore.user?.username?.charAt(0).toUpperCase() || 'U' }}
            </el-avatar>
            <div class="avatar-badge">
              <el-icon class="badge-icon"><User /></el-icon>
            </div>
          </div>
        </div>
        <div class="user-info">
          <h1 class="user-name">
            {{ userStore.user?.full_name || userStore.user?.username || '未知用户' }}
          </h1>
          <p class="user-title">{{ getUserSourceLabel(userStore.user?.user_source) }}</p>
          <div class="user-meta">
            <span class="meta-item">
              <el-icon><Message /></el-icon>
              {{ userStore.user?.email || '未设置邮箱' }}
            </span>
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              注册于 {{ formatDate(userStore.user?.created_at, 'date') }}
            </span>
          </div>
          <div class="status-container">
            <el-tag
              :type="userStore.user?.is_active ? 'success' : 'danger'"
              class="status-tag"
              effect="light"
            >
              <el-icon
                ><CircleCheck v-if="userStore.user?.is_active" /><CircleClose v-else
              /></el-icon>
              {{ userStore.user?.is_active ? '账户激活' : '账户禁用' }}
            </el-tag>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            :type="editMode ? 'default' : 'primary'"
            @click="editMode = !editMode"
            class="edit-btn"
            size="large"
          >
            <el-icon><Edit /></el-icon>
            {{ editMode ? '取消编辑' : '编辑资料' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon permissions">
          <el-icon><Key /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ userPermissions.length }}</div>
          <div class="stat-label">拥有权限</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon tasks">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">0</div>
          <div class="stat-label">创建任务</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon evaluations">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">0</div>
          <div class="stat-label">参与评测</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon days">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ getDaysFromRegistration() }}</div>
          <div class="stat-label">注册天数</div>
        </div>
      </div>
    </div>

    <!-- 详细信息表单 -->
    <div class="form-container">
      <div class="form-header">
        <h2 class="form-title">
          <el-icon><Setting /></el-icon>
          详细信息
        </h2>
        <p class="form-subtitle">管理您的个人信息和账户设置</p>
      </div>

      <div class="form-content">
        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          class="profile-form"
          :disabled="!editMode"
        >
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">
                <el-icon><User /></el-icon>
                用户名
              </label>
              <el-form-item prop="username">
                <el-input
                  v-model="profileForm.username"
                  placeholder="请输入用户名"
                  size="large"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <div class="form-group">
              <label class="form-label">
                <el-icon><Message /></el-icon>
                邮箱地址
              </label>
              <el-form-item prop="email">
                <el-input
                  v-model="profileForm.email"
                  placeholder="请输入邮箱地址"
                  size="large"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <div class="form-group">
              <label class="form-label">
                <el-icon><Avatar /></el-icon>
                真实姓名
              </label>
              <el-form-item prop="full_name">
                <el-input
                  v-model="profileForm.full_name"
                  placeholder="请输入真实姓名"
                  size="large"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <div class="form-group">
              <label class="form-label">
                <el-icon><OfficeBuilding /></el-icon>
                用户类型
              </label>
              <el-form-item prop="user_source">
                <el-select
                  v-model="profileForm.user_source"
                  placeholder="请选择用户类型"
                  size="large"
                  class="form-input"
                >
                  <el-option
                    v-for="option in userSourceOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-group readonly">
              <label class="form-label">
                <el-icon><Calendar /></el-icon>
                注册时间
              </label>
              <el-input
                :value="formatDate(userStore.user?.created_at)"
                readonly
                size="large"
                class="form-input readonly-input"
              />
            </div>

            <div class="form-group readonly">
              <label class="form-label">
                <el-icon><Clock /></el-icon>
                最后更新
              </label>
              <el-input
                :value="formatDate(userStore.user?.updated_at)"
                readonly
                size="large"
                class="form-input readonly-input"
              />
            </div>

            <div v-if="editMode" class="form-group">
              <label class="form-label">
                <el-icon><Lock /></el-icon>
                新密码
              </label>
              <el-form-item prop="password">
                <el-input
                  v-model="profileForm.password"
                  type="password"
                  placeholder="留空则不修改密码"
                  show-password
                  size="large"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <div v-if="editMode" class="form-group">
              <label class="form-label">
                <el-icon><Lock /></el-icon>
                确认密码
              </label>
              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="profileForm.confirmPassword"
                  type="password"
                  placeholder="请确认新密码"
                  show-password
                  size="large"
                  class="form-input"
                />
              </el-form-item>
            </div>
          </div>

          <div v-if="editMode" class="form-actions">
            <el-button
              type="primary"
              @click="saveProfile"
              :loading="saving"
              size="large"
              class="save-btn"
            >
              <el-icon><Check /></el-icon>
              保存修改
            </el-button>
            <el-button @click="resetForm" size="large" class="reset-btn">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UpdateUserRequest } from '@/api/users'
import { updateUser, userSourceOptions } from '@/api/users'
import { useUserStore } from '@/stores/counter'
import { showError, showSuccess } from '@/utils'
import {
  Avatar,
  Calendar,
  Check,
  CircleCheck,
  CircleClose,
  Clock,
  DataAnalysis,
  Document,
  Edit,
  Key,
  Lock,
  Message,
  OfficeBuilding,
  RefreshLeft,
  Setting,
  Timer,
  User,
} from '@element-plus/icons-vue'
import { type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

const userStore = useUserStore()

// 响应式数据
const editMode = ref(false)
const saving = ref(false)
const profileFormRef = ref<FormInstance>()

// 表单数据
const profileForm = reactive({
  username: '',
  email: '',
  full_name: '',
  user_source: 'individual' as 'enterprise' | 'individual' | 'school' | 'hospital' | 'government',
  password: '',
  confirmPassword: '',
})

// 表单验证规则
const validateConfirmPassword = (_rule: any, value: string, callback: any) => {
  if (profileForm.password && value !== profileForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const profileRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  full_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  password: [{ min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }],
}

// 计算属性
const userPermissions = computed(() => userStore.permissions || [])

// 方法
const getUserSourceLabel = (source?: string) => {
  if (!source) return '未设置'
  const option = userSourceOptions.find((opt) => opt.value === source)
  return option?.label || source
}

const initForm = () => {
  if (userStore.user) {
    Object.assign(profileForm, {
      username: userStore.user.username,
      email: userStore.user.email,
      full_name: userStore.user.full_name,
      user_source: userStore.user.user_source,
      password: '',
      confirmPassword: '',
    })
  }
}

const saveProfile = async () => {
  if (!profileFormRef.value || !userStore.user) return

  try {
    await profileFormRef.value.validate()
    saving.value = true

    const updateData: UpdateUserRequest = {
      username: profileForm.username,
      email: profileForm.email,
      full_name: profileForm.full_name,
      user_source: profileForm.user_source,
    }

    // 只有在输入了新密码时才更新密码
    if (profileForm.password) {
      updateData.password = profileForm.password
    }

    const updatedUser = await updateUser(userStore.user.id, updateData)

    // 更新本地用户信息
    userStore.updateUserInfo(updatedUser)

    showSuccess('个人资料更新成功')
    editMode.value = false
    profileForm.password = ''
    profileForm.confirmPassword = ''
  } catch (error: any) {
    showError(error.message || '更新失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  initForm()
  profileFormRef.value?.resetFields()
}

const formatDate = (dateString?: string, type: 'full' | 'date' = 'full') => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (type === 'date') {
    return date.toLocaleDateString('zh-CN')
  }
  return date.toLocaleString('zh-CN')
}

const getDaysFromRegistration = () => {
  if (!userStore.user?.created_at) return 0
  const registrationDate = new Date(userStore.user.created_at)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - registrationDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// 生命周期
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

/* 现代化头部样式 */
.profile-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 32px;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  padding: 40px;
  gap: 32px;
}

.avatar-container {
  flex-shrink: 0;
}

.avatar-wrapper {
  position: relative;
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 4px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  font-size: 36px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.avatar-badge {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 32px;
  height: 32px;
  background: #67c23a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.badge-icon {
  color: white;
  font-size: 14px;
}

.user-info {
  flex: 1;
  color: white;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.user-meta {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.status-container {
  margin-top: 16px;
}

.status-tag {
  font-weight: 500;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.header-actions {
  flex-shrink: 0;
}

.edit-btn {
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.permissions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.evaluations {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.days {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 表单容器样式 */
.form-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 32px;
  border-bottom: 1px solid #e4e7ed;
}

.form-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.form-content {
  padding: 32px;
}

.profile-form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.readonly {
  opacity: 0.7;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-input {
  width: 100%;
}

.readonly-input {
  background-color: #f5f7fa !important;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 12px 32px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.reset-btn {
  border: 2px solid #dcdfe6;
  background: white;
  color: #606266;
  padding: 12px 32px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  border-color: #c0c4cc;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 1240px) {
  .profile-page {
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .profile-page {
    padding: 0 12px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    padding: 24px;
    gap: 24px;
  }

  .user-meta {
    flex-direction: column;
    gap: 12px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-header,
  .form-content {
    padding: 24px;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .profile-page {
    padding: 0 8px;
  }

  .header-content {
    padding: 16px;
  }

  .form-header,
  .form-content {
    padding: 16px;
  }
}
</style>
