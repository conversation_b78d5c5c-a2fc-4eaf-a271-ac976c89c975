<template>
  <div class="review-list-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">
            {{ getTypeTitle() }}
            <el-tag v-if="currentType" type="warning" class="type-tag"> 待审核 </el-tag>
          </h1>
          <p class="page-subtitle">审核{{ getTypeLabel(currentType) }}数据集</p>
        </div>
        <div class="header-right">
          <el-button @click="navigateToOverview">
            <el-icon><ArrowLeft /></el-icon>
            返回概览
          </el-button>
          <el-button type="primary" @click="navigateToHistory">
            <el-icon><Document /></el-icon>
            审核历史
          </el-button>
        </div>
      </div>

      <!-- 搜索筛选区 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索数据集标题..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-controls">
            <el-select
              v-model="selectedType"
              placeholder="全部类型"
              clearable
              @change="handleTypeChange"
            >
              <el-option label="全部类型" value="" />
              <el-option label="综合性病例" :value="1" />
              <el-option label="影像数据集" :value="2" />
              <el-option label="考试题目" :value="3" />
            </el-select>

            <el-select
              v-model="selectedCreator"
              placeholder="全部创建者"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部创建者" value="" />
            </el-select>

            <el-button @click="handleReset">重置筛选</el-button>
          </div>
        </div>
      </div>

      <!-- 待审核数据集列表 -->
      <div class="review-table">
        <el-table :data="pendingList" stripe v-loading="loading">
          <el-table-column label="数据集信息" min-width="250">
            <template #default="{ row }">
              <div class="dataset-info">
                <div class="dataset-name">{{ row.title }}</div>
                <div class="dataset-desc">{{ row.description }}</div>
                <div class="dataset-tags" v-if="row.tags && row.tags.length">
                  <el-tag v-for="tag in row.tags" :key="tag" size="small" class="tag-item">
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.dataset_type)">
                {{ getTypeLabel(row.dataset_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建者" width="100" prop="creator_name" />

          <el-table-column label="提交时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="提交说明" width="150">
            <template #default="{ row }">
              <div class="submit-comment">{{ row.submit_comment || '无' }}</div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleViewDetail(row)"> 查看详情 </el-button>
              <el-button type="success" @click="handleStartReview(row)"> 开始审核 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <DatasetReviewModal
      v-model:visible="reviewModalVisible"
      :dataset="selectedDataset"
      @review-submitted="handleReviewSubmitted"
    />
  </div>
</template>

<script setup lang="ts">
import { reviewApi } from '@/api'
import DatasetReviewModal from '@/components/DatasetReviewModal.vue'
import type { Dataset, DatasetType } from '@/types'
import { ArrowLeft, Document, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const selectedType = ref<DatasetType | ''>('')
const selectedCreator = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)
const pendingList = ref<Dataset[]>([])

// 审核弹窗相关
const reviewModalVisible = ref(false)
const selectedDataset = ref<Dataset | null>(null)

// 计算属性
const currentType = computed(() => {
  const typeFromQuery = route.query.type
  if (typeFromQuery) {
    return Number(typeFromQuery) as DatasetType
  }
  return selectedType.value || null
})

// 方法
const loadPendingReviews = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      size: pageSize.value,
    }

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    if (currentType.value) {
      params.dataset_type = currentType.value
    }

    if (selectedCreator.value) {
      params.creator_id = selectedCreator.value
    }

    const response = await reviewApi.getPendingReviews(params)
    if (response.success) {
      pendingList.value = response.data.items
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载待审核数据集失败:', error)
    ElMessage.error('加载待审核数据集失败')
  } finally {
    loading.value = false
  }
}

const getTypeTitle = () => {
  if (currentType.value) {
    return `${getTypeLabel(currentType.value)}审核`
  }
  return '数据集审核'
}

const getTypeLabel = (type: DatasetType) => {
  const labels: Record<DatasetType, string> = {
    [1]: '综合性病例',
    [2]: '影像数据集',
    [3]: '考试题目',
  }
  return labels[type] || '未知类型'
}

const getTypeTagType = (type: DatasetType) => {
  const types: Record<DatasetType, string> = {
    [1]: 'primary',
    [2]: 'success',
    [3]: 'warning',
  }
  return types[type] || ''
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleDateString()
}

const navigateToOverview = () => {
  router.push({ name: 'dataset-review-overview' })
}

const navigateToHistory = () => {
  router.push({ name: 'dataset-review-history' })
}

const handleSearch = () => {
  currentPage.value = 1
  loadPendingReviews()
}

const handleTypeChange = () => {
  currentPage.value = 1
  loadPendingReviews()
}

const handleReset = () => {
  searchKeyword.value = ''
  selectedType.value = ''
  selectedCreator.value = ''
  currentPage.value = 1
  loadPendingReviews()
}

const handleViewDetail = (row: Dataset) => {
  router.push({
    name: 'dataset-detail',
    params: { id: row.id },
  })
}

const handleStartReview = (row: Dataset) => {
  selectedDataset.value = row
  reviewModalVisible.value = true
}

const handleReviewSubmitted = () => {
  reviewModalVisible.value = false
  selectedDataset.value = null
  loadPendingReviews()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadPendingReviews()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadPendingReviews()
}

// 监听路由变化
watch(
  () => route.query.type,
  (newType) => {
    if (newType) {
      selectedType.value = Number(newType) as DatasetType
    }
    loadPendingReviews()
  },
  { immediate: true },
)

onMounted(() => {
  if (route.query.type) {
    selectedType.value = Number(route.query.type) as DatasetType
  }
  loadPendingReviews()
})
</script>

<style scoped>
.review-list-page {
  width: 100%;
}

.type-tag {
  margin-left: 8px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 审核表格 */
.review-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.dataset-info {
  padding: 4px 0;
}

.dataset-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 4px;
}

.dataset-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.3;
  margin-bottom: 8px;
}

.dataset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  margin: 0;
}

.submit-comment {
  font-size: 12px;
  color: #6b7280;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-controls {
    flex-wrap: wrap;
  }
}
</style>
