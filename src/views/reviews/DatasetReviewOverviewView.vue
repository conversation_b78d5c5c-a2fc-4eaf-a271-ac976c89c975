<template>
  <div class="review-overview content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-main">
            <h1 class="page-title">数据集审核</h1>
            <p class="page-description">审核和管理待审核的数据集</p>
          </div>
        </div>
      </div>

      <!-- 审核统计卡片 -->
      <div class="stats-cards">
        <div class="stats-card pending">
          <div class="card-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">待审核</div>
            <div class="card-value">{{ stats.pending_count || 0 }}</div>
            <div class="card-desc">等待审核的数据集</div>
          </div>
        </div>

        <div class="stats-card approved">
          <div class="card-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">已通过</div>
            <div class="card-value">{{ stats.approved_count || 0 }}</div>
            <div class="card-desc">审核通过的数据集</div>
          </div>
        </div>

        <div class="stats-card rejected">
          <div class="card-icon">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">已拒绝</div>
            <div class="card-value">{{ stats.rejected_count || 0 }}</div>
            <div class="card-desc">审核拒绝的数据集</div>
          </div>
        </div>

        <div class="stats-card my-reviews">
          <div class="card-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">我的审核</div>
            <div class="card-value">{{ stats.my_reviews_count || 0 }}</div>
            <div class="card-desc">我审核的数据集</div>
          </div>
        </div>
      </div>

      <!-- 数据集类型审核卡片 -->
      <div class="review-types">
        <h2 class="review-type-tile">按类型审核</h2>
        <div class="type-cards">
          <!-- 综合性病例数据集 -->
          <div class="type-card" @click="navigateToReviewList(1)">
            <div class="card-header">
              <div class="card-icon-large comprehensive">
                <el-icon><Document /></el-icon>
              </div>
              <div class="pending-badge" v-if="getPendingCountByType(1) > 0">
                {{ getPendingCountByType(1) }}
              </div>
            </div>
            <div class="card-body">
              <h3 class="card-title">综合性病例数据集</h3>
              <p class="card-description">
                审核包含动物类型、症状、诊断、治疗等完整病例信息的数据集
              </p>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">待审核</span>
                  <span class="stat-value">{{ getPendingCountByType(1) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">最近审核</span>
                  <span class="stat-value">{{ formatDate(getLastReviewTime(1)) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 影像数据集 -->
          <div class="type-card" @click="navigateToReviewList(2)">
            <div class="card-header">
              <div class="card-icon-large imaging">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="pending-badge" v-if="getPendingCountByType(2) > 0">
                {{ getPendingCountByType(2) }}
              </div>
            </div>
            <div class="card-body">
              <h3 class="card-title">医学影像数据集</h3>
              <p class="card-description">
                审核包含X光片、CT、MRI等各类医学影像及其诊断结果的数据集
              </p>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">待审核</span>
                  <span class="stat-value">{{ getPendingCountByType(2) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">最近审核</span>
                  <span class="stat-value">{{ formatDate(getLastReviewTime(2)) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 考试题目数据集 -->
          <div class="type-card" @click="navigateToReviewList(3)">
            <div class="card-header">
              <div class="card-icon-large exam">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="pending-badge" v-if="getPendingCountByType(3) > 0">
                {{ getPendingCountByType(3) }}
              </div>
            </div>
            <div class="card-body">
              <h3 class="card-title">考试题目数据集</h3>
              <p class="card-description">
                审核包含各类兽医专业考试题目、答案及解析的教育评估数据集
              </p>
              <div class="card-stats">
                <div class="stat-item">
                  <span class="stat-label">待审核</span>
                  <span class="stat-value">{{ getPendingCountByType(3) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">最近审核</span>
                  <span class="stat-value">{{ formatDate(getLastReviewTime(3)) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近审核记录 -->
      <div class="recent-reviews" v-if="stats.recent_reviews && stats.recent_reviews.length">
        <h2 class="review-type-tile">最近审核记录</h2>
        <div class="review-list">
          <div v-for="review in stats.recent_reviews" :key="review.id" class="review-item">
            <div class="review-info">
              <div class="review-title">{{ review.dataset_title }}</div>
              <div class="review-time">{{ formatDate(review.reviewed_at) }}</div>
            </div>
            <div class="review-status">
              <el-tag :type="review.review_status === 1 ? 'success' : 'danger'">
                {{ review.review_status === 1 ? '通过' : '拒绝' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reviewApi } from '@/api'
import type { DatasetType, ReviewStats } from '@/types'
import {
  CircleCheck,
  CircleClose,
  Clock,
  Document,
  EditPen,
  Picture,
  User,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const stats = ref<ReviewStats>({
  pending_count: 0,
  approved_count: 0,
  rejected_count: 0,
  my_reviews_count: 0,
  recent_reviews: [],
})

// 按类型的待审核数量（模拟数据，实际应从API获取）
const pendingByType = ref({
  1: 5, // 综合性病例
  2: 3, // 影像数据集
  3: 7, // 考试题目
})

const lastReviewTimes = ref({
  1: '2024-01-15',
  2: '2024-01-18',
  3: '2024-01-20',
})

// 方法
const loadStats = async () => {
  try {
    const response = await reviewApi.getReviewStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载审核统计失败:', error)
    ElMessage.error('加载审核统计失败')
  }
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleDateString()
}

const getPendingCountByType = (type: DatasetType) => {
  return pendingByType.value[type] || 0
}

const getLastReviewTime = (type: DatasetType) => {
  return lastReviewTimes.value[type] || ''
}

const navigateToReviewList = (datasetType: DatasetType) => {
  router.push({
    name: 'dataset-review-list',
    query: { type: datasetType },
  })
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.review-overview {
  width: 100%;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-card.pending .card-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.stats-card.approved .card-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.stats-card.rejected .card-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
.stats-card.my-reviews .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #9ca3af;
}

/* 类型卡片 */
.review-types {
  margin-top: 32px;
}

.review-type-tile {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f1f2;
}

.type-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.type-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.type-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.card-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.card-icon-large {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
}

.card-icon-large.comprehensive {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card-icon-large.imaging {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.card-icon-large.exam {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.pending-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.card-body {
  padding: 24px;
}

.card-body .card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20px;
}

.card-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 最近审核记录 */
.recent-reviews {
  margin-top: 32px;
}

.review-list {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.review-item:last-child {
  border-bottom: none;
}

.review-info {
  flex: 1;
}

.review-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.review-time {
  font-size: 12px;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .type-cards {
    grid-template-columns: 1fr;
  }

  .card-stats {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
