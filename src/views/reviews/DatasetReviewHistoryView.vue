<template>
  <div class="review-history-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">审核历史</h1>
          <p class="page-subtitle">查看所有数据集的审核记录</p>
        </div>
        <div class="header-right">
          <el-button @click="navigateToOverview">
            <el-icon><ArrowLeft /></el-icon>
            返回概览
          </el-button>
        </div>
      </div>

      <!-- 搜索筛选区 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索数据集标题..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-controls">
            <el-select
              v-model="selectedType"
              placeholder="全部类型"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部类型" value="" />
              <el-option label="综合性病例" :value="1" />
              <el-option label="影像数据集" :value="2" />
              <el-option label="考试题目" :value="3" />
            </el-select>

            <el-select
              v-model="selectedStatus"
              placeholder="全部结果"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部结果" value="" />
              <el-option label="通过" :value="1" />
              <el-option label="拒绝" :value="2" />
            </el-select>

            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />

            <el-button @click="handleReset">重置筛选</el-button>
          </div>
        </div>
      </div>

      <!-- 审核历史列表 -->
      <div class="history-table">
        <el-table :data="historyList" stripe v-loading="loading">
          <el-table-column label="数据集信息" min-width="200">
            <template #default="{ row }">
              <div class="dataset-info">
                <div class="dataset-name">{{ row.dataset.title }}</div>
                <el-tag :type="getTypeTagType(row.dataset.dataset_type)" size="small">
                  {{ getTypeLabel(row.dataset.dataset_type) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="审核结果" width="100">
            <template #default="{ row }">
              <el-tag :type="row.review_status === 1 ? 'success' : 'danger'">
                {{ row.review_status === 1 ? '通过' : '拒绝' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="审核意见" min-width="150">
            <template #default="{ row }">
              <div class="review-comment">
                {{ row.review_comment || '无' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="拒绝原因" min-width="150">
            <template #default="{ row }">
              <div class="review-reason">
                {{ row.review_reason || '无' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="审核人" width="100" prop="reviewer_name" />

          <el-table-column label="审核时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.reviewed_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleViewDataset(row)">
                查看数据集
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reviewApi } from '@/api'
import type { DatasetReview, DatasetType, ReviewStatus } from '@/types'
import { ArrowLeft, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const selectedType = ref<DatasetType | ''>('')
const selectedStatus = ref<ReviewStatus | ''>('')
const dateRange = ref<[string, string] | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)
const historyList = ref<DatasetReview[]>([])

// 方法
const loadHistory = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      size: pageSize.value,
    }

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    if (selectedType.value) {
      params.dataset_type = selectedType.value
    }

    if (selectedStatus.value) {
      params.review_status = selectedStatus.value
    }

    if (dateRange.value) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }

    const response = await reviewApi.getReviewHistory(params)
    if (response.success) {
      historyList.value = response.data.items
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载审核历史失败:', error)
    ElMessage.error('加载审核历史失败')
  } finally {
    loading.value = false
  }
}

const getTypeLabel = (type: DatasetType) => {
  const labels: Record<DatasetType, string> = {
    [1]: '综合性病例',
    [2]: '影像数据集',
    [3]: '考试题目',
  }
  return labels[type] || '未知类型'
}

const getTypeTagType = (type: DatasetType) => {
  const types: Record<DatasetType, string> = {
    [1]: 'primary',
    [2]: 'success',
    [3]: 'warning',
  }
  return types[type] || ''
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleString()
}

const navigateToOverview = () => {
  router.push({ name: 'dataset-review-overview' })
}

const handleSearch = () => {
  currentPage.value = 1
  loadHistory()
}

const handleReset = () => {
  searchKeyword.value = ''
  selectedType.value = ''
  selectedStatus.value = ''
  dateRange.value = null
  currentPage.value = 1
  loadHistory()
}

const handleViewDataset = (row: DatasetReview) => {
  router.push({
    name: 'dataset-detail',
    params: { id: row.dataset_id },
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadHistory()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadHistory()
}

onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.review-history-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 历史表格 */
.history-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.dataset-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dataset-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.review-comment,
.review-reason {
  font-size: 12px;
  color: #6b7280;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-controls {
    flex-wrap: wrap;
  }
}
</style>
