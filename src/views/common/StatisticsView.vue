<template>
  <div class="statistics content-container">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">统计分析</h1>
      </div>

      <div class="content-section">
        <el-row :gutter="16">
          <el-col :span="24">
            <el-card class="page-card">
              <template #header>
                <span>模型性能对比</span>
              </template>
              <PerformanceChart />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PerformanceChart from '@/components/charts/PerformanceChart.vue'
</script>

<style scoped>
.statistics {
  width: 100%;
}
</style>
