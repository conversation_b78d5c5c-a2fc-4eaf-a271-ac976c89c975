<template>
  <div class="home-page">
    <!-- Banner 区域 -->
    <section class="banner-section">
      <div class="banner-container">
        <div class="banner-content">
          <div class="banner-text">
            <h1 class="banner-title">
              <span class="title-highlight">宠医智评</span>
              专业AI模型评测平台
            </h1>
            <p class="banner-subtitle">
              基于兽医领域的专业AI模型评测与数据管理平台，为宠物医疗行业提供精准、可靠的智能化解决方案
            </p>
          </div>

          <div class="banner-stats">
            <div class="stat-item">
              <div class="stat-number">{{ stats.datasets }}+</div>
              <div class="stat-label">专业数据集</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.models }}+</div>
              <div class="stat-label">AI模型</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.evaluations }}+</div>
              <div class="stat-label">评测任务</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.users }}+</div>
              <div class="stat-label">专业用户</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 平台介绍 -->
    <section class="intro-section">
      <div class="section-container">
        <div class="intro-content">
          <div class="intro-text">
            <h2 class="section-title">引领宠物医疗AI评测新标准</h2>
            <p class="section-description">
              宠医智评平台致力于构建兽医领域最专业的AI模型评测体系，通过严格的数据质量控制、
              科学的评测方法和完善的审核机制，为宠物医疗行业的智能化发展提供坚实的技术支撑。
            </p>
            <div class="intro-features">
              <div class="feature-item">
                <el-icon class="feature-icon"><Star /></el-icon>
                <span>专业可靠</span>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><TrendCharts /></el-icon>
                <span>科学评测</span>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Connection /></el-icon>
                <span>开放协作</span>
              </div>
            </div>
          </div>
          <div class="intro-visual">
            <div class="visual-card">
              <el-icon class="visual-icon"><DataAnalysis /></el-icon>
              <h4>智能分析</h4>
              <p>基于大数据的智能分析引擎</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能模块 -->
    <section class="features-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">核心功能模块</h2>
          <p class="section-subtitle">为您提供全方位的AI模型评测与数据管理服务</p>
        </div>

        <div class="features-grid">
          <div
            v-for="feature in availableFeatures"
            :key="feature.name"
            class="feature-card"
            @click="handleFeatureClick(feature.path)"
          >
            <div class="feature-header">
              <div class="feature-icon-wrapper">
                <el-icon class="feature-icon"><component :is="feature.icon" /></el-icon>
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
            </div>
            <p class="feature-description">{{ feature.description }}</p>

            <!-- 功能特性标签 -->
            <div v-if="feature.features" class="feature-tags">
              <el-tag
                v-for="tag in feature.features"
                :key="tag"
                size="small"
                type="info"
                class="feature-tag"
              >
                {{ tag }}
              </el-tag>
            </div>

            <div class="feature-footer">
              <el-button type="primary" link class="feature-action">
                立即体验 <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色功能 -->
    <section class="highlights-section">
      <div class="section-container">
        <div class="highlights-content">
          <div class="highlights-text">
            <h2 class="section-title">平台核心优势</h2>
            <div class="highlights-list">
              <div class="highlight-item">
                <div class="highlight-icon">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="highlight-content">
                  <h4>专业领域聚焦</h4>
                  <p>专注兽医领域，提供针对性的AI模型评测服务</p>
                </div>
              </div>
              <div class="highlight-item">
                <div class="highlight-icon">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="highlight-content">
                  <h4>严格质量控制</h4>
                  <p>多层次审核机制，确保数据质量和评测结果的可靠性</p>
                </div>
              </div>
              <div class="highlight-item">
                <div class="highlight-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="highlight-content">
                  <h4>高效评测流程</h4>
                  <p>自动化评测流程，快速获得准确的模型性能评估</p>
                </div>
              </div>
            </div>
          </div>
          <div class="highlights-visual">
            <div class="visual-grid">
              <div class="visual-item">
                <el-icon><DataBoard /></el-icon>
                <span>数据管理</span>
              </div>
              <div class="visual-item">
                <el-icon><Monitor /></el-icon>
                <span>实时监控</span>
              </div>
              <div class="visual-item">
                <el-icon><Document /></el-icon>
                <span>报告生成</span>
              </div>
              <div class="visual-item">
                <el-icon><Share /></el-icon>
                <span>协作共享</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 最近活动 -->
    <section class="activity-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">最近动态</h2>
          <p class="section-subtitle">了解平台最新的活动和更新</p>
        </div>

        <div class="activity-grid">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-card">
            <div class="activity-icon">
              <el-icon><component :is="activity.icon" /></el-icon>
            </div>
            <div class="activity-content">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <p class="activity-description">{{ activity.description }}</p>
              <span class="activity-time">{{ formatTime(activity.time) }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer-section">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-brand">
            <div class="footer-logo">
              <el-icon><Cpu /></el-icon>
              <span class="footer-brand-text">宠医智评</span>
            </div>
            <p class="footer-description">
              专业的兽医AI模型评测与数据管理平台，致力于推动宠物医疗行业的智能化发展。
            </p>
          </div>

          <div class="footer-links">
            <div class="footer-column">
              <h4 class="footer-title">产品服务</h4>
              <ul class="footer-list">
                <li>
                  <a href="#" @click.prevent="handleFooterLinkClick('/app/datasets')">数据集管理</a>
                </li>
                <li>
                  <a href="#" @click.prevent="handleFooterLinkClick('/app/models')">模型评测</a>
                </li>
                <li>
                  <a href="#" @click.prevent="handleFooterLinkClick('/app/dataset-reviews')"
                    >质量审核</a
                  >
                </li>
                <li>
                  <a href="#" @click.prevent="handleFooterLinkClick('/app/evaluations')"
                    >评测结果</a
                  >
                </li>
              </ul>
            </div>

            <div class="footer-column">
              <h4 class="footer-title">帮助支持</h4>
              <ul class="footer-list">
                <li>
                  <a href="#" @click.prevent="handleFooterLinkClick('/app/help')">使用指南</a>
                </li>
                <li><a href="#">API文档</a></li>
                <li><a href="#">技术支持</a></li>
                <li><a href="#">常见问题</a></li>
              </ul>
            </div>

            <div class="footer-column">
              <h4 class="footer-title">关于我们</h4>
              <ul class="footer-list">
                <li><a href="#">公司介绍</a></li>
                <li><a href="#">联系我们</a></li>
                <li><a href="#">隐私政策</a></li>
                <li><a href="#">服务条款</a></li>
              </ul>
            </div>

            <div class="footer-column">
              <h4 class="footer-title">联系信息</h4>
              <div class="contact-info">
                <p>
                  <el-icon><Message /></el-icon> <EMAIL>
                </p>
                <p>
                  <el-icon><Phone /></el-icon> ************
                </p>
                <p>
                  <el-icon><Location /></el-icon> 北京市朝阳区科技园
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <div class="footer-copyright">
            <p>&copy; 2024 宠医智评平台. 保留所有权利.</p>
          </div>
          <div class="footer-links-bottom">
            <a href="#">隐私政策</a>
            <a href="#">服务条款</a>
            <a href="#">网站地图</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/counter'
import {
  ArrowRight,
  Check,
  Connection,
  Cpu,
  DataAnalysis,
  DataBoard,
  Document,
  Location,
  Message,
  Monitor,
  Phone,
  Share,
  Star,
  TrendCharts,
} from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userStore = useUserStore()

// 统计数据
const stats = ref({
  datasets: 156,
  models: 23,
  evaluations: 89,
  users: 1200,
})

// 所有功能模块
const allFeatures = [
  {
    name: 'datasets',
    title: '数据集管理',
    description:
      '专业的兽医数据集管理平台，支持图像、文本、病例等多种数据类型的上传、标注和版本控制，为AI模型训练提供高质量的数据基础',
    icon: 'DataBoard',
    path: '/app/datasets',
    permission: null, // 移除权限限制，始终显示
    features: ['多格式数据支持', '智能标注工具', '版本管理', '质量检测'],
  },
  {
    name: 'reviews',
    title: '数据集审核',
    description:
      '严格的多层次数据质量审核体系，由专业兽医和数据科学家组成的审核团队，确保每个数据集都符合行业标准和学术要求',
    icon: 'Document',
    path: '/app/dataset-reviews',
    permission: null, // 移除权限限制，始终显示
    features: ['专家审核', '质量评分', '标准化检查', '反馈机制'],
  },
  {
    name: 'models',
    title: 'AI模型榜单',
    description:
      '全面展示兽医AI模型的性能排行榜，包括诊断准确率、处理速度、适用场景等多维度评估，帮助用户选择最适合的模型',
    icon: 'Cpu',
    path: '/app/models',
    permission: null,
    features: ['性能排行', '多维评估', '实时更新', '详细报告'],
  },
  {
    name: 'evaluations',
    title: '评测结果',
    description:
      '详细的模型评测结果展示和分析平台，提供可视化的性能对比、错误分析和改进建议，支持自定义评测指标和报告导出',
    icon: 'Monitor',
    path: '/app/evaluations',
    permission: null,
    features: ['可视化分析', '性能对比', '错误诊断', '报告导出'],
  },
  {
    name: 'tasks',
    title: '评测任务',
    description:
      '灵活的评测任务管理系统，支持批量模型测试、自定义评测流程和实时监控，让模型评测变得简单高效',
    icon: 'TrendCharts',
    path: '/app/tasks',
    permission: null,
    features: ['批量测试', '自定义流程', '实时监控', '结果追踪'],
  },
]

// 根据权限过滤可用功能（现在大部分功能都公开显示）
const availableFeatures = computed(() => {
  // 所有功能都显示，权限检查在点击时进行
  return allFeatures
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '数据集审核完成',
    description: '兽医诊断图像数据集 v2.1 已通过专业审核，现已上线',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000),
    icon: 'Check',
  },
  {
    id: 2,
    title: '新模型评测启动',
    description: '宠物疾病诊断模型 GPT-Vet v3.0 开始全面评测',
    time: new Date(Date.now() - 4 * 60 * 60 * 1000),
    icon: 'Cpu',
  },
])

// 处理功能卡片点击
const handleFeatureClick = (path: string) => {
  // 公开页面直接跳转
  const publicPaths = ['/app/models', '/app/evaluations']
  if (publicPaths.includes(path)) {
    router.push(path)
    return
  }

  // 需要登录的功能页面
  if (path.startsWith('/app') && !userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  router.push(path)
}

// 处理页脚链接点击
const handleFooterLinkClick = (path: string) => {
  // 如果是app路径且用户未登录，跳转到登录页
  if (path.startsWith('/app') && !userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  router.push(path)
}

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes}分钟前`
  }
  if (hours < 24) {
    return `${hours}小时前`
  }
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里可以调用API获取真实的统计数据
    // const response = await getHomeStats()
    // stats.value = response.data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
/* 基础样式 */
.home-page {
  min-height: 100vh;
  background: #ffffff;
  overflow-x: hidden;
}

/* 确保首页不受全局page-container样式影响 */
:deep(.page-container) {
  padding: 0 !important;
  max-width: none !important;
}

/* Banner 区域 */
.banner-section {
  background: linear-gradient(135deg, #3981f3, #6f4e9f);
  color: white;
  padding: 60px 0;
  min-height: 450px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.banner-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>')
    no-repeat center center;
  background-size: cover;
  opacity: 0.3;
}

.banner-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  z-index: 1;
}

.banner-content {
  max-width: 100%;
}

.banner-title {
  font-size: 42px;
  font-weight: 700;
  line-height: 1.3;
  margin: 0 0 20px 0;
}

.title-highlight {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-right: 12px;
}

.banner-subtitle {
  font-size: 18px;
  line-height: 1.6;
  margin: 0 0 32px 0;
  opacity: 0.9;
  max-width: 600px;
}

.banner-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 6px 0;
  color: #ffd700;
}

.stat-label {
  font-size: 13px;
  opacity: 0.9;
}

/* 通用区域样式 */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.section-header {
  text-align: center;
  margin-bottom: 64px;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #1a202c;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #3981f3, #6f4e9f);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 18px;
  color: #718096;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section-description {
  font-size: 18px;
  line-height: 1.7;
  color: #4a5568;
  margin: 0 0 32px 0;
}

/* 平台介绍区域 */
.intro-section {
  padding: 100px 0;
  background: #f7fafc;
}

.intro-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: center;
}

.intro-text .section-title {
  text-align: left;
}

.intro-text .section-title::after {
  left: 0;
  transform: none;
}

.intro-features {
  display: flex;
  gap: 32px;
  margin-top: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #2d3748;
}

.feature-item .feature-icon {
  font-size: 20px;
  color: #3981f3;
}

.intro-visual {
  display: flex;
  justify-content: center;
}

.visual-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.visual-icon {
  font-size: 64px;
  color: #3981f3;
  margin-bottom: 16px;
}

.visual-card h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a202c;
}

.visual-card p {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

/* 功能模块区域 */
.features-section {
  padding: 100px 0;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
}

.feature-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #3981f3;
}

.feature-header {
  margin-bottom: 20px;
}

.feature-icon-wrapper {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #3981f3, #6f4e9f);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.feature-icon-wrapper .feature-icon {
  font-size: 28px;
  color: white;
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #1a202c;
}

.feature-description {
  font-size: 14px;
  line-height: 1.6;
  color: #718096;
  margin: 0 0 16px 0;
  flex: 1;
}

.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 20px;
}

.feature-tag {
  font-size: 11px;
  border-radius: 4px;
  background: rgba(57, 129, 243, 0.1);
  border-color: rgba(57, 129, 243, 0.2);
  color: #3981f3;
}

.feature-footer {
  margin-top: auto;
}

.feature-action {
  font-weight: 500;
}

/* 特色功能区域 */
.highlights-section {
  padding: 100px 0;
  background: #f7fafc;
}

.highlights-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.highlights-text .section-title {
  text-align: left;
}

.highlights-text .section-title::after {
  left: 0;
  transform: none;
}

.highlights-list {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-top: 32px;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.highlight-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3981f3, #6f4e9f);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.highlight-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a202c;
}

.highlight-content p {
  font-size: 14px;
  line-height: 1.6;
  color: #718096;
  margin: 0;
}

.highlights-visual {
  display: flex;
  justify-content: center;
}

.visual-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  max-width: 300px;
}

.visual-item {
  background: white;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.visual-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.visual-item .el-icon {
  font-size: 32px;
  color: #3981f3;
  margin-bottom: 8px;
}

.visual-item span {
  font-size: 12px;
  font-weight: 500;
  color: #4a5568;
}

/* 最近活动区域 */
.activity-section {
  padding: 100px 0;
  background: white;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.activity-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.activity-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: #3981f3;
}

.activity-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3981f3, #6f4e9f);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a202c;
}

.activity-description {
  font-size: 14px;
  line-height: 1.5;
  color: #718096;
  margin: 0 0 8px 0;
}

.activity-time {
  font-size: 12px;
  color: #a0aec0;
}

/* 页脚区域 */
.footer-section {
  background: #1a202c;
  color: white;
  padding: 60px 0 20px 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-brand {
  max-width: 300px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.footer-logo .el-icon {
  font-size: 32px;
  color: #3981f3;
}

.footer-brand-text {
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.footer-description {
  font-size: 14px;
  line-height: 1.6;
  color: #a0aec0;
  margin: 0;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
}

.footer-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-list li {
  margin-bottom: 8px;
}

.footer-list a {
  color: #a0aec0;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-list a:hover {
  color: #3981f3;
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #a0aec0;
}

.contact-info .el-icon {
  font-size: 16px;
  color: #3981f3;
}

.footer-bottom {
  border-top: 1px solid #2d3748;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-copyright p {
  margin: 0;
  font-size: 14px;
  color: #a0aec0;
}

.footer-links-bottom {
  display: flex;
  gap: 24px;
}

.footer-links-bottom a {
  color: #a0aec0;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links-bottom a:hover {
  color: #3981f3;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .banner-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .intro-content,
  .highlights-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .intro-text .section-title,
  .highlights-text .section-title {
    text-align: center;
  }

  .intro-text .section-title::after,
  .highlights-text .section-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .intro-features {
    justify-content: center;
    flex-wrap: wrap;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .banner-section {
    padding: 50px 0;
  }

  .banner-title {
    font-size: 32px;
  }

  .banner-subtitle {
    font-size: 16px;
  }

  .banner-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-title {
    font-size: 28px;
  }

  .section-subtitle {
    font-size: 16px;
  }

  .intro-section,
  .features-section,
  .highlights-section,
  .activity-section {
    padding: 60px 0;
  }

  .section-container {
    padding: 0 16px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .footer-links-bottom {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .banner-container {
    padding: 0 16px;
  }

  .banner-title {
    font-size: 26px;
  }

  .banner-subtitle {
    font-size: 15px;
  }

  .highlights-list {
    gap: 24px;
  }

  .highlight-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .visual-grid {
    grid-template-columns: 1fr;
    max-width: 200px;
  }

  .activity-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}
</style>
