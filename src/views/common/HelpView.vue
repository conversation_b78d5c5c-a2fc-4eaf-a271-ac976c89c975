<template>
  <div class="help-center content-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">
            <el-icon class="title-icon"><QuestionFilled /></el-icon>
            帮助中心
          </h1>
          <p class="page-subtitle">获取使用指南、常见问题解答和技术支持</p>
        </div>
        <div class="header-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索帮助内容..."
            class="search-input"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="help-main">
      <!-- 左侧导航 -->
      <div class="help-sidebar">
        <div class="sidebar-header">
          <h3 class="sidebar-title">文档导航</h3>
        </div>
        <div class="sidebar-content">
          <el-tree
            :data="helpTree"
            :props="treeProps"
            node-key="id"
            :default-expanded-keys="expandedKeys"
            :current-node-key="currentNodeKey"
            @node-click="handleNodeClick"
            class="help-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon class="node-icon">
                  <component :is="data.icon" />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
                <el-badge v-if="data.badge" :value="data.badge" class="node-badge" type="primary" />
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="help-content">
        <div class="content-header">
          <div class="breadcrumb">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item v-for="item in breadcrumbItems" :key="item.id">
                {{ item.label }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="content-actions">
            <el-button size="small" @click="toggleFavorite">
              <el-icon><Star /></el-icon>
              {{ isFavorite ? '已收藏' : '收藏' }}
            </el-button>
            <el-button size="small" @click="printContent">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
          </div>
        </div>

        <div class="content-body">
          <div class="content-article">
            <h1 class="article-title">{{ currentContent.title }}</h1>
            <div class="article-meta">
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                最后更新：{{ currentContent.updateTime }}
              </span>
              <span class="meta-item">
                <el-icon><View /></el-icon>
                阅读量：{{ currentContent.views }}
              </span>
            </div>
            <div class="article-content" v-html="currentContent.content"></div>
          </div>

          <!-- 相关文章 -->
          <div v-if="relatedArticles.length > 0" class="related-section">
            <h3 class="related-title">相关文章</h3>
            <div class="related-list">
              <div
                v-for="article in relatedArticles"
                :key="article.id"
                class="related-item"
                @click="handleNodeClick(article)"
              >
                <el-icon class="related-icon">
                  <component :is="article.icon" />
                </el-icon>
                <div class="related-info">
                  <div class="related-name">{{ article.label }}</div>
                  <div class="related-desc">{{ article.description }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 反馈区域 -->
          <div class="feedback-section">
            <div class="feedback-header">
              <h3 class="feedback-title">这篇文章对您有帮助吗？</h3>
            </div>
            <div class="feedback-actions">
              <el-button
                :type="feedback === 'helpful' ? 'primary' : 'default'"
                @click="submitFeedback('helpful')"
              >
                <el-icon><Select /></el-icon>
                有帮助
              </el-button>
              <el-button
                :type="feedback === 'not-helpful' ? 'danger' : 'default'"
                @click="submitFeedback('not-helpful')"
              >
                <el-icon><Close /></el-icon>
                没帮助
              </el-button>
            </div>
            <div v-if="feedback" class="feedback-thanks">感谢您的反馈！</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showSuccess } from '@/utils'
import { Clock, Close, Printer, Select, Star, View } from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'

// 响应式数据
const searchQuery = ref('')
const currentNodeKey = ref('quick-start')
const feedback = ref('')
const isFavorite = ref(false)

// 树形结构配置
const treeProps = {
  children: 'children',
  label: 'label',
}

// 默认展开的节点
const expandedKeys = ref(['getting-started', 'faq', 'api-docs', 'support'])

// 帮助文档树形结构
const helpTree = ref([
  {
    id: 'getting-started',
    label: '快速开始',
    icon: 'Guide',
    children: [
      {
        id: 'quick-start',
        label: '快速入门',
        icon: 'Guide',
        description: '了解如何快速上手使用平台',
      },
      {
        id: 'account-setup',
        label: '账户设置',
        icon: 'Setting',
        description: '创建和配置您的账户',
      },
      {
        id: 'first-dataset',
        label: '创建第一个数据集',
        icon: 'Document',
        description: '学习如何上传和管理数据集',
      },
      {
        id: 'first-evaluation',
        label: '运行第一次评测',
        icon: 'DataAnalysis',
        description: '创建和执行评测任务',
      },
    ],
  },
  {
    id: 'faq',
    label: '常见问题',
    icon: 'QuestionFilled',
    badge: '热门',
    children: [
      {
        id: 'dataset-format',
        label: '数据集格式要求',
        icon: 'Document',
        description: '支持的数据格式和结构要求',
      },
      {
        id: 'model-accuracy',
        label: '如何提高模型准确率',
        icon: 'DataAnalysis',
        description: '优化模型性能的技巧和建议',
      },
      {
        id: 'evaluation-failed',
        label: '评测任务失败原因',
        icon: 'QuestionFilled',
        description: '常见的评测失败问题和解决方案',
      },
      {
        id: 'result-interpretation',
        label: '结果解读说明',
        icon: 'View',
        description: '如何理解和分析评测结果',
      },
    ],
  },
  {
    id: 'api-docs',
    label: 'API文档',
    icon: 'Document',
    children: [
      {
        id: 'rest-api',
        label: 'REST API接口',
        icon: 'Document',
        description: 'HTTP API接口文档',
      },
      {
        id: 'python-sdk',
        label: 'Python SDK',
        icon: 'Document',
        description: 'Python开发工具包使用指南',
      },
      {
        id: 'authentication',
        label: '认证和授权',
        icon: 'Setting',
        description: 'API认证机制和权限管理',
      },
      {
        id: 'code-examples',
        label: '示例代码',
        icon: 'Document',
        description: '常用功能的代码示例',
      },
    ],
  },
  {
    id: 'support',
    label: '技术支持',
    icon: 'Service',
    children: [
      {
        id: 'contact-us',
        label: '联系我们',
        icon: 'Phone',
        description: '获取技术支持和帮助',
      },
      {
        id: 'feedback',
        label: '意见反馈',
        icon: 'Message',
        description: '提交问题和建议',
      },
      {
        id: 'service-status',
        label: '服务状态',
        icon: 'View',
        description: '查看系统运行状态',
      },
    ],
  },
])

// 内容数据
const contentData = {
  'quick-start': {
    title: '快速入门指南',
    updateTime: '2024-01-15',
    views: 1234,
    content: `
      <h2>欢迎使用宠医AI评测平台</h2>
      <p>本指南将帮助您快速上手使用我们的平台，开始您的AI模型评测之旅。</p>

      <h3>第一步：注册账户</h3>
      <p>访问我们的注册页面，填写必要信息创建您的账户。支持以下注册方式：</p>
      <ul>
        <li>邮箱注册</li>
        <li>手机号注册</li>
        <li>企业账户注册</li>
      </ul>

      <h3>第二步：完善个人信息</h3>
      <p>登录后，请完善您的个人信息，包括：</p>
      <ul>
        <li>真实姓名</li>
        <li>所属机构</li>
        <li>联系方式</li>
        <li>专业领域</li>
      </ul>

      <h3>第三步：上传数据集</h3>
      <p>准备您的数据集并上传到平台：</p>
      <ol>
        <li>点击"数据集管理"菜单</li>
        <li>选择"创建数据集"</li>
        <li>填写数据集基本信息</li>
        <li>上传数据文件</li>
        <li>等待审核通过</li>
      </ol>

      <h3>第四步：创建评测任务</h3>
      <p>使用您的数据集创建评测任务：</p>
      <ol>
        <li>进入"评测任务"页面</li>
        <li>选择要评测的模型</li>
        <li>配置评测参数</li>
        <li>提交任务并等待结果</li>
      </ol>

      <div class="tip-box">
        <h4>💡 小贴士</h4>
        <p>首次使用建议先阅读我们的<a href="#dataset-format">数据集格式要求</a>，确保您的数据符合平台标准。</p>
      </div>
    `,
  },
  'dataset-format': {
    title: '数据集格式要求',
    updateTime: '2024-01-10',
    views: 856,
    content: `
      <h2>支持的数据格式</h2>
      <p>我们的平台支持多种数据格式，以满足不同类型的AI模型评测需求。</p>

      <h3>图像数据</h3>
      <ul>
        <li><strong>格式：</strong>JPEG, PNG, TIFF, BMP</li>
        <li><strong>分辨率：</strong>最小 224x224，推荐 512x512 或更高</li>
        <li><strong>文件大小：</strong>单个文件不超过 10MB</li>
        <li><strong>颜色空间：</strong>RGB, 灰度</li>
      </ul>

      <h3>文本数据</h3>
      <ul>
        <li><strong>格式：</strong>TXT, CSV, JSON</li>
        <li><strong>编码：</strong>UTF-8</li>
        <li><strong>文件大小：</strong>单个文件不超过 50MB</li>
      </ul>

      <h3>标注文件</h3>
      <p>标注文件必须与数据文件一一对应：</p>
      <pre><code>{
  "image_id": "001.jpg",
  "annotations": [
    {
      "category": "病变区域",
      "bbox": [x, y, width, height],
      "confidence": 0.95
    }
  ]
}</code></pre>

      <h3>目录结构</h3>
      <p>推荐的数据集目录结构：</p>
      <pre><code>dataset/
├── images/
│   ├── train/
│   ├── val/
│   └── test/
├── annotations/
│   ├── train.json
│   ├── val.json
│   └── test.json
└── metadata.json</code></pre>

      <div class="warning-box">
        <h4>⚠️ 注意事项</h4>
        <ul>
          <li>确保所有文件名使用英文字符</li>
          <li>避免使用特殊字符和空格</li>
          <li>检查文件完整性，避免损坏文件</li>
          <li>标注数据必须准确无误</li>
        </ul>
      </div>
    `,
  },
  'contact-us': {
    title: '联系我们',
    updateTime: '2024-01-01',
    views: 432,
    content: `
      <h2>获取技术支持</h2>
      <p>我们提供多种方式为您提供技术支持和帮助。</p>

      <div class="contact-grid">
        <div class="contact-card">
          <div class="contact-icon">📞</div>
          <h3>客服电话</h3>
          <p>400-123-4567</p>
          <p class="contact-time">服务时间：周一至周五 9:00-18:00</p>
        </div>

        <div class="contact-card">
          <div class="contact-icon">📧</div>
          <h3>邮箱支持</h3>
          <p><EMAIL></p>
          <p class="contact-time">24小时内回复</p>
        </div>

        <div class="contact-card">
          <div class="contact-icon">💬</div>
          <h3>在线客服</h3>
          <p>点击右下角客服图标</p>
          <p class="contact-time">工作时间在线</p>
        </div>

        <div class="contact-card">
          <div class="contact-icon">🎫</div>
          <h3>工单系统</h3>
          <p>提交技术工单</p>
          <p class="contact-time">专业技术团队处理</p>
        </div>
      </div>

      <h3>常见问题优先级</h3>
      <ul>
        <li><strong>紧急：</strong>系统故障、数据丢失 - 2小时内响应</li>
        <li><strong>高：</strong>功能异常、评测失败 - 4小时内响应</li>
        <li><strong>中：</strong>使用咨询、功能建议 - 1个工作日内响应</li>
        <li><strong>低：</strong>一般咨询 - 3个工作日内响应</li>
      </ul>

      <div class="info-box">
        <h4>📋 提交问题时请提供</h4>
        <ul>
          <li>详细的问题描述</li>
          <li>操作步骤和截图</li>
          <li>错误信息（如有）</li>
          <li>您的账户信息</li>
          <li>使用的浏览器和版本</li>
        </ul>
      </div>
    `,
  },
}

// 当前显示的内容
const currentContent = computed(() => {
  return contentData[currentNodeKey.value] || contentData['quick-start']
})

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = [{ id: 'help', label: '帮助中心' }]

  // 查找当前节点的父级路径
  const findPath = (nodes: any[], targetId: string, path: any[] = []): any[] | null => {
    for (const node of nodes) {
      const currentPath = [...path, { id: node.id, label: node.label }]

      if (node.id === targetId) {
        return currentPath
      }

      if (node.children) {
        const result = findPath(node.children, targetId, currentPath)
        if (result) return result
      }
    }
    return null
  }

  const path = findPath(helpTree.value, currentNodeKey.value)
  if (path) {
    items.push(...path)
  }

  return items
})

// 相关文章
const relatedArticles = computed(() => {
  // 根据当前文章推荐相关内容
  const related = {
    'quick-start': ['account-setup', 'first-dataset'],
    'dataset-format': ['first-dataset', 'evaluation-failed'],
    'contact-us': ['feedback', 'service-status'],
  }

  const relatedIds = related[currentNodeKey.value] || []
  const articles: any[] = []

  const findNodes = (nodes: any[]) => {
    for (const node of nodes) {
      if (relatedIds.includes(node.id)) {
        articles.push(node)
      }
      if (node.children) {
        findNodes(node.children)
      }
    }
  }

  findNodes(helpTree.value)
  return articles
})

// 方法
const handleSearch = (query: string) => {
  // 实现搜索功能
  console.log('搜索:', query)
}

const handleNodeClick = (data: any) => {
  if (data.children && data.children.length > 0) {
    // 如果是父节点，不切换内容
    return
  }
  currentNodeKey.value = data.id
  feedback.value = ''
}

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  showSuccess(isFavorite.value ? '已添加到收藏' : '已取消收藏')
}

const printContent = () => {
  window.print()
}

const submitFeedback = (type: string) => {
  feedback.value = type
  showSuccess('感谢您的反馈！')
}

onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.help-center {
  min-height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-main {
  flex: 1;
}

.title-icon {
  font-size: 32px;
  color: #3981f3;
}

.search-input {
  width: 300px;
}

/* 主要内容区域 */
.help-main {
  display: flex;
  gap: 24px;
  min-height: calc(100vh - 120px);
  align-items: flex-start;
  flex: 1;
}

/* 左侧导航 */
.help-sidebar {
  width: 280px;
  flex-shrink: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: fit-content;
  max-height: calc(100vh - 120px);
}

.sidebar-header {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.help-tree {
  --el-tree-node-hover-bg-color: #f5f7fa;
  --el-tree-text-color: #606266;
  --el-tree-expand-icon-color: #c0c4cc;
  padding: 0 8px;
  height: 100%;
}

.help-tree :deep(.el-tree-node) {
  position: relative;
  z-index: 1;
}

.help-tree :deep(.el-tree-node__content) {
  height: 40px;
  padding: 0 12px;
  border-radius: 6px;
  margin: 2px 0;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.help-tree :deep(.el-tree-node__content:hover) {
  background-color: #f0f9ff;
}

.help-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e1f5fe;
  color: #0288d1;
  font-weight: 500;
}

.help-tree :deep(.el-tree-node__children) {
  position: relative;
  z-index: 1;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-icon {
  font-size: 16px;
  color: #909399;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-badge {
  flex-shrink: 0;
}

/* 右侧内容 */
.help-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: fit-content;
  max-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
  flex-shrink: 0;
}

.breadcrumb {
  flex: 1;
}

.content-actions {
  display: flex;
  gap: 8px;
}

.content-body {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.content-article {
  margin-bottom: 32px;
}

.article-title {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
}

.article-meta {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #909399;
}

.article-content {
  line-height: 1.7;
  color: #303133;
}

.article-content :deep(h2) {
  margin: 32px 0 16px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.article-content :deep(h3) {
  margin: 24px 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.article-content :deep(h4) {
  margin: 20px 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.article-content :deep(p) {
  margin: 16px 0;
  line-height: 1.7;
}

.article-content :deep(ul),
.article-content :deep(ol) {
  margin: 16px 0;
  padding-left: 24px;
}

.article-content :deep(li) {
  margin: 8px 0;
  line-height: 1.6;
}

.article-content :deep(pre) {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.article-content :deep(code) {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.article-content :deep(.tip-box) {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
}

.article-content :deep(.warning-box) {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
}

.article-content :deep(.info-box) {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
}

.article-content :deep(.contact-grid) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 24px 0;
}

.article-content :deep(.contact-card) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.article-content :deep(.contact-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-content :deep(.contact-icon) {
  font-size: 32px;
  margin-bottom: 12px;
}

.article-content :deep(.contact-time) {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

/* 相关文章 */
.related-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.related-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.related-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.related-item:hover {
  background: #e3f2fd;
  transform: translateX(4px);
}

.related-icon {
  font-size: 16px;
  color: #409eff;
  flex-shrink: 0;
}

.related-info {
  flex: 1;
}

.related-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.related-desc {
  font-size: 12px;
  color: #909399;
}

/* 反馈区域 */
.feedback-section {
  margin-top: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.feedback-header {
  margin-bottom: 16px;
}

.feedback-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.feedback-actions {
  display: flex;
  gap: 12px;
}

.feedback-thanks {
  margin-top: 16px;
  padding: 12px;
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 6px;
  color: #2e7d32;
  font-size: 14px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .search-input {
    width: 100%;
  }

  .help-main {
    flex-direction: column;
    gap: 16px;
  }

  .help-sidebar {
    width: 100%;
    order: 2;
    max-height: 400px;
  }

  .help-content {
    order: 1;
    max-height: none;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .content-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .content-body {
    padding: 20px;
  }

  .article-title {
    font-size: 24px;
  }

  .article-meta {
    flex-direction: column;
    gap: 8px;
  }

  .feedback-actions {
    flex-direction: column;
  }

  .article-content :deep(.contact-grid) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 24px;
  }

  .content-body {
    padding: 16px;
  }

  .article-title {
    font-size: 20px;
  }

  .sidebar-content {
    max-height: 300px;
  }
}
</style>
