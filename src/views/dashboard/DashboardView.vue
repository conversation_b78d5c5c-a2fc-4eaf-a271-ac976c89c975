<template>
  <div class="dashboard content-container">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">仪表板</h1>
        <p class="page-subtitle">欢迎回来，{{ userStore.user?.username }}！</p>
      </div>

      <!-- 统计卡片 -->
      <div class="content-section">
        <el-row :gutter="16" class="stats-row">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon models">
                  <el-icon><Cpu /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardData.totalModels }}</div>
                  <div class="stat-label">总模型数</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon tasks">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardData.totalTasks }}</div>
                  <div class="stat-label">总任务数</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon running">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardData.runningTasks }}</div>
                  <div class="stat-label">运行中</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ dashboardData.completedTasks }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表和列表 -->
      <div class="content-section">
        <el-row :gutter="16" class="content-row">
          <!-- 性能趋势图 -->
          <el-col :span="16">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>性能趋势</span>
                  <el-button type="text" @click="refreshChart">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </template>
              <PerformanceChart :data="chartData" :loading="chartLoading" />
            </el-card>
          </el-col>

          <!-- 最近任务 -->
          <el-col :span="8">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>最近任务</span>
                  <el-button type="text" @click="$router.push('/evaluations')">
                    查看全部
                  </el-button>
                </div>
              </template>
              <div class="recent-tasks">
                <div v-for="task in dashboardData.recentTasks" :key="task.id" class="task-item">
                  <div class="task-info">
                    <div class="task-name">{{ task.name }}</div>
                    <div class="task-model">{{ task.modelName }}</div>
                  </div>
                  <div class="task-status">
                    <el-tag :type="getStatusType(task.status)" size="small">
                      {{ getStatusText(task.status) }}
                    </el-tag>
                  </div>
                </div>
                <div v-if="dashboardData.recentTasks.length === 0" class="empty-tasks">
                  暂无任务
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { statisticsApi } from '@/api'
import PerformanceChart from '@/components/charts/PerformanceChart.vue'
import { useUserStore } from '@/stores/counter'
import type { EvaluationTask } from '@/types'
import { CircleCheck, Cpu, DataAnalysis, Refresh, Timer } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'

const userStore = useUserStore()

const dashboardData = ref({
  totalModels: 0,
  totalTasks: 0,
  runningTasks: 0,
  completedTasks: 0,
  recentTasks: [] as EvaluationTask[],
})

const chartData = ref<any>(null)
const chartLoading = ref(false)

function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
  }
  return statusMap[status] || 'info'
}

function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
  }
  return statusMap[status] || status
}

async function fetchDashboardData() {
  try {
    const response = await statisticsApi.getDashboard()
    dashboardData.value = response.data
    chartData.value = response.data.performanceChart
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
  }
}

async function refreshChart() {
  chartLoading.value = true
  try {
    await fetchDashboardData()
  } finally {
    chartLoading.value = false
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  width: 100%;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.models {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.running {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.content-row {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-tasks {
  max-height: 400px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.task-model {
  font-size: 12px;
  color: #909399;
}

.empty-tasks {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}
</style>
