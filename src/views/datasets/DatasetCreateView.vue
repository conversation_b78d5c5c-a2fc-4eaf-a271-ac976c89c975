<template>
  <div class="dataset-create-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">
            新建数据
            <el-tag v-if="currentType" type="primary" class="type-tag">
              {{ getTypeLabel(currentType) }}
            </el-tag>
          </h1>
          <p class="page-subtitle">创建新的数据并填写相关信息</p>
        </div>
        <div class="header-right">
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
        </div>
      </div>

      <!-- 数据集表单 -->
      <div class="form-container">
        <DatasetForm
          v-if="currentType"
          :initial-type="currentType"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />

        <div v-else class="error-container">
          <el-result icon="warning" title="参数错误" sub-title="请通过正确的入口访问此页面">
            <template #extra>
              <el-button type="primary" @click="handleBack">返回</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { datasetApi } from '@/api'
import DatasetForm from '@/components/DatasetForm.vue'
import type { DatasetCreateRequest, DatasetType } from '@/types'
import { ArrowLeft, Camera, Document, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 数据集类型选项配置
const datasetTypeOptions = [
  {
    value: 1 as DatasetType,
    label: '综合性病例数据集',
    description: '包含完整病例信息的综合性数据集，适用于临床诊断和治疗研究',
    icon: Document,
  },
  {
    value: 2 as DatasetType,
    label: '影像数据集',
    description: '医学影像数据集，包含X光、CT、MRI等各类影像资料',
    icon: Camera,
  },
  {
    value: 3 as DatasetType,
    label: '考试数据集',
    description: '用于考试和测评的题目数据集，支持多种题型和难度等级',
    icon: Edit,
  },
]

// 计算属性：当前数据集类型
const currentType = computed(() => {
  const typeFromQuery = route.query.type
  if (typeFromQuery) {
    const type = Number(typeFromQuery) as DatasetType
    // 验证类型是否有效
    if ([1, 2, 3].includes(type)) {
      return type
    }
  }
  return undefined
})

// 方法
const handleBack = () => {
  router.back()
}

const getTypeLabel = (type: DatasetType) => {
  const option = datasetTypeOptions.find((opt) => opt.value === type)
  return option?.label || '未知类型'
}

const handleSubmit = async (data: DatasetCreateRequest) => {
  try {
    const response = await datasetApi.createDataset(data)
    if (response.success) {
      ElMessage.success('数据集创建成功')
      router.push({
        name: 'dataset-detail',
        params: { id: response.data.id },
      })
    }
  } catch (error) {
    console.error('创建数据集失败:', error)
    ElMessage.error('创建数据集失败')
  }
}

const handleCancel = () => {
  router.back()
}
</script>

<style scoped>
.dataset-create-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.type-tag {
  margin-left: 12px;
}

.selected-type-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 24px;
}

.type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1e40af;
}

.type-badge .el-icon {
  color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .selected-type-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
