<template>
  <div class="dataset-detail-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">数据集详情</h1>
          <p class="page-subtitle">查看数据集的详细信息</p>
        </div>
        <div class="header-right">
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
          <el-button v-if="canEdit" type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
        </div>
      </div>

      <!-- 数据集详情 -->
      <div class="detail-container" v-loading="loading">
        <div v-if="dataset" class="detail-content">
          <!-- 基本信息 -->
          <DatasetBasicInfo :dataset="dataset" />

          <!-- 数据内容 -->
          <DatasetContentDisplay :data="dataset.data" :dataset-type="dataset.dataset_type" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { datasetApi } from '@/api'
import DatasetBasicInfo from '@/components/dataset/DatasetBasicInfo.vue'
import DatasetContentDisplay from '@/components/dataset/DatasetContentDisplay.vue'
import type { Dataset } from '@/types'
import { showError } from '@/utils'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const dataset = ref<Dataset | null>(null)

// 计算属性
const canEdit = computed(() => {
  // 只有草稿状态的数据集可以编辑
  return dataset.value && dataset.value.dataset_status === 1
})

// 方法
const loadDataset = async () => {
  const id = Number(route.params.id)
  if (!id) {
    showError('数据集ID无效')
    router.back()
    return
  }

  loading.value = true
  try {
    const response = await datasetApi.getDatasetDetail(id, true)
    if (response.success) {
      dataset.value = response.data
    } else {
      showError(response.message || '获取数据集失败')
    }
  } catch (error) {
    console.error('加载数据集失败:', error)
    showError('加载数据集失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  if (dataset.value) {
    router.push({
      name: 'dataset-list',
      query: { type: dataset.value.dataset_type },
    })
  } else {
    router.back()
  }
}

const handleEdit = () => {
  router.push({
    name: 'dataset-edit',
    params: { id: route.params.id },
  })
}

onMounted(() => {
  loadDataset()
})
</script>

<style scoped>
.dataset-detail-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-container {
  min-height: 200px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
