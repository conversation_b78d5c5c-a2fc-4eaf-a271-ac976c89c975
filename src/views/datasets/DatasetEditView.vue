<template>
  <div class="dataset-edit-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-main">
            <h1 class="page-title">
              <span v-if="loading">编辑数据集</span>
              <span v-else-if="dataset">
                编辑数据集 -
                <span class="dataset-name" :title="dataset.title">{{
                  truncateTitle(dataset.title)
                }}</span>
              </span>
              <span v-else>编辑数据集</span>
            </h1>
            <p class="page-description">
              <span v-if="loading">正在加载数据集信息...</span>
              <span v-else-if="dataset">修改"{{ dataset.title }}"的信息和内容</span>
              <span v-else>修改数据集信息和内容</span>
            </p>
          </div>
          <div class="header-actions">
            <el-button @click="handleBack">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
          </div>
        </div>
      </div>

      <!-- 状态提示 -->
      <div v-if="dataset && !isEditable" class="status-warning">
        <el-alert
          title="数据集不可编辑"
          :description="statusWarningMessage"
          type="warning"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 数据集表单 -->
      <div class="form-container" v-loading="loading">
        <DatasetForm
          v-if="dataset && isEditable"
          :initial-data="dataset"
          :is-edit="true"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />
        <div v-else-if="dataset && !isEditable" class="non-editable-content">
          <p>此数据集当前状态不允许编辑。如需修改，请联系管理员或等待审核完成。</p>
          <el-button @click="handleBack">返回列表</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { datasetApi } from '@/api'
import DatasetForm from '@/components/DatasetForm.vue'
import { useUserStore } from '@/stores/counter'
import type { Dataset, DatasetCreateRequest } from '@/types'
import { showError, showSuccess } from '@/utils'
import { ArrowLeft } from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const dataset = ref<Dataset | null>(null)

// 计算属性
const isEditable = computed(() => {
  if (!dataset.value) return false
  // 只有草稿(1)和已拒绝(4)状态的数据集可以编辑
  const editableStatuses = [1, 4]
  return editableStatuses.includes(dataset.value.dataset_status)
})

const statusWarningMessage = computed(() => {
  if (!dataset.value) return ''
  const statusName = dataset.value.status_name || getStatusName(dataset.value.dataset_status)
  return `当前数据集状态为"${statusName}"，只有草稿和已拒绝状态的数据集可以编辑。`
})

// 工具方法
const getStatusName = (status: number) => {
  switch (status) {
    case 1:
      return '草稿'
    case 2:
      return '待审核'
    case 3:
      return '已通过'
    case 4:
      return '已拒绝'
    case 5:
      return '已归档'
    default:
      return '未知'
  }
}

// 截断标题显示，避免过长
const truncateTitle = (title: string, maxLength = 30) => {
  if (!title) return ''
  if (title.length <= maxLength) return title
  return title.substring(0, maxLength) + '...'
}

// 方法
const loadDataset = async () => {
  const id = Number(route.params.id)
  if (!id) {
    showError('数据集ID无效')
    router.back()
    return
  }

  loading.value = true
  try {
    const response = await datasetApi.getDatasetDetail(id, true)
    if (response.success) {
      dataset.value = response.data
    }
  } catch (error) {
    console.error('加载数据集失败:', error)
    showError('加载数据集失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  // 直接返回到数据集列表页面
  router.push({
    name: 'dataset-list',
  })
}

const handleSubmit = async (data: DatasetCreateRequest) => {
  const id = Number(route.params.id)
  try {
    if (data.dataset_type === 2) {
      console.log('影像数据集 image_files:', (data.data as any)?.image_files)
    }

    const response = await datasetApi.updateDataset(id, data)
    if (response.success) {
      showSuccess('数据集更新成功')
      router.push({
        name: 'dataset-detail',
        params: { id: id },
      })
    }
  } catch (error: any) {
    // 错误处理
    if (error.response?.status === 403) {
      showError('无权限编辑此数据集，请检查您的权限设置')
    } else if (error.response?.data?.message) {
      showError(error.response.data.message)
    } else {
      showError('更新数据集失败')
    }
  }
}

const handleCancel = () => {
  handleBack()
}

onMounted(async () => {
  await loadDataset()
})
</script>

<style scoped>
.dataset-edit-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
  min-width: 0; /* 允许内容收缩 */
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.dataset-name {
  color: #409eff;
  font-weight: 600;
}

/* 保留特殊样式 */
.page-title {
  word-break: break-word; /* 长标题时允许换行 */
}

.status-warning {
  margin-bottom: 24px;
}

.form-container {
  min-height: 200px;
}

.non-editable-content {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.non-editable-content p {
  margin-bottom: 20px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
