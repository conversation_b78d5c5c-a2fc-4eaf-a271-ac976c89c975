<template>
  <div class="dataset-overview content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-main">
            <h1 class="page-title">数据集管理</h1>
            <p class="page-description">管理和维护各类兽医评估数据集</p>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stats-card">
          <div class="card-icon total">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">总数据集</div>
            <div class="card-value">{{ stats.total_datasets || 0 }}</div>
            <div class="card-desc">全部数据集数量</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="card-icon my">
            <el-icon><User /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">我的数据集</div>
            <div class="card-value">{{ stats.my_datasets || 0 }}</div>
            <div class="card-desc">我创建的数据集</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="card-icon approved">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">已通过</div>
            <div class="card-value">{{ stats.by_status?.[3] || 0 }}</div>
            <div class="card-desc">审核通过的数据集</div>
          </div>
        </div>

        <div class="stats-card">
          <div class="card-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">待审核</div>
            <div class="card-value">{{ stats.by_status?.[2] || 0 }}</div>
            <div class="card-desc">等待审核的数据集</div>
          </div>
        </div>
      </div>

      <!-- 数据集类型卡片 -->
      <div class="dataset-types">
        <h2 class="dataset-types-title">数据集类型</h2>
        <div class="type-cards">
          <!-- 综合性病例数据集 -->
          <div class="type-card" @click="navigateToList(1)">
            <div class="card-header">
              <div class="card-icon-large comprehensive">
                <el-icon><Document /></el-icon>
              </div>
              <div class="card-actions">
                <!-- 添加大号字体的总数量显示 -->
                <div class="total-count">{{ getTypeCount(1) }}</div>
                <div>条记录</div>
              </div>
            </div>
            <div class="card-body">
              <h3 class="card-title">综合性病例数据集</h3>
              <p class="card-description">
                包含动物类型、症状、诊断、治疗等完整病例信息的综合性数据集
              </p>
              <div class="card-stats">
                <!-- 移除总数量项 -->
                <!-- 删除: <div class="stat-item">
                  <span class="stat-label">总数量</span>
                  <span class="stat-value">{{ getTypeCount(1) }}</span>
                </div> -->
                <div class="stat-item">
                  <span class="stat-label">最近更新</span>
                  <span class="stat-value">{{ getLastUpdateTime(1) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">贡献人数</span>
                  <span class="stat-value">{{ getContributors(1) }}</span>
                </div>
                <!-- 新增管理数据按钮 -->
                <div class="stat-item">
                  <el-link href="#" class="manage-data-link" @click.prevent="navigateToList(2)"
                    >管理数据 →</el-link
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 影像数据集 -->
          <div class="type-card" @click="navigateToList(2)">
            <div class="card-header">
              <div class="card-icon-large imaging">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="card-actions">
                <!-- 添加大号字体的总数量显示 -->
                <div class="total-count">{{ getTypeCount(2) }}</div>
                <div>条记录</div>
              </div>
            </div>
            <div class="card-body">
              <h3 class="card-title">医学影像数据集</h3>
              <p class="card-description">
                包含X光片、CT、MRI等各类医学影像及其诊断结果的专业数据集
              </p>
              <div class="card-stats">
                <!-- 移除总数量项 -->
                <!-- 删除: <div class="stat-item">
                  <span class="stat-label">总数量</span>
                  <span class="stat-value">{{ getTypeCount(2) }}</span>
                </div> -->
                <div class="stat-item">
                  <span class="stat-label">最近更新</span>
                  <span class="stat-value">{{ getLastUpdateTime(2) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">贡献人数</span>
                  <span class="stat-value">{{ getContributors(2) }}</span>
                </div>
                <!-- 新增管理数据按钮 -->
                <div class="stat-item">
                  <el-link href="#" class="manage-data-link" @click.prevent="navigateToList(2)"
                    >管理数据 →</el-link
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 考试题目数据集 -->
          <div class="type-card" @click="navigateToList(3)">
            <div class="card-header">
              <div class="card-icon-large exam">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="card-actions">
                <!-- 添加大号字体的总数量显示 -->
                <div class="total-count">{{ getTypeCount(3) }}</div>
                <div>条记录</div>
              </div>
            </div>
            <div class="card-body">
              <h3 class="card-title">考试题目数据集</h3>
              <p class="card-description">包含各类兽医专业考试题目、答案及解析的教育评估数据集</p>
              <div class="card-stats">
                <!-- 移除总数量项 -->
                <!-- 删除: <div class="stat-item">
                  <span class="stat-label">总数量</span>
                  <span class="stat-value">{{ getTypeCount(3) }}</span>
                </div> -->
                <div class="stat-item">
                  <span class="stat-label">最近更新</span>
                  <span class="stat-value">{{ getLastUpdateTime(3) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">贡献人数</span>
                  <span class="stat-value">{{ getContributors(3) }}</span>
                </div>
                <!-- 新增管理数据按钮 -->
                <div class="stat-item">
                  <el-link href="#" class="manage-data-link" @click.prevent="navigateToList(2)"
                    >管理数据 →</el-link
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { datasetApi } from '@/api'
import { useUserStore } from '@/stores/counter'
import type { DatasetStats, DatasetType } from '@/types'
import {
  CircleCheck,
  Clock,
  DataBoard,
  Document,
  EditPen,
  Picture,
  User,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const stats = ref<DatasetStats>({
  total_datasets: 0,
  by_type: {},
  by_status: {},
  my_datasets: 0,
  type_details: {},
})

// 方法
const loadStats = async () => {
  try {
    const response = await datasetApi.getDatasetStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleDateString()
}

// 获取指定类型的数据集数量
const getTypeCount = (type: DatasetType) => {
  return (
    stats.value.type_details?.[type.toString()]?.count ||
    stats.value.by_type?.[type.toString()] ||
    0
  )
}

// 获取指定类型的最近更新时间
const getLastUpdateTime = (type: DatasetType) => {
  const lastUpdated = stats.value.type_details?.[type.toString()]?.last_updated
  return formatDate(lastUpdated || '')
}

// 获取指定类型的贡献人数
const getContributors = (type: DatasetType) => {
  return stats.value.type_details?.[type.toString()]?.contributors || 0
}

const navigateToList = (datasetType?: DatasetType) => {
  router.push({
    name: 'dataset-list',
    query: datasetType ? { type: datasetType } : {},
  })
}

const handleCreateDataset = (datasetType?: DatasetType) => {
  router.push({
    name: 'dataset-create',
    query: datasetType ? { type: datasetType } : {},
  })
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dataset-overview {
  width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card-icon.my {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.card-icon.approved {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.card-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #9ca3af;
}

/* 数据集类型卡片 */
.dataset-types {
  margin-top: 32px;
}

.dataset-types-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.type-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.type-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.card-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-icon-large {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
}

.card-icon-large.comprehensive {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card-icon-large.imaging {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.card-icon-large.exam {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-body {
  padding: 24px;
}

.card-body .card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20px;
}

.card-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 60px;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 新增 total-count 样式 */
.total-count {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  padding: 4px 8px;
  margin-right: 8px;
}

/* 新增 manage-data-link 样式 */
.manage-data-link {
  display: inline-block;
  vertical-align: middle;
  text-decoration: none;
  color: #409eff;
  font-size: 16px;
  font-weight: bold;
  transition: color 0.3s ease;
}

.manage-data-link:hover {
  color: #66b1ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .type-cards {
    grid-template-columns: 1fr;
  }

  .card-stats {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
