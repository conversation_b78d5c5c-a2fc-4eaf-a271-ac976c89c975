<template>
  <div class="dataset-list-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-main">
            <h1 class="page-title">
              {{ getTypeTitle() }}
              <el-tag v-if="currentType" type="primary" class="type-tag">
                {{ getTypeLabel(currentType) }}
              </el-tag>
            </h1>
            <p class="page-description">管理和维护数据集的创建、编辑、删除和发布</p>
          </div>
          <div class="header-actions">
            <el-button @click="navigateToOverview">
              <el-icon><ArrowLeft /></el-icon>
              返回概览
            </el-button>
            <el-button type="success" @click="handleImport">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新建数据
            </el-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选区 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="search-box">
            <el-input v-model="searchKeyword" placeholder="搜索数据集标题..." clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-controls">
            <el-select
              v-model="selectedStatus"
              placeholder="全部状态"
              clearable
              @change="handleSearch"
              style="width: 150px"
            >
              <el-option label="全部状态" value="" />
              <el-option label="草稿" :value="1" />
              <el-option label="待审核" :value="2" />
              <el-option label="已通过" :value="3" />
              <el-option label="已拒绝" :value="4" />
              <el-option label="已归档" :value="5" />
            </el-select>

            <el-select
              v-model="selectedCreator"
              placeholder="全部创建者"
              clearable
              @change="handleSearch"
              style="width: 150px"
            >
              <el-option label="全部创建者" value="" />
              <el-option label="我的数据集" value="my" />
            </el-select>

            <el-button @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 数据集列表 -->
      <div class="dataset-table">
        <el-table :data="datasetList" stripe v-loading="loading">
          <el-table-column prop="id" label="ID" width="80" />

          <el-table-column prop="title" label="标题" min-width="150">
            <template #default="{ row }">
              <div class="dataset-name">{{ row.title }}</div>
            </template>
          </el-table-column>

          <el-table-column label="标签" width="150">
            <template #default="{ row }">
              <div class="dataset-tags" v-if="row.tags && row.tags.length">
                <el-tag
                  v-for="tag in row.tags.slice(0, 3)"
                  :key="tag"
                  size="small"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                <el-tag v-if="row.tags.length > 3" size="small" type="info">
                  +{{ row.tags.length - 3 }}
                </el-tag>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.dataset_type)">
                {{ getTypeLabel(row.dataset_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.dataset_status)">
                {{ getStatusLabel(row.dataset_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="更新时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="hasPermission('dataset:read')"
                type="primary"
                link
                @click="handleView(row)"
              >
                查看
              </el-button>
              <el-tooltip
                v-if="hasPermission('dataset:write')"
                :content="getEditTooltip(row)"
                :disabled="isEditable(row)"
                placement="top"
              >
                <el-button
                  :type="isEditable(row) ? 'primary' : 'info'"
                  :disabled="!isEditable(row)"
                  link
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
              </el-tooltip>
              <el-button
                v-if="hasPermission('dataset:write') && row.dataset_status === 1"
                type="success"
                link
                @click="handleSubmitReview(row)"
              >
                提交审核
              </el-button>
              <el-button
                v-if="hasPermission('dataset:delete')"
                type="danger"
                link
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { datasetApi } from '@/api'
import { usePermissions } from '@/composables/usePermissions'
import type { Dataset, DatasetStatus, DatasetType } from '@/types'
import { ArrowLeft, Plus, Search, Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const { hasPermission } = usePermissions()

// 响应式数据
const searchKeyword = ref('')
const selectedStatus = ref<DatasetStatus | ''>('')
const selectedCreator = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)
const datasetList = ref<Dataset[]>([])

// 计算属性
const currentType = computed(() => {
  const typeFromQuery = route.query.type
  if (typeFromQuery) {
    return Number(typeFromQuery) as DatasetType
  }
  return null
})

// 方法
const loadDatasets = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      size: pageSize.value,
    }

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    if (currentType.value) {
      params.dataset_type = currentType.value
    }

    if (selectedStatus.value) {
      params.dataset_status = selectedStatus.value
    }

    const apiCall =
      selectedCreator.value === 'my'
        ? datasetApi.getMyDatasets(params)
        : datasetApi.getDatasets(params)

    const response = await apiCall
    if (response.success) {
      datasetList.value = response.data.items
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载数据集失败:', error)
    ElMessage.error('加载数据集失败')
  } finally {
    loading.value = false
  }
}

const getTypeTitle = () => {
  if (currentType.value) {
    return `${getTypeLabel(currentType.value)}列表`
  }
  return '数据集列表'
}

const getTypeLabel = (type: DatasetType) => {
  const labels: Record<DatasetType, string> = {
    [1]: '综合性病例',
    [2]: '影像数据集',
    [3]: '考试题目',
  }
  return labels[type] || '未知类型'
}

const getTypeTagType = (type: DatasetType) => {
  const types: Record<DatasetType, string> = {
    [1]: 'primary',
    [2]: 'success',
    [3]: 'warning',
  }
  return types[type] || ''
}

const getStatusLabel = (status: DatasetStatus) => {
  const labels: Record<DatasetStatus, string> = {
    [1]: '草稿',
    [2]: '待审核',
    [3]: '已通过',
    [4]: '已拒绝',
    [5]: '已归档',
  }
  return labels[status] || '未知状态'
}

const getStatusTagType = (status: DatasetStatus) => {
  const types: Record<DatasetStatus, string> = {
    [1]: 'info',
    [2]: 'warning',
    [3]: 'success',
    [4]: 'danger',
    [5]: '',
  }
  return types[status] || ''
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleDateString()
}

const navigateToOverview = () => {
  router.push({ name: 'dataset-overview' })
}

const handleSearch = () => {
  currentPage.value = 1
  loadDatasets()
}

const handleReset = () => {
  searchKeyword.value = ''
  selectedStatus.value = ''
  selectedCreator.value = ''
  currentPage.value = 1
  loadDatasets()
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const handleCreate = () => {
  router.push({
    name: 'dataset-create',
    query: currentType.value ? { type: currentType.value } : {},
  })
}

// 判断数据集是否可编辑
const isEditable = (row: Dataset) => {
  // 只有草稿(1)和已拒绝(4)状态的数据集可以编辑
  const editableStatuses = [1, 4]
  return editableStatuses.includes(row.dataset_status)
}

// 获取编辑按钮的tooltip内容
const getEditTooltip = (row: Dataset) => {
  if (isEditable(row)) {
    return '' // 可编辑时不显示tooltip
  }

  const statusName = getStatusLabel(row.dataset_status)
  return `数据集状态为"${statusName}"，只有草稿和已拒绝状态的数据集可以编辑`
}

const handleEdit = (row: Dataset) => {
  if (!isEditable(row)) {
    const statusName = getStatusLabel(row.dataset_status)
    ElMessage.warning(`数据集状态为"${statusName}"，无法编辑`)
    return
  }

  router.push({
    name: 'dataset-edit',
    params: { id: row.id },
  })
}

const handleView = (row: Dataset) => {
  router.push({
    name: 'dataset-detail',
    params: { id: row.id },
  })
}

const handleSubmitReview = async (row: Dataset) => {
  try {
    await ElMessageBox.confirm(`确定要提交数据集"${row.title}"进行审核吗？`, '确认提交', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await datasetApi.submitForReview(row.id, '请审核此数据集')
    ElMessage.success('提交审核成功')
    loadDatasets()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交审核失败:', error)
      ElMessage.error('提交审核失败')
    }
  }
}

const handleDelete = async (row: Dataset) => {
  try {
    await ElMessageBox.confirm(`确定要删除数据集"${row.title}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await datasetApi.deleteDataset(row.id)
    ElMessage.success('删除成功')
    loadDatasets()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadDatasets()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadDatasets()
}

// 监听路由变化
watch(
  () => route.query.type,
  () => {
    loadDatasets()
  },
  { immediate: true },
)

onMounted(() => {
  loadDatasets()
})
</script>

<style scoped>
.dataset-list-page {
  width: 100%;
}

.type-tag {
  margin-left: 8px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-main {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 数据集表格 */
.dataset-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.dataset-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 4px;
}

.dataset-desc {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.3;
  margin-bottom: 8px;
}

.dataset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  margin: 0;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-controls {
    flex-wrap: wrap;
  }
}

/* 禁用状态的编辑按钮样式 */
.el-button.is-disabled.is-link {
  color: #c0c4cc !important;
  cursor: not-allowed;
}

.el-button.is-disabled.is-link:hover {
  color: #c0c4cc !important;
}
</style>
