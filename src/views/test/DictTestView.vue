<template>
  <div class="dict-test-page">
    <h1>数据字典测试页面</h1>

    <el-card>
      <template #header>
        <span>测试字典API</span>
      </template>

      <el-space direction="vertical" style="width: 100%">
        <el-button @click="testDictApi('department_disease')">测试 department_disease</el-button>
        <el-button @click="testDictApi('specialty_category')">测试 specialty_category</el-button>
        <el-button @click="testDictApi('animal_category')">测试 animal_category</el-button>
        <el-button @click="testDictApi('image_category')">测试 image_category</el-button>
        <el-button @click="testDictApi('exam_subject')">测试 exam_subject</el-button>
      </el-space>
    </el-card>

    <el-card v-if="testResult" style="margin-top: 20px">
      <template #header>
        <span>API 响应结果</span>
      </template>

      <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { dataDictApi } from '@/api'
import { ref } from 'vue'

const testResult = ref<any>(null)

const testDictApi = async (category: string) => {
  try {
    const response = await dataDictApi.getDictOptions(category)

    testResult.value = {
      category,
      response,
    }
  } catch (error) {
    console.error(`测试 ${category} 失败:`, error)
    testResult.value = {
      category,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}
</script>

<style scoped>
.dict-test-page {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
