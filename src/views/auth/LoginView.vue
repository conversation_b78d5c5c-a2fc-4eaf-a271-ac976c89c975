<template>
  <div class="login-page">
    <!-- 登录内容区 -->
    <div class="login-container">
      <div class="login-content">
        <!-- 左侧介绍 -->
        <section class="intro-section">
          <h2 class="intro-title">宠医智评</h2>
          <p class="intro-subtitle">PetMed AI Evaluation Platform</p>
          <p class="intro-desc">
            专业的宠物医疗AI模型评测平台，为兽医行业提供准确、可靠的AI诊断工具评估服务
          </p>
          <div class="intro-actions">
            <el-button type="primary" size="large" @click="goToHome">立即开始</el-button>
            <el-button size="large" @click="goToHome">了解更多</el-button>
          </div>
        </section>

        <!-- 右侧登录卡片 -->
        <div class="login-box">
          <div class="login-header">
            <img src="/favicon.ico" alt="Logo" class="logo" />
            <h1 class="title">大模型评测平台</h1>
            <p class="subtitle">欢迎登录</p>
            <div class="demo-info">
              <el-alert title="演示账户" type="info" :closable="false" show-icon>
                <template #default>
                  <p>用户名：<strong>admin</strong></p>
                  <p>密码：<strong>123456</strong></p>
                </template>
              </el-alert>
            </div>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                size="large"
                prefix-icon="User"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                prefix-icon="Lock"
                show-password
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
                <el-link type="primary" @click="$router.push('/forgot-password')">
                  忘记密码？
                </el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleLogin"
                class="login-btn"
              >
                登录
              </el-button>
            </el-form-item>

            <el-form-item>
              <div class="register-link">
                还没有账户？
                <el-link type="primary" @click="$router.push('/register')"> 立即注册 </el-link>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/counter'
import type { LoginForm } from '@/types'
import { showError, showSuccess } from '@/utils'
import { type FormInstance, type FormRules } from 'element-plus'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const userStore = useUserStore()

// 返回首页
const goToHome = () => {
  router.push('/')
}

const loginFormRef = ref<FormInstance>()
const loading = ref(false)

const loginForm = reactive<LoginForm>({
  username: 'admin',
  password: 'admin123',
  remember: false,
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' },
  ],
}

async function handleLogin() {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const response = await userStore.login({
      username: loginForm.username,
      password: loginForm.password,
    })

    if (response.success) {
      showSuccess('登录成功')
      router.push('/home')
    } else {
      showError(response.message || '登录失败，请重试')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    showError(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 登录容器样式 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  min-width: 1200px;
  padding: 20px;
}

/* 左右分栏布局 */
.login-content {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: 56px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

/* 左侧介绍区样式 */
.intro-section {
  color: #ffffff;
}
.intro-title {
  font-size: 56px;
  font-weight: 800;
  margin: 0 0 12px 0;
}
.intro-subtitle {
  font-size: 22px;
  opacity: 0.9;
  margin: 0 0 20px 0;
}
.intro-desc {
  font-size: 16px;
  line-height: 1.9;
  opacity: 0.95;
  max-width: 520px;
}
.intro-actions {
  margin-top: 28px;
  display: flex;
  gap: 16px;
}

/* 右侧登录卡片微调 */
.login-box {
  width: 440px;
}

/* 响应式：小屏改为上下布局 */
@media (max-width: 1200px) {
  .login-container {
    min-width: auto;
    padding: 16px;
  }
  .login-content {
    grid-template-columns: 1fr;
    gap: 24px;
    max-width: 720px;
  }
  .intro-section {
    text-align: center;
  }
  .intro-desc {
    margin: 0 auto;
  }
  .intro-actions {
    justify-content: center;
  }
  .login-box {
    margin: 0 auto;
    width: 100%;
    max-width: 440px;
  }
}

.login-box {
  width: 440px;
  max-width: 90vw;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0 0 16px 0;
}

.demo-info {
  margin-top: 16px;
}

.demo-info p {
  margin: 4px 0;
  font-size: 13px;
}

.login-form {
  margin-top: 32px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-btn {
  width: 100%;
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #909399;
}
</style>
