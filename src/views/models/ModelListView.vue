<template>
  <div class="model-leaderboard-page content-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">AI模型榜单</h1>
          <p class="page-description">基于兽医AI模型评测平台的排行</p>
        </div>
        <div class="header-actions">
          <el-select v-model="selectedTimeRange" placeholder="全部时间" class="time-select">
            <el-option label="全部时间" value="all" />
            <el-option label="最近7天" value="7d" />
            <el-option label="最近30天" value="30d" />
          </el-select>
          <el-button type="primary" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出榜单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 功能标签 -->
    <div class="feature-tags">
      <div class="tag-item active">
        <el-icon><Star /></el-icon>
        综合排名
      </div>
      <div class="tag-item">
        <el-icon><TrendCharts /></el-icon>
        领域能力
      </div>
      <div class="tag-item">
        <el-icon><Aim /></el-icon>
        准确率
      </div>
      <div class="tag-item">
        <el-icon><Timer /></el-icon>
        响应时间
      </div>
      <div class="tag-item">
        <el-icon><Monitor /></el-icon>
        算力需求
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon blue">
          <el-icon><DataBoard /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">142</div>
          <div class="stat-label">参与模型数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon green">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">1,286</div>
          <div class="stat-label">总评测次数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon purple">
          <el-icon><Checked /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">85.7%</div>
          <div class="stat-label">平均准确率</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon orange">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">2小时前</div>
          <div class="stat-label">最近更新</div>
        </div>
      </div>
    </div>

    <!-- 模型排行榜 -->
    <div class="leaderboard-section">
      <div class="section-header">
        <h2 class="section-title">模型排名</h2>
      </div>

      <div class="leaderboard-table">
        <!-- 表头 -->
        <div class="table-header">
          <div class="col col-rank">排名</div>
          <div class="col col-model">模型</div>
          <div class="col col-score">综合评分</div>
          <div class="col col-accuracy">准确率</div>
          <div class="col col-speed">响应速度</div>
          <div class="col col-stability">稳定性</div>
          <div class="col col-change">变化</div>
          <div class="col col-action">操作</div>
        </div>

        <!-- 表格内容 -->
        <div class="table-body">
          <div v-for="(model, index) in modelList" :key="model.id" class="table-row">
            <!-- 排名 -->
            <div class="col col-rank">
              <div class="rank-badge" :class="getRankClass(index + 1)">
                <el-icon v-if="index === 0" class="trophy-icon"><Trophy /></el-icon>
                <span v-else class="rank-number">{{ index + 1 }}</span>
              </div>
            </div>

            <!-- 模型信息 -->
            <div class="col col-model">
              <div class="model-info">
                <div class="model-avatar" :style="{ backgroundColor: getAvatarColor(model.name) }">
                  {{ getAvatarText(model.name) }}
                </div>
                <div class="model-details">
                  <div class="model-name">{{ model.name }}</div>
                  <div class="model-desc">{{ model.description }}</div>
                </div>
              </div>
            </div>

            <!-- 综合评分 -->
            <div class="col col-score">
              <div class="score-display">
                <div class="score-bar">
                  <div
                    class="score-fill"
                    :style="{
                      width: model.score + '%',
                      backgroundColor: getScoreColor(model.score),
                    }"
                  ></div>
                </div>
                <span class="score-text">{{ model.score }}</span>
              </div>
            </div>

            <!-- 准确率 -->
            <div class="col col-accuracy">
              <span class="metric-value">{{ model.accuracy }}%</span>
            </div>

            <!-- 响应速度 -->
            <div class="col col-speed">
              <span class="metric-value">{{ model.speed }}s</span>
            </div>

            <!-- 稳定性 -->
            <div class="col col-stability">
              <span class="metric-value">{{ model.stability }}%</span>
            </div>

            <!-- 变化 -->
            <div class="col col-change">
              <div class="change-indicator" :class="getChangeClass(model.change)">
                <el-icon v-if="model.change > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="model.change < 0"><ArrowDown /></el-icon>
                <span>{{ model.change > 0 ? '+' : '' }}{{ model.change }}</span>
              </div>
            </div>

            <!-- 操作 -->
            <div class="col col-action">
              <el-button type="primary" link @click="viewDetails(model)"> 查看详情 </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Aim,
  ArrowDown,
  ArrowUp,
  Checked,
  Clock,
  DataBoard,
  Download,
  Monitor,
  Star,
  Timer,
  TrendCharts,
  Trophy,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'

// 响应式数据
const selectedTimeRange = ref('all')
const modelList = ref([
  {
    id: 1,
    name: 'VetGPT-Pro',
    description: '专业兽医',
    score: 92.5,
    accuracy: 94.2,
    speed: 1.8,
    stability: 98.1,
    change: 2,
  },
  {
    id: 2,
    name: 'PetMed-Advanced',
    description: '宠物医疗助手',
    score: 89.3,
    accuracy: 91.8,
    speed: 2.1,
    stability: 96.7,
    change: -1,
  },
  {
    id: 3,
    name: 'AnimalCare-AI',
    description: '动物护理',
    score: 87.9,
    accuracy: 89.5,
    speed: 1.9,
    stability: 95.2,
    change: 1,
  },
  {
    id: 4,
    name: 'VetAssist-Plus',
    description: '兽医助手',
    score: 85.7,
    accuracy: 87.3,
    speed: 2.3,
    stability: 94.8,
    change: 0,
  },
  {
    id: 5,
    name: 'PetHealth-Pro',
    description: '宠物健康',
    score: 83.2,
    accuracy: 85.9,
    speed: 2.0,
    stability: 93.1,
    change: 3,
  },
])

// 方法
const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return 'rank-normal'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#4caf50'
  if (score >= 85) return '#2196f3'
  if (score >= 80) return '#ff9800'
  return '#f44336'
}

const getChangeClass = (change: number) => {
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const viewDetails = (model: any) => {
  ElMessage.info(`查看 ${model.name} 详情`)
}

const getAvatarText = (name: string) => {
  return name.charAt(0).toUpperCase()
}

const getAvatarColor = (name: string) => {
  const colors = [
    '#4285f4',
    '#34a853',
    '#ea4335',
    '#9c27b0',
    '#ff9800',
    '#00bcd4',
    '#e91e63',
    '#673ab7',
    '#795548',
    '#607d8b',
  ]
  const index = name.charCodeAt(0) % colors.length
  return colors[index]
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.model-leaderboard-page {
  width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-select {
  width: 120px;
}

/* 功能标签 */
.feature-tags {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.tag-item:hover {
  border-color: #4285f4;
  color: #4285f4;
}

.tag-item.active {
  background: #4285f4;
  border-color: #4285f4;
  color: white;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.stat-icon.blue {
  background: #4285f4;
}

.stat-icon.green {
  background: #34a853;
}

.stat-icon.purple {
  background: #9c27b0;
}

.stat-icon.orange {
  background: #ff9800;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  line-height: 1;
}

/* 排行榜部分 */
.leaderboard-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  padding-bottom: 8px;
  border-bottom: 2px solid #4285f4;
}

.leaderboard-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 80px 1fr 120px 100px 100px 100px 100px 120px;
  gap: 16px;
  padding: 16px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-body {
  background: white;
}

.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 100px 100px 100px 100px 120px;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
  align-items: center;
}

.table-row:hover {
  background: #f9fafb;
}

.table-row:last-child {
  border-bottom: none;
}

.col {
  display: flex;
  align-items: center;
}

/* 排名列 */
.col-rank {
  justify-content: center;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.rank-badge.rank-first {
  background: #ffd700;
  color: #b8860b;
}

.rank-badge.rank-second {
  background: #c0c0c0;
  color: #666;
}

.rank-badge.rank-third {
  background: #cd7f32;
  color: #8b4513;
}

.rank-badge.rank-normal {
  background: #f3f4f6;
  color: #6b7280;
}

.trophy-icon {
  font-size: 16px;
}

.rank-number {
  font-size: 14px;
}

/* 模型信息列 */
.model-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.model-details {
  flex: 1;
  min-width: 0;
}

.model-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 2px;
}

.model-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1;
}

/* 评分列 */
.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-bar {
  width: 60px;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.score-text {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  min-width: 40px;
}

/* 指标值 */
.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

/* 变化指示器 */
.col-change {
  justify-content: center;
}

.change-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.change-indicator.positive {
  background: #dcfce7;
  color: #16a34a;
}

.change-indicator.negative {
  background: #fef2f2;
  color: #dc2626;
}

.change-indicator.neutral {
  background: #f3f4f6;
  color: #6b7280;
}

/* 操作列 */
.col-action {
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .table-header,
  .table-row {
    grid-template-columns: 60px 1fr 100px 80px 80px 80px 80px 100px;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .feature-tags {
    flex-wrap: wrap;
  }

  .table-header,
  .table-row {
    grid-template-columns: 50px 1fr 80px 70px 70px 70px 60px 80px;
    gap: 8px;
    padding: 12px 16px;
  }

  .section-header {
    padding: 16px 20px;
  }
}
</style>
