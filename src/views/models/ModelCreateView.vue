<template>
  <div class="model-create content-container">
    <div class="page-header">
      <h1>添加模型</h1>
    </div>

    <el-card>
      <el-form :model="form" label-width="120px">
        <el-form-item label="模型名称">
          <el-input v-model="form.name" placeholder="请输入模型名称" />
        </el-form-item>

        <el-form-item label="模型版本">
          <el-input v-model="form.version" placeholder="请输入模型版本" />
        </el-form-item>

        <el-form-item label="模型类型">
          <el-select v-model="form.type" placeholder="请选择模型类型">
            <el-option label="大语言模型" value="llm" />
            <el-option label="视觉模型" value="vision" />
            <el-option label="多模态模型" value="multimodal" />
          </el-select>
        </el-form-item>

        <el-form-item label="提供商">
          <el-input v-model="form.provider" placeholder="请输入提供商" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入模型描述"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary">保存</el-button>
          <el-button @click="$router.back()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

const form = reactive({
  name: '',
  version: '',
  type: '',
  provider: '',
  description: '',
})
</script>

<style scoped>
.model-create {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
</style>
