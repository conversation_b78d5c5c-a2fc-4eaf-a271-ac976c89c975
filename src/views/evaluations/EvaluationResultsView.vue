<template>
  <div class="evaluation-results content-container">
    <div class="page-container">
      <div class="page-header">
        <div class="header-content">
          <div class="header-main">
            <h1 class="page-title">评测结果</h1>
            <p class="page-description">查看和分析AI模型的详细评测结果</p>
          </div>
          <div class="header-actions">
            <el-button @click="$router.back()">返回</el-button>
          </div>
        </div>
      </div>

      <div class="content-section">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-card class="page-card">
              <template #header>
                <span>性能指标</span>
              </template>
              <div class="metrics">
                <div class="metric-item">
                  <span class="metric-label">准确率</span>
                  <span class="metric-value">95.2%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">召回率</span>
                  <span class="metric-value">92.8%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">F1分数</span>
                  <span class="metric-value">94.0%</span>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card class="page-card">
              <template #header>
                <span>任务信息</span>
              </template>
              <div class="task-info">
                <p><strong>任务名称：</strong>示例评测任务</p>
                <p><strong>模型：</strong>GPT-4</p>
                <p><strong>数据集：</strong>测试数据集</p>
                <p><strong>开始时间：</strong>2024-01-15 10:00:00</p>
                <p><strong>结束时间：</strong>2024-01-15 12:30:00</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这里可以根据路由参数获取具体的评测结果
</script>

<style scoped>
.evaluation-results {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 500;
  color: #606266;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.task-info p {
  margin: 8px 0;
  color: #606266;
}
</style>
