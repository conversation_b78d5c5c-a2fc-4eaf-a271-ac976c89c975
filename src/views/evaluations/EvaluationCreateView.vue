<template>
  <div class="evaluation-create content-container">
    <div class="page-header">
      <h1>创建评测任务</h1>
    </div>

    <el-card>
      <el-form :model="form" label-width="120px">
        <el-form-item label="任务名称">
          <el-input v-model="form.name" placeholder="请输入任务名称" />
        </el-form-item>

        <el-form-item label="选择模型">
          <el-select v-model="form.modelId" placeholder="请选择模型">
            <el-option label="GPT-4" value="1" />
            <el-option label="Claude-3" value="2" />
            <el-option label="Gemini Pro" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="数据集">
          <el-input v-model="form.dataset" placeholder="请输入数据集名称" />
        </el-form-item>

        <el-form-item label="任务描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入任务描述"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary">创建任务</el-button>
          <el-button @click="$router.back()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

const form = reactive({
  name: '',
  modelId: '',
  dataset: '',
  description: '',
})
</script>

<style scoped>
.evaluation-create {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
</style>
