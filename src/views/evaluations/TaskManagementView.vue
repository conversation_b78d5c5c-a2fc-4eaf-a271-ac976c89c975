<template>
  <div class="task-management-page content-container">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">评测任务管理</h1>
          <p class="page-subtitle">创建、监控和管理大模型评测任务</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleCreateTask">
            <el-icon><Plus /></el-icon>
            创建评测任务
          </el-button>
        </div>
      </div>

      <!-- 状态统计 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon blue">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">1</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon green">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">1</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon yellow">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">1</div>
            <div class="stat-label">等待中</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon red">
            <el-icon><Close /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">1</div>
            <div class="stat-label">失败</div>
          </div>
        </div>
      </div>

      <!-- 搜索筛选区 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="search-box">
            <el-input v-model="searchKeyword" placeholder="搜索任务名称或模型..." clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-controls">
            <el-select v-model="selectedModel" placeholder="全部模型" clearable>
              <el-option label="全部模型" value="" />
              <el-option label="VetGPT-Pro" value="vetgpt" />
              <el-option label="PetMed-Advanced" value="petmed" />
            </el-select>

            <el-select v-model="selectedStatus" placeholder="全部状态" clearable>
              <el-option label="全部状态" value="" />
              <el-option label="进行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="等待中" value="pending" />
              <el-option label="失败" value="failed" />
            </el-select>

            <el-button @click="handleReset">重置筛选</el-button>
          </div>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="task-grid">
        <div v-for="task in taskList" :key="task.id" class="task-card">
          <div class="task-header">
            <div class="task-title">{{ task.name }}</div>
            <el-tag :type="getStatusType(task.status)" size="small">
              {{ getStatusLabel(task.status) }}
            </el-tag>
          </div>

          <div class="task-info">
            <div class="info-item">
              <el-icon><Cpu /></el-icon>
              <span>{{ task.model }}</span>
            </div>
            <div class="info-item">
              <el-icon><DataBoard /></el-icon>
              <span>{{ task.dataset }}</span>
            </div>
          </div>

          <div class="task-progress">
            <div class="progress-label">进度</div>
            <el-progress
              :percentage="task.progress"
              :status="
                task.status === 'completed'
                  ? 'success'
                  : task.status === 'failed'
                    ? 'exception'
                    : undefined
              "
            />
          </div>

          <div class="task-meta">
            <div class="meta-item">
              <span class="meta-label">开始时间</span>
              <span class="meta-value">{{ formatDate(task.startTime) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">创建者</span>
              <span class="meta-value">{{ task.creator }}</span>
            </div>
          </div>

          <div class="task-actions">
            <el-button type="primary" size="small" @click="handleViewTask(task)">
              查看详情
            </el-button>
            <el-button
              v-if="task.status === 'running'"
              type="danger"
              size="small"
              @click="handleStopTask(task)"
            >
              停止任务
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Check,
  Clock,
  Close,
  Cpu,
  DataBoard,
  Document,
  Plus,
  Search,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const selectedModel = ref('')
const selectedStatus = ref('')

const taskList = ref([
  {
    id: 1,
    name: '小动物内科疾病诊断评测任务',
    model: 'VetGPT-Pro',
    dataset: '内科疾病数据集',
    status: 'running',
    progress: 65,
    startTime: '2024-01-25 10:30:00',
    creator: '张医生',
  },
  {
    id: 2,
    name: '宠物外科手术评测任务',
    model: 'PetMed-Advanced',
    dataset: '外科手术数据集',
    status: 'completed',
    progress: 100,
    startTime: '2024-01-24 14:23:00',
    creator: '李医生',
  },
  {
    id: 3,
    name: '宠物疫苗接种评测任务',
    model: 'AnimalCare-AI',
    dataset: '疫苗接种数据集',
    status: 'pending',
    progress: 0,
    startTime: '2024-01-23 09:15:00',
    creator: '王医生',
  },
])

// 方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    running: 'warning',
    completed: 'success',
    pending: 'info',
    failed: 'danger',
  }
  return types[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    running: '进行中',
    completed: '已完成',
    pending: '等待中',
    failed: '失败',
  }
  return labels[status] || status
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString()
}

const handleCreateTask = () => {
  ElMessage.info('创建评测任务功能开发中...')
}

const handleReset = () => {
  searchKeyword.value = ''
  selectedModel.value = ''
  selectedStatus.value = ''
}

const handleViewTask = (task: any) => {
  ElMessage.info(`查看任务详情: ${task.name}`)
}

const handleStopTask = async (task: any) => {
  try {
    await ElMessageBox.confirm(`确定要停止任务"${task.name}"吗？`, '确认停止', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    ElMessage.success('任务已停止')
  } catch {
    ElMessage.info('已取消操作')
  }
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.task-management-page {
  width: 100%;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.stat-icon.blue {
  background: #4285f4;
}

.stat-icon.green {
  background: #34a853;
}

.stat-icon.yellow {
  background: #fbbc04;
}

.stat-icon.red {
  background: #ea4335;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  line-height: 1;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 任务网格 */
.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.task-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  flex: 1;
  margin-right: 12px;
}

.task-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6b7280;
}

.info-item:last-child {
  margin-bottom: 0;
}

.task-progress {
  margin-bottom: 16px;
}

.progress-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.task-meta {
  margin-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  color: #6b7280;
}

.meta-value {
  color: #1f2937;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-controls {
    flex-wrap: wrap;
  }

  .task-grid {
    grid-template-columns: 1fr;
  }
}
</style>
