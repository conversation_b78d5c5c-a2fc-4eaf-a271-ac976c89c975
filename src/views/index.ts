// 认证相关
export { default as LoginView } from './auth/LoginView.vue'
export { default as RegisterView } from './auth/RegisterView.vue'

// 仪表盘
export { default as DashboardView } from './dashboard/DashboardView.vue'

// 数据集管理
export { default as DatasetOverviewView } from './datasets/DatasetOverviewView.vue'
export { default as DatasetView } from './datasets/DatasetView.vue'
export { default as DatasetCreateView } from './datasets/DatasetCreateView.vue'
export { default as DatasetEditView } from './datasets/DatasetEditView.vue'
export { default as DatasetDetailView } from './datasets/DatasetDetailView.vue'

// 数据集审核
export { default as DatasetReviewOverviewView } from './reviews/DatasetReviewOverviewView.vue'
export { default as DatasetReviewListView } from './reviews/DatasetReviewListView.vue'
export { default as DatasetReviewHistoryView } from './reviews/DatasetReviewHistoryView.vue'

// AI模型管理
export { default as ModelListView } from './models/ModelListView.vue'
export { default as ModelCreateView } from './models/ModelCreateView.vue'

// 评测相关
export { default as EvaluationResultsView } from './evaluations/EvaluationResultsView.vue'
export { default as EvaluationCreateView } from './evaluations/EvaluationCreateView.vue'
export { default as TaskManagementView } from './evaluations/TaskManagementView.vue'

// 系统管理
export { default as SystemManagementView } from './system/SystemManagementView.vue'
export { default as UserManagementView } from './system/UserManagementView.vue'
export { default as RoleManagementView } from './system/RoleManagementView.vue'
export { default as PermissionManagementView } from './system/PermissionManagementView.vue'

// 用户相关
export { default as ProfileView } from './user/ProfileView.vue'
export { default as SettingsView } from './user/SettingsView.vue'

// 通用页面
export { default as HomeView } from './common/HomeView.vue'
export { default as AboutView } from './common/AboutView.vue'
export { default as HelpView } from './common/HelpView.vue'
export { default as StatisticsView } from './common/StatisticsView.vue'
