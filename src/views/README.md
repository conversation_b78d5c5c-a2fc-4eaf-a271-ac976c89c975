# Views 目录结构说明

本目录按功能模块重新组织了所有Vue页面组件，提高了代码的可维护性和可读性。

## 📁 目录结构

```
src/views/
├── auth/                    # 🔐 认证相关
│   ├── LoginView.vue        # 登录页面
│   └── RegisterView.vue     # 注册页面
├── dashboard/               # 📊 仪表盘
│   └── DashboardView.vue    # 仪表盘主页
├── datasets/               # 📋 数据集管理
│   ├── DatasetOverviewView.vue    # 数据集管理概览
│   ├── DatasetView.vue            # 数据集列表
│   ├── DatasetCreateView.vue      # 创建数据集
│   ├── DatasetEditView.vue        # 编辑数据集
│   └── DatasetDetailView.vue      # 数据集详情
├── reviews/                # ✅ 数据集审核
│   ├── DatasetReviewOverviewView.vue  # 审核概览
│   ├── DatasetReviewListView.vue      # 审核列表
│   └── DatasetReviewHistoryView.vue   # 审核历史
├── models/                 # 🤖 AI模型管理
│   ├── ModelListView.vue    # 模型列表
│   └── ModelCreateView.vue  # 创建模型
├── evaluations/            # 🧪 评测相关
│   ├── EvaluationResultsView.vue  # 评测结果
│   ├── EvaluationCreateView.vue   # 创建评测
│   └── TaskManagementView.vue     # 任务管理
├── system/                 # ⚙️ 系统管理
│   ├── SystemManagementView.vue     # 系统管理主页
│   ├── UserManagementView.vue       # 用户管理
│   ├── RoleManagementView.vue       # 角色管理
│   └── PermissionManagementView.vue # 权限管理
├── user/                   # 👤 用户相关
│   ├── ProfileView.vue      # 个人资料
│   └── SettingsView.vue     # 用户设置
├── common/                 # 🌐 通用页面
│   ├── HomeView.vue         # 首页
│   ├── AboutView.vue        # 关于页面
│   ├── HelpView.vue         # 帮助页面
│   └── StatisticsView.vue   # 统计页面
├── index.ts                # 📦 统一导出文件
└── README.md               # 📖 本说明文件
```

## 🎯 分类原则

### 1. **功能模块分类**
- 按照业务功能将页面分组
- 相关功能的页面放在同一目录下
- 便于团队协作和代码维护

### 2. **命名规范**
- 目录名使用小写字母，多个单词用连字符分隔
- Vue文件名使用PascalCase命名
- 保持与原有命名习惯一致

### 3. **路由映射**
- 路由路径与目录结构保持一致
- 便于快速定位和理解页面层级关系

## 🔧 使用方式

### 1. **路由配置**
```typescript
// 推荐方式：直接导入
component: () => import('@/views/datasets/DatasetOverviewView.vue')

// 或者使用统一导出（可选）
import { DatasetOverviewView } from '@/views'
```

### 2. **添加新页面**
1. 确定页面所属的功能模块
2. 在对应目录下创建Vue文件
3. 更新路由配置
4. 如需要，更新 `index.ts` 导出文件

### 3. **目录扩展**
如果某个模块的页面过多，可以进一步细分：
```
src/views/system/
├── users/
│   ├── UserListView.vue
│   ├── UserCreateView.vue
│   └── UserEditView.vue
├── roles/
│   ├── RoleListView.vue
│   └── RoleCreateView.vue
└── SystemManagementView.vue
```

## 📋 迁移记录

### 已迁移的文件
- ✅ 认证相关：LoginView.vue, RegisterView.vue
- ✅ 仪表盘：DashboardView.vue
- ✅ 数据集管理：5个相关页面
- ✅ 数据集审核：3个相关页面
- ✅ AI模型：ModelListView.vue, ModelCreateView.vue
- ✅ 评测相关：3个相关页面
- ✅ 系统管理：4个相关页面
- ✅ 用户相关：ProfileView.vue, SettingsView.vue
- ✅ 通用页面：4个相关页面

### 更新的配置文件
- ✅ `src/router/index.ts` - 更新所有路由导入路径
- ✅ `src/views/index.ts` - 创建统一导出文件

## 🚀 优势

1. **更好的组织结构**：按功能模块分类，逻辑清晰
2. **提高开发效率**：快速定位相关页面
3. **便于团队协作**：不同开发者可以专注不同模块
4. **易于维护**：相关功能集中管理
5. **扩展性强**：新增功能模块时结构清晰

## 📝 注意事项

1. **保持一致性**：新增页面时遵循现有的分类原则
2. **路由同步**：添加新页面时记得更新路由配置
3. **导入路径**：使用新的目录结构导入路径
4. **团队沟通**：确保团队成员了解新的目录结构
