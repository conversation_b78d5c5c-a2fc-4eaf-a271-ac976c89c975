<template>
  <div class="system-management-page content-container hover-scrollbar">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-main">
            <h1 class="page-title">系统管理</h1>
            <p class="page-description">管理系统用户、角色权限及其他系统配置</p>
          </div>
          <div class="header-actions">
            <el-tooltip content="刷新页面" placement="bottom">
              <el-button circle @click="refreshPage">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 主体内容 -->
      <div class="main-container" v-if="hasAnyPermission">
        <!-- 左侧导航菜单 -->
        <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
          <div class="content-header">
            <div class="content-title">
              <span v-show="!sidebarCollapsed">功能模块</span>
            </div>
            <div class="sidebar-toggle">
              <el-button
                circle
                size="small"
                @click="toggleSidebar"
                :icon="sidebarCollapsed ? Expand : Fold"
              />
            </div>
          </div>
          <div class="menu-section">
            <div
              v-for="item in coreMenuItems"
              :key="item.name"
              class="menu-item"
              :class="{ active: activeMenu === item.name }"
              @click="switchMenu(item.name)"
              :title="sidebarCollapsed ? item.label : ''"
            >
              <el-icon class="menu-icon">
                <component :is="item.icon" />
              </el-icon>
              <span v-show="!sidebarCollapsed" class="menu-label">{{ item.label }}</span>
              <span v-if="item.badge && !sidebarCollapsed" class="menu-badge">{{
                item.badge
              }}</span>
            </div>
          </div>

          <!-- 预留扩展区域 -->
          <div class="menu-section" v-if="extendedMenuItems.length > 0">
            <div v-show="!sidebarCollapsed" class="section-title">扩展功能</div>
            <div
              v-for="item in extendedMenuItems"
              :key="item.name"
              class="menu-item"
              :class="{ active: activeMenu === item.name }"
              @click="switchMenu(item.name)"
              :title="sidebarCollapsed ? item.label : ''"
            >
              <el-icon class="menu-icon">
                <component :is="item.icon" />
              </el-icon>
              <span v-show="!sidebarCollapsed" class="menu-label">{{ item.label }}</span>
              <span v-if="item.badge && !sidebarCollapsed" class="menu-badge">{{
                item.badge
              }}</span>
            </div>
          </div>

          <!-- 系统设置区域 -->
          <div class="menu-section" v-if="systemMenuItems.length > 0">
            <div v-show="!sidebarCollapsed" class="section-title">系统设置</div>
            <div
              v-for="item in systemMenuItems"
              :key="item.name"
              class="menu-item"
              :class="{ active: activeMenu === item.name }"
              @click="switchMenu(item.name)"
              :title="sidebarCollapsed ? item.label : ''"
            >
              <el-icon class="menu-icon">
                <component :is="item.icon" />
              </el-icon>
              <span v-show="!sidebarCollapsed" class="menu-label">{{ item.label }}</span>
              <span v-if="item.badge && !sidebarCollapsed" class="menu-badge">{{
                item.badge
              }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="content-wrapper hover-scrollbar">
          <div class="content-header">
            <div class="content-title">
              <el-icon class="title-icon">
                <component :is="getCurrentMenuItem()?.icon || 'Setting'" />
              </el-icon>
              <span>{{ getCurrentMenuItem()?.label || '系统管理' }}</span>
            </div>
          </div>

          <div class="content-area hover-scrollbar">
            <!-- 核心管理模块 -->
            <UserManagement v-if="activeMenu === 'users' && userStore.hasPermission('user:read')" />
            <RoleManagement v-if="activeMenu === 'roles' && userStore.hasPermission('role:read')" />
            <PermissionManagement
              v-if="activeMenu === 'permissions' && userStore.hasPermission('permission:read')"
            />

            <!-- 扩展功能模块 -->
            <DataPermissionManagement
              v-if="activeMenu === 'data' && userStore.hasPermission('data:read')"
            />
            <DataDictManagement
              v-if="activeMenu === 'dict' && userStore.hasPermission('data_dict:read')"
            />
            <AuditManagement
              v-if="activeMenu === 'audit' && userStore.hasPermission('audit:read')"
            />
            <NotificationManagement
              v-if="activeMenu === 'notification' && userStore.hasPermission('notification:read')"
            />
            <DocumentManagement
              v-if="activeMenu === 'document' && userStore.hasPermission('document:read')"
            />

            <!-- 系统设置模块 -->
            <SystemConfig
              v-if="activeMenu === 'config' && userStore.hasPermission('config:read')"
            />
            <SecuritySettings
              v-if="activeMenu === 'security' && userStore.hasPermission('security:read')"
            />
            <LogManagement v-if="activeMenu === 'log' && userStore.hasPermission('log:read')" />
            <SystemMaintenance
              v-if="activeMenu === 'maintenance' && userStore.hasPermission('maintenance:read')"
            />

            <!-- 默认欢迎页面 -->
            <div v-if="!getCurrentMenuItem()" class="welcome-content">
              <el-empty description="请从左侧菜单选择要管理的功能模块">
                <template #image>
                  <el-icon size="64" color="#c0c4cc">
                    <Setting />
                  </el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>

      <!-- 无权限提示 -->
      <div v-else class="no-permission">
        <el-empty description="您没有访问系统管理功能的权限">
          <template #image>
            <el-icon size="64" color="#c0c4cc">
              <Lock />
            </el-icon>
          </template>
          <el-button type="primary" @click="$router.push('/app/home')"> 返回首页 </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 核心管理组件
import PermissionManagement from '@/components/system/PermissionManagement.vue'
import RoleManagement from '@/components/system/RoleManagement.vue'
import UserManagement from '@/components/system/UserManagement.vue'

// 扩展功能组件 (按需导入，如果组件不存在可以注释掉)
import DataDictManagement from '@/components/system/DataDictManagement.vue'
import DataPermissionManagement from '@/components/system/DataPermissionManagement.vue'
// import AuditManagement from '@/components/system/AuditManagement.vue'
// import NotificationManagement from '@/components/system/NotificationManagement.vue'
// import DocumentManagement from '@/components/system/DocumentManagement.vue'

// 系统设置组件 (按需导入，如果组件不存在可以注释掉)
// import SystemConfig from '@/components/system/SystemConfig.vue'
// import SecuritySettings from '@/components/system/SecuritySettings.vue'
// import LogManagement from '@/components/system/LogManagement.vue'
// import SystemMaintenance from '@/components/system/SystemMaintenance.vue'

import { useUserStore } from '@/stores/counter'
import { Expand, Fold, Lock, Refresh, Setting } from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const activeMenu = ref('users')
const sidebarCollapsed = ref(false)

// 核心管理菜单
const coreMenuItems = computed(() => {
  const items = []
  if (userStore.hasPermission('user:read')) {
    items.push({
      name: 'users',
      label: '用户管理',
      icon: 'User',
      badge: null,
    })
  }
  if (userStore.hasPermission('role:read')) {
    items.push({
      name: 'roles',
      label: '角色管理',
      icon: 'UserFilled',
      badge: null,
    })
  }
  if (userStore.hasPermission('permission:read')) {
    items.push({
      name: 'permissions',
      label: '权限管理',
      icon: 'Key',
      badge: null,
    })
  }
  return items
})

// 扩展功能菜单 - 可根据需要启用
const extendedMenuItems = computed(() => {
  const items = []
  // 数据权限管理
  if (userStore.hasPermission('data:read')) {
    items.push({
      name: 'data',
      label: '数据权限',
      icon: 'DataBoard',
      badge: null,
    })
  }
  // 数据字典管理
  if (userStore.hasPermission('data_dict:read')) {
    items.push({
      name: 'dict',
      label: '数据字典',
      icon: 'Collection',
      badge: null,
    })
  }
  // 审核管理
  if (userStore.hasPermission('audit:read')) {
    items.push({
      name: 'audit',
      label: '审核管理',
      icon: 'Monitor',
      badge: 'New',
    })
  }
  // 通知管理
  if (userStore.hasPermission('notification:read')) {
    items.push({
      name: 'notification',
      label: '通知管理',
      icon: 'Bell',
      badge: null,
    })
  }
  // 文档管理
  if (userStore.hasPermission('document:read')) {
    items.push({
      name: 'document',
      label: '文档管理',
      icon: 'Document',
      badge: null,
    })
  }
  return items
})

// 系统设置菜单
const systemMenuItems = computed(() => {
  const items = []
  // 系统配置
  if (userStore.hasPermission('config:read')) {
    items.push({
      name: 'config',
      label: '系统配置',
      icon: 'Setting',
      badge: null,
    })
  }
  // 安全设置
  if (userStore.hasPermission('security:read')) {
    items.push({
      name: 'security',
      label: '安全设置',
      icon: 'Shield',
      badge: null,
    })
  }
  // 日志管理
  if (userStore.hasPermission('log:read')) {
    items.push({
      name: 'log',
      label: '日志管理',
      icon: 'Files',
      badge: null,
    })
  }
  // 系统维护
  if (userStore.hasPermission('maintenance:read')) {
    items.push({
      name: 'maintenance',
      label: '系统维护',
      icon: 'Operation',
      badge: null,
    })
  }
  return items
})

// 获取所有菜单项（用于权限检查等）
const allMenuItems = computed(() => [
  ...coreMenuItems.value,
  ...extendedMenuItems.value,
  ...systemMenuItems.value,
])

// 获取当前选中的菜单项
const getCurrentMenuItem = () => {
  return allMenuItems.value.find((item) => item.name === activeMenu.value)
}

// 检查是否有任何系统管理权限
const hasAnyPermission = computed(() => {
  return allMenuItems.value.length > 0
})

// 根据路由参数设置活动菜单
const initActiveMenu = () => {
  const menu = route.query.menu as string
  if (menu && allMenuItems.value.some((item) => item.name === menu)) {
    // 检查权限映射
    const permissionMap: Record<string, string> = {
      users: 'user:read',
      roles: 'role:read',
      permissions: 'permission:read',
      data: 'data:read',
      dict: 'data_dict:read',
      audit: 'audit:read',
      notification: 'notification:read',
      document: 'document:read',
      config: 'config:read',
      security: 'security:read',
      log: 'log:read',
      maintenance: 'maintenance:read',
    }

    const requiredPermission = permissionMap[menu]
    if (requiredPermission && userStore.hasPermission(requiredPermission)) {
      activeMenu.value = menu
    } else {
      // 如果没有权限，选择第一个有权限的菜单
      if (allMenuItems.value.length > 0) {
        activeMenu.value = allMenuItems.value[0].name
      }
    }
  } else {
    // 默认选择第一个有权限的菜单
    if (allMenuItems.value.length > 0) {
      activeMenu.value = allMenuItems.value[0].name
    }
  }
}

// 菜单切换处理
const switchMenu = (menuName: string) => {
  activeMenu.value = menuName
  router.push({
    path: '/app/system',
    query: { menu: menuName },
  })
}

// 刷新页面
const refreshPage = () => {
  // 重新初始化活动菜单
  initActiveMenu()
  // 可以在这里添加其他刷新逻辑，比如重新加载数据
}

// 切换侧边栏展开/收起状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 添加新的系统管理模块的辅助方法 (供将来扩展使用)
// const addNewModule = (moduleConfig: {
//   name: string
//   label: string
//   icon: string
//   permission: string
//   category: 'core' | 'extended' | 'system'
//   badge?: string
// }) => {
//   // 这个方法可以用于动态添加新的系统管理模块
//   // 在实际使用时，可以通过配置文件或API来动态加载模块
//   console.log('添加新模块:', moduleConfig)
// }

onMounted(() => {
  initActiveMenu()
})
</script>

<style scoped>
.system-management-page {
  width: 100%;
  min-height: calc(100vh - 64px);
  overflow-x: auto; /* 允许水平滚动 */
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-main {
  flex: 1;
}

/* 主容器样式 */
.main-container {
  display: flex;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  min-height: 700px;
  max-width: 100%;
}

/* 侧边栏样式 */
.sidebar {
  width: 200px;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  flex-shrink: 0;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 80px;
}

/* 侧边栏头部样式 */
.sidebar .content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.sidebar.collapsed .content-header {
  justify-content: center;
  padding: 16px 12px;
}

.sidebar .content-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.sidebar-toggle {
  display: flex;
  align-items: center;
}

.sidebar.collapsed .sidebar-toggle {
  margin: 0;
}

/* 菜单分组样式 */
.menu-section {
  margin: 10px auto;
}

.menu-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 24px 12px 24px;
  margin-bottom: 8px;
  border-bottom: 1px solid #4285f4;
}

/* 菜单项样式 */
.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  margin: 0 12px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.menu-item:hover {
  color: #374151;
  background: rgba(59, 130, 246, 0.05);
}

.menu-item.active {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  font-weight: 600;
}

/* 收起状态下的菜单项样式 */
.sidebar.collapsed .menu-item {
  justify-content: center;
  padding: 12px;
  margin: 0 12px;
}

.sidebar.collapsed .menu-icon {
  margin: 0;
}

/* 收起状态下的菜单项样式 */
.sidebar.collapsed .menu-item {
  justify-content: center;
  padding: 12px;
  margin: 0 12px;
}

.sidebar.collapsed .menu-icon {
  margin: 0;
}

.menu-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.menu-label {
  flex: 1;
  white-space: nowrap;
}

.menu-badge {
  font-size: 10px;
  font-weight: 600;
  color: #ffffff;
  background: #ef4444;
  padding: 2px 6px;
  border-radius: 10px;
  line-height: 1;
}

/* 内容区域样式 */
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden; /* 防止内容溢出 */
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  font-size: 20px;
  color: #3b82f6;
}

.content-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-area {
  flex: 1;
  padding: 32px;
  min-height: 600px;
  background: #ffffff;
  overflow: auto; /* 允许滚动 */
}

.welcome-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 筛选区域全局样式 */
.sys-filter-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  justify-content: flex-start;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  justify-content: flex-end;
}

.sys-search-box {
  width: 300px;
  flex-shrink: 0;
}

.sys-filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格相关全局样式 */
.sys-table-section {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.sys-table-section .el-table {
  width: 100% !important;
}

/* 按钮组样式 */
.sys-btn-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 分页样式 */
.sys-pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* 对话框样式 */
.sys-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 状态标签样式 */
.sys-status-tag {
  font-weight: 500;
}

/* 无权限状态样式 */
.no-permission {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .page-title {
    font-size: 28px;
  }

  .page-description {
    font-size: 15px;
  }

  .content-area {
    padding: 24px;
  }

  .filter-left {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-right {
    justify-content: flex-end;
  }

  .sys-search-box {
    width: 100%;
  }

  .nav-tabs {
    padding: 0 16px;
  }

  .tab-item {
    padding: 14px 16px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    align-self: flex-end;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .nav-tabs {
    padding: 0 12px;
  }

  .tabs-container {
    gap: 2px;
  }

  .tab-item {
    padding: 12px 14px;
    font-size: 12px;
    gap: 6px;
  }

  .tab-icon {
    font-size: 14px;
  }

  .content-area {
    padding: 20px 16px;
  }

  .main-container {
    border-radius: 12px;
    min-height: 600px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }

  .tab-item {
    padding: 10px 12px;
    gap: 4px;
  }

  .tab-label {
    display: none;
  }

  .content-area {
    padding: 16px 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .system-management-page {
    background: #111827;
  }

  .page-title {
    color: #f9fafb;
  }

  .page-description {
    color: #9ca3af;
  }

  .main-container {
    background: #1f2937;
    border-color: #374151;
  }

  .sidebar {
    background: #111827;
    border-color: #374151;
  }

  .section-title {
    color: #9ca3af;
    border-color: #60a5fa;
  }

  .menu-item {
    color: #9ca3af;
  }

  .menu-item:hover {
    color: #d1d5db;
    background: rgba(59, 130, 246, 0.1);
  }

  .menu-item.active {
    color: #60a5fa;
    background: rgba(59, 130, 246, 0.15);
  }

  .content-wrapper {
    background: #1f2937;
  }

  .content-header {
    background: #1f2937;
    border-color: #374151;
  }

  .content-title {
    color: #f9fafb;
  }

  .content-area {
    background: #1f2937;
  }

  .no-permission {
    background: #1f2937;
    border-color: #374151;
  }
}
</style>
