<template>
  <div class="permission-management-page content-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">权限管理</h1>
        <p class="page-subtitle">查看和管理系统权限</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshPermissions">
          <el-icon><Refresh /></el-icon>
          刷新权限
        </el-button>
      </div>
    </div>

    <!-- 权限统计 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Key /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ permissions.length }}</div>
          <div class="stat-label">总权限数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ activePermissionsCount }}</div>
          <div class="stat-label">激活权限</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon resources">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ resourceOptions.length }}</div>
          <div class="stat-label">资源类型</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon actions">
          <el-icon><Operation /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ actionOptions.length }}</div>
          <div class="stat-label">操作类型</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索权限名称、描述或资源..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-controls">
          <el-select
            v-model="selectedResource"
            placeholder="资源类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="resource in resourceOptions"
              :key="resource"
              :label="resource"
              :value="resource"
            />
          </el-select>

          <el-select
            v-model="selectedAction"
            placeholder="操作类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="action in actionOptions"
              :key="action"
              :label="action"
              :value="action"
            />
          </el-select>

          <el-select v-model="selectedStatus" placeholder="状态" clearable @change="handleSearch">
            <el-option label="激活" value="true" />
            <el-option label="禁用" value="false" />
          </el-select>

          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 权限列表 -->
    <div class="table-section">
      <el-table :data="filteredPermissions" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="name" label="权限标识" width="200" />

        <el-table-column
          prop="description"
          label="权限描述"
          min-width="200"
          show-overflow-tooltip
        />

        <el-table-column prop="resource" label="资源" width="150">
          <template #default="{ row }">
            <el-tag type="info">{{ row.resource || '未分类' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="action" label="操作" width="120">
          <template #default="{ row }">
            <el-tag>{{ row.action || '未定义' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="editPermission(row)">编辑</el-button>
            <el-button type="primary" link @click="viewPermissionDetails(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 编辑权限对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑权限" width="600px" @close="resetForm">
      <el-form
        ref="permissionFormRef"
        :model="permissionForm"
        :rules="permissionRules"
        label-width="100px"
      >
        <el-form-item label="权限标识">
          <el-input v-model="permissionForm.name" readonly />
        </el-form-item>

        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>

        <el-form-item label="资源" prop="resource">
          <el-input v-model="permissionForm.resource" placeholder="请输入资源名称" />
        </el-form-item>

        <el-form-item label="操作" prop="action">
          <el-input v-model="permissionForm.action" placeholder="请输入操作名称" />
        </el-form-item>

        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="permissionForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="savePermission" :loading="saving"> 更新 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="权限详情" width="700px">
      <div class="permission-detail" v-if="currentPermission">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>权限ID:</label>
              <span>{{ currentPermission.id }}</span>
            </div>
            <div class="detail-item">
              <label>权限标识:</label>
              <span>{{ currentPermission.name }}</span>
            </div>
            <div class="detail-item">
              <label>权限描述:</label>
              <span>{{ currentPermission.description || '无描述' }}</span>
            </div>
            <div class="detail-item">
              <label>资源:</label>
              <span>{{ currentPermission.resource || '未分类' }}</span>
            </div>
            <div class="detail-item">
              <label>操作:</label>
              <span>{{ currentPermission.action || '未定义' }}</span>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <el-tag :type="currentPermission.is_active ? 'success' : 'danger'">
                {{ currentPermission.is_active ? '激活' : '禁用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(currentPermission.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间:</label>
              <span>{{ formatDate(currentPermission.updated_at) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>关联角色</h3>
          <div class="roles-list" v-loading="rolesLoading">
            <el-tag v-for="role in permissionRoles" :key="role.id" class="role-tag" type="primary">
              {{ role.display_name }}
            </el-tag>
            <span v-if="permissionRoles.length === 0" class="no-data">暂无关联角色</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getPermissionList,
  updatePermission,
  type Permission,
  type UpdatePermissionRequest,
} from '@/api/permissions'
import { getPermissionRoles } from '@/api/roles'
import { useUserStore } from '@/stores/counter'
import { CircleCheck, FolderOpened, Key, Operation, Refresh, Search } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

const userStore = useUserStore()

// 响应式数据
const permissions = ref<Permission[]>([])
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const selectedResource = ref('')
const selectedAction = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const editingPermission = ref<Permission | null>(null)
const currentPermission = ref<Permission | null>(null)
const permissionFormRef = ref<FormInstance>()

// 权限详情相关
const permissionRoles = ref<any[]>([])
const rolesLoading = ref(false)

// 表单数据
const permissionForm = reactive({
  name: '',
  description: '',
  resource: '',
  action: '',
  is_active: true,
})

// 表单验证规则
const permissionRules: FormRules = {
  description: [{ max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }],
  resource: [{ max: 50, message: '资源名称不能超过 50 个字符', trigger: 'blur' }],
  action: [{ max: 50, message: '操作名称不能超过 50 个字符', trigger: 'blur' }],
}

// 计算属性
const filteredPermissions = computed(() => {
  let result = permissions.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (permission) =>
        permission.name.toLowerCase().includes(keyword) ||
        permission.description?.toLowerCase().includes(keyword) ||
        permission.resource?.toLowerCase().includes(keyword) ||
        permission.action?.toLowerCase().includes(keyword),
    )
  }

  // 资源过滤
  if (selectedResource.value) {
    result = result.filter((permission) => permission.resource === selectedResource.value)
  }

  // 操作过滤
  if (selectedAction.value) {
    result = result.filter((permission) => permission.action === selectedAction.value)
  }

  // 状态过滤
  if (selectedStatus.value !== '') {
    const isActive = selectedStatus.value === 'true'
    result = result.filter((permission) => permission.is_active === isActive)
  }

  return result
})

const resourceOptions = computed(() => {
  const resources = new Set<string>()
  permissions.value.forEach((permission) => {
    if (permission.resource) {
      resources.add(permission.resource)
    }
  })
  return Array.from(resources).sort()
})

const actionOptions = computed(() => {
  const actions = new Set<string>()
  permissions.value.forEach((permission) => {
    if (permission.action) {
      actions.add(permission.action)
    }
  })
  return Array.from(actions).sort()
})

const activePermissionsCount = computed(() => {
  return permissions.value.filter((p) => p.is_active).length
})

// 方法
const loadPermissions = async () => {
  loading.value = true
  try {
    const data = await getPermissionList()
    permissions.value = data
    total.value = data.length
  } catch (error: any) {
    ElMessage.error(error.message || '加载权限列表失败')
  } finally {
    loading.value = false
  }
}

const refreshPermissions = () => {
  loadPermissions()
  ElMessage.success('权限列表已刷新')
}

const handleSearch = () => {
  // 搜索在计算属性中处理
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedResource.value = ''
  selectedAction.value = ''
  selectedStatus.value = ''
}

const editPermission = (permission: Permission) => {
  editingPermission.value = permission
  Object.assign(permissionForm, {
    name: permission.name,
    description: permission.description,
    resource: permission.resource,
    action: permission.action,
    is_active: permission.is_active,
  })
  showEditDialog.value = true
}

const savePermission = async () => {
  if (!permissionFormRef.value || !editingPermission.value) return

  try {
    await permissionFormRef.value.validate()
    saving.value = true

    const updateData: UpdatePermissionRequest = {
      description: permissionForm.description,
      resource: permissionForm.resource,
      action: permissionForm.action,
      is_active: permissionForm.is_active,
    }

    await updatePermission(editingPermission.value.id, updateData)
    ElMessage.success('权限更新成功')
    showEditDialog.value = false
    loadPermissions()
  } catch (error: any) {
    ElMessage.error(error.message || '更新失败')
  } finally {
    saving.value = false
  }
}

const viewPermissionDetails = async (permission: Permission) => {
  currentPermission.value = permission
  showDetailDialog.value = true

  // 加载关联角色
  try {
    rolesLoading.value = true
    const roles = await getPermissionRoles(permission.id)
    permissionRoles.value = roles
  } catch (error: any) {
    ElMessage.error(error.message || '加载关联角色失败')
    permissionRoles.value = []
  } finally {
    rolesLoading.value = false
  }
}

const resetForm = () => {
  editingPermission.value = null
  Object.assign(permissionForm, {
    name: '',
    description: '',
    resource: '',
    action: '',
    is_active: true,
  })
  permissionFormRef.value?.resetFields()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 工具方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadPermissions()
})
</script>

<style scoped>
.permission-management-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 400px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.stat-icon.resources {
  background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
}

.stat-icon.actions {
  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.permission-detail {
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
}

.detail-item span {
  color: #303133;
}

.roles-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  align-items: flex-start;
}

.role-tag {
  margin: 0;
}

.no-data {
  color: #909399;
  font-style: italic;
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .filter-controls {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .stats-section {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
