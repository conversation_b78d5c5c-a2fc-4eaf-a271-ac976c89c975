<template>
  <div class="user-management-page content-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-subtitle">管理系统用户信息和权限</p>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-left">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户名或邮箱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-controls">
            <el-select
              v-model="selectedUserSource"
              placeholder="用户类型"
              clearable
              @change="loadUsers"
              style="width: 140px"
            >
              <el-option
                v-for="option in userSourceOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <el-select
              v-model="selectedStatus"
              placeholder="状态"
              clearable
              @change="loadUsers"
              style="width: 120px"
            >
              <el-option label="激活" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>
          </div>
        </div>

        <div class="filter-right">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-table :data="filteredUsers" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="username" label="用户名" width="150" />

        <el-table-column prop="email" label="邮箱" width="200" />

        <el-table-column prop="full_name" label="真实姓名" width="150" />

        <el-table-column prop="user_source" label="用户类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getUserSourceType(row.user_source)">
              {{ getUserSourceLabel(row.user_source) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="editUser(row)">编辑</el-button>
            <el-button type="primary" link @click="manageUserRoles(row)">角色</el-button>
            <el-button
              type="danger"
              link
              @click="deleteUser(row)"
              :disabled="row.id === userStore.user?.id"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingUser ? '编辑用户' : '新增用户'"
      width="600px"
      @close="resetForm"
    >
      <el-form ref="userFormRef" :model="userForm" :rules="userRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="真实姓名" prop="full_name">
          <el-input v-model="userForm.full_name" placeholder="请输入真实姓名" />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="!editingUser">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>

        <el-form-item label="用户类型" prop="user_source">
          <el-select
            v-model="userForm.user_source"
            placeholder="请选择用户类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in userSourceOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="userForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveUser" :loading="saving">
            {{ editingUser ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  deleteUser as apiDeleteUser,
  createUser,
  getUserList,
  updateUser,
  userSourceOptions,
  type CreateUserRequest,
  type UpdateUserRequest,
  type User,
} from '@/api/users'
import { useUserStore } from '@/stores/counter'
import { showError, showInfo, showSuccess } from '@/utils'
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

const userStore = useUserStore()

// 响应式数据
const users = ref<User[]>([])
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const selectedUserSource = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const showCreateDialog = ref(false)
const editingUser = ref<User | null>(null)
const userFormRef = ref<FormInstance>()

// 表单数据
const userForm = reactive<CreateUserRequest & { is_active: boolean }>({
  username: '',
  email: '',
  password: '',
  full_name: '',
  user_source: 'individual',
  is_active: true,
})

// 表单验证规则
const userRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  full_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' },
  ],
  user_source: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
}

// 计算属性
const filteredUsers = computed(() => {
  let result = users.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (user) =>
        user.username.toLowerCase().includes(keyword) ||
        user.email.toLowerCase().includes(keyword) ||
        user.full_name.toLowerCase().includes(keyword),
    )
  }

  return result
})

// 方法
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
    }
    const data = await getUserList(params)
    users.value = data
    total.value = data.length // 注意：实际应该从API返回总数
  } catch (error: any) {
    console.error('加载用户列表失败:', error) // 调试信息
    showError(error.message || '加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索在计算属性中处理
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedUserSource.value = ''
  selectedStatus.value = ''
  loadUsers()
}

const editUser = (user: User) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    email: user.email,
    full_name: user.full_name,
    user_source: user.user_source,
    is_active: user.is_active,
    password: '', // 编辑时不显示密码
  })
  showCreateDialog.value = true
}

const saveUser = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    saving.value = true

    if (editingUser.value) {
      // 更新用户
      const updateData: UpdateUserRequest = {
        username: userForm.username,
        email: userForm.email,
        full_name: userForm.full_name,
        user_source: userForm.user_source,
        is_active: userForm.is_active,
      }
      if (userForm.password) {
        updateData.password = userForm.password
      }
      await updateUser(editingUser.value.id, updateData)
      showSuccess('用户更新成功')
    } else {
      // 创建用户
      await createUser(userForm)
      showSuccess('用户创建成功')
    }

    showCreateDialog.value = false
    loadUsers()
  } catch (error: any) {
    showError(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

const deleteUser = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await apiDeleteUser(user.id)
    showSuccess('用户删除成功')
    loadUsers()
  } catch (error: any) {
    if (error !== 'cancel') {
      showError(error.message || '删除失败')
    }
  }
}

const manageUserRoles = (_user: User) => {
  // TODO: 实现角色管理功能
  showInfo('角色管理功能开发中...')
}

const resetForm = () => {
  editingUser.value = null
  Object.assign(userForm, {
    username: '',
    email: '',
    password: '',
    full_name: '',
    user_source: 'individual',
    is_active: true,
  })
  userFormRef.value?.resetFields()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadUsers()
}

const getUserSourceLabel = (source: string) => {
  const option = userSourceOptions.find((opt) => opt.value === source)
  return option?.label || source
}

const getUserSourceType = (source: string) => {
  const typeMap: Record<string, string> = {
    enterprise: 'primary',
    individual: 'success',
    school: 'warning',
    hospital: 'info',
    government: 'danger',
  }
  return typeMap[source] || 'default'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management-page {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.search-box {
  width: 300px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-left {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-right {
    justify-content: flex-end;
  }

  .search-box {
    width: 100%;
  }

  .filter-controls {
    justify-content: flex-start;
  }
}
</style>
