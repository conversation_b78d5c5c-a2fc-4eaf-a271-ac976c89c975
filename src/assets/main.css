@import './base.css';

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    width: 100%;
    padding: 0;
  }
}

/* 全局分页样式优化 */
.el-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
  font-size: 14px;
}

.el-pagination .el-pagination__total {
  color: #606266;
  font-weight: 500;
}

.el-pagination .el-pagination__sizes {
  margin-right: 16px;
}

.el-pagination .el-pagination__sizes .el-select {
  width: 100px;
}

.el-pagination .el-pager {
  display: flex;
  align-items: center;
  gap: 4px;
}

.el-pagination .el-pager .number,
.el-pagination .btn-prev,
.el-pagination .btn-next {
  min-width: 32px;
  height: 32px;
  line-height: 30px;
  padding: 0 8px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: white;
  color: #606266;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.el-pagination .el-pager .number:hover,
.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {
  color: #409eff;
  border-color: #409eff;
  background: #ecf5ff;
}

.el-pagination .el-pager .number.is-active {
  color: white;
  background: #409eff;
  border-color: #409eff;
  font-weight: 600;
}

.el-pagination .btn-prev:disabled,
.el-pagination .btn-next:disabled {
  color: #c0c4cc;
  background: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.el-pagination .el-pagination__jump {
  margin-left: 16px;
  color: #606266;
}

.el-pagination .el-pagination__jump .el-input {
  width: 60px;
  margin: 0 8px;
}

.el-pagination .el-pagination__jump .el-input .el-input__inner {
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: 6px;
}

/* 响应式分页 */
@media (max-width: 768px) {
  .el-pagination {
    flex-wrap: wrap;
    gap: 8px;
  }

  .el-pagination .el-pagination__total,
  .el-pagination .el-pagination__jump {
    order: 3;
    width: 100%;
    text-align: center;
    margin: 8px 0 0 0;
  }
}
