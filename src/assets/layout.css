/* PC端布局专用样式 */

/* 强制全宽布局 */
html,
body {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto;
}

#app {
  width: 100% !important;
  max-width: 100% !important;
}

/* Element Plus 容器强制全宽 */
.el-container {
  width: 100% !important;
  max-width: 100% !important;
}

/* 主布局容器 */
.main-layout {
  width: 100% !important;
  max-width: 100% !important;
}

/* 侧边栏固定宽度 */
.el-aside {
  flex-shrink: 0 !important;
}

/* 主内容区自适应 */
.el-main {
  flex: 1 !important;
  width: auto !important;
  max-width: none !important;
}

/* 头部导航栏 */
.el-header {
  width: 100% !important;
  flex-shrink: 0 !important;
}

/* 登录页面全宽 */
.login-container {
  width: 100% !important;
  max-width: 100% !important;
}

/* 页面容器样式 - 基础容器，不添加padding */
.page-container {
  width: 100%;
  box-sizing: border-box;
}

/* 内容容器样式 - 应用页面使用 */
.content-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px clamp(20px, 8vw, 100px);
  box-sizing: border-box;
}

/* 统一的页面头部样式 */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-main {
  flex: 1;
}

.header-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 通用的悬停显示滚动条样式 */
.hover-scrollbar {
  /* 自定义滚动条样式 - 默认隐藏 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* 鼠标悬停时显示滚动条 */
.hover-scrollbar:hover {
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Webkit浏览器滚动条样式 */
.hover-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.hover-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.hover-scrollbar::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

/* 鼠标悬停时显示滚动条 */
.hover-scrollbar:hover::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.hover-scrollbar:hover::-webkit-scrollbar-thumb {
  background: #c1c1c1;
}

.hover-scrollbar:hover::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 统一的页面标题样式 */
.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  font-size: 16px;
  color: #718096;
  margin: 0;
  line-height: 1.5;
}

/* 兼容旧的类名 */
.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  line-height: 1.5;
}

/* 统一的卡片样式 */
.page-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.page-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 统一的内容区域样式 */
.content-section {
  margin-bottom: 24px;
}

.content-section:last-child {
  margin-bottom: 0;
}
