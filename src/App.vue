<script setup lang="ts">
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { RouterView } from 'vue-router'
</script>

<template>
  <ElConfigProvider :locale="zhCn as any">
    <RouterView />
  </ElConfigProvider>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  width: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  background-color: #f0f2f5;
  min-width: 1200px; /* 确保最小宽度，适合PC端 */
  overflow-x: auto; /* 当内容超出时显示水平滚动条 */
}

#app {
  height: 100%;
  width: 100%;
  min-height: 100vh;
  min-width: 1200px;
}

/* PC端专用样式 */
@media (max-width: 1199px) {
  body {
    min-width: 1200px;
  }
}

/* 确保Element Plus组件在PC端的正确显示 */
.el-container {
  width: 100% !important;
  min-width: 1200px;
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
