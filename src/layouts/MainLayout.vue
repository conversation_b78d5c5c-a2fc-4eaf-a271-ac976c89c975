<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <TopNavbar />

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-container">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TopNavbar from '@/components/TopNavbar.vue'
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.main-content {
  padding-top: 64px; /* 为固定的顶部导航栏留出空间 */
  min-height: 100vh;
  background-color: #f8f9fa;
  max-width: 100%;
  box-sizing: border-box;
}

/* page-container样式已在layout.css中定义，这里不需要重复 */
</style>
