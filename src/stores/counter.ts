import type { LoginRequest, User } from '@/api/auth'
import { login as apiLogin, getCurrentUser } from '@/api/auth'
import { permissionApi } from '@/api/permissions'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isLoggedIn = ref(false)
  const permissions = ref<string[]>([])
  const loading = ref(false)

  // 登录
  async function login(loginData: LoginRequest) {
    loading.value = true
    try {
      const response = await apiLogin(loginData)

      // 保存token
      localStorage.setItem('access_token', response.access_token)

      // 获取用户信息
      console.log('获取用户信息...')
      const userData = await getCurrentUser()
      user.value = userData
      isLoggedIn.value = true
      localStorage.setItem('user', JSON.stringify(userData))
      console.log('用户信息获取成功:', userData)

      // 获取用户权限
      console.log('开始获取用户权限...')
      await loadUserPermissions()
      console.log('权限获取完成，当前权限:', permissions.value)

      return { success: true, data: userData }
    } catch (error: any) {
      console.error('登录失败:', error)
      return { success: false, message: error.message || '登录失败' }
    } finally {
      loading.value = false
    }
  }

  // 登出
  function logout() {
    user.value = null
    isLoggedIn.value = false
    permissions.value = []
    localStorage.removeItem('access_token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  async function initUser() {
    const savedUser = localStorage.getItem('user')
    const token = localStorage.getItem('access_token')

    if (savedUser && token) {
      try {
        user.value = JSON.parse(savedUser)
        isLoggedIn.value = true

        // 验证token有效性并获取最新用户信息
        const userData = await getCurrentUser()
        user.value = userData
        localStorage.setItem('user', JSON.stringify(userData))

        // 加载用户权限
        await loadUserPermissions()
      } catch (error) {
        console.error('初始化用户信息失败:', error)
        logout()
      }
    }
  }

  // 加载用户权限
  async function loadUserPermissions() {
    // console.log('开始加载用户权限...')
    try {
      const response = await permissionApi.getMyPermissions()
      // console.log('权限API响应:', response)

      if (response.success) {
        // 修复：后端实际返回的是权限对象数组，而非字符串数组
        // API类型定义与实际返回不一致
        if (Array.isArray(response.data) && response.data.length > 0) {
          // 如果data本身就是数组（实际返回格式）
          permissions.value = response.data.map((perm: { name: string }) => perm.name)
        } else if (Array.isArray(response.data.permissions) && response.data.permissions.length > 0) {
          // 如果data.permissions是数组（按类型定义）
          if (typeof response.data.permissions[0] === 'object' && response.data.permissions[0] !== null) {
            // 如果是对象数组，提取name属性
            permissions.value = response.data.permissions.map((perm: { name: string }) => perm.name)
          } else {
            // 如果是字符串数组，直接使用
            permissions.value = response.data.permissions
          }
        } else {
          permissions.value = []
        }
        // console.log('用户权限加载成功:', permissions.value)
        // console.log('权限数量:', permissions.value.length)
      } else {
        console.error('获取权限失败:', response.message)
        permissions.value = []
      }
    } catch (error) {
      console.error('加载用户权限失败:', error)
      console.error('错误详情:', error)
      permissions.value = []
    }
  }

  // 检查权限
  function hasPermission(permission: string): boolean {
    return permissions.value.includes(permission)
  }

  // 检查是否有任一权限
  function hasAnyPermission(permissionList: string[]): boolean {
    return permissionList.some((permission) => permissions.value.includes(permission))
  }

  // 检查是否有所有权限
  function hasAllPermissions(permissionList: string[]): boolean {
    return permissionList.every((permission) => permissions.value.includes(permission))
  }

  // 更新用户信息
  function updateUserInfo(userData: User) {
    user.value = userData
    localStorage.setItem('user', JSON.stringify(userData))
  }

  return {
    user,
    isLoggedIn,
    permissions,
    loading,
    login,
    logout,
    initUser,
    loadUserPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    updateUserInfo,
  }
})
