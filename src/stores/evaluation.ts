import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { EvaluationTask, Model, PaginationParams } from '@/types'
import { evaluationApi, modelApi } from '@/api'

export const useEvaluationStore = defineStore('evaluation', () => {
  const tasks = ref<EvaluationTask[]>([])
  const models = ref<Model[]>([])
  const loading = ref(false)
  const pagination = ref<PaginationParams>({
    page: 1,
    pageSize: 10,
    total: 0,
  })

  const runningTasks = computed(() => tasks.value.filter((task) => task.status === 'running'))

  const completedTasks = computed(() => tasks.value.filter((task) => task.status === 'completed'))

  // 获取评测任务列表
  async function fetchTasks(params?: PaginationParams & { status?: string; modelId?: string }) {
    loading.value = true
    try {
      const response = await evaluationApi.getTasks(params)
      tasks.value = response.data.items
      pagination.value = response.data.pagination
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取模型列表
  async function fetchModels() {
    try {
      const response = await modelApi.getModels({ page: 1, pageSize: 100 })
      models.value = response.data.items
      return response
    } catch (error) {
      throw error
    }
  }

  // 创建评测任务
  async function createTask(
    taskData: Omit<EvaluationTask, 'id' | 'status' | 'progress' | 'createdAt' | 'updatedAt'>,
  ) {
    try {
      const response = await evaluationApi.createTask(taskData)
      tasks.value.unshift(response.data)
      return response
    } catch (error) {
      throw error
    }
  }

  // 启动评测任务
  async function startTask(taskId: string) {
    try {
      const response = await evaluationApi.startTask(taskId)
      const taskIndex = tasks.value.findIndex((task) => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value[taskIndex].status = 'running'
      }
      return response
    } catch (error) {
      throw error
    }
  }

  // 停止评测任务
  async function stopTask(taskId: string) {
    try {
      const response = await evaluationApi.stopTask(taskId)
      const taskIndex = tasks.value.findIndex((task) => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value[taskIndex].status = 'pending'
      }
      return response
    } catch (error) {
      throw error
    }
  }

  // 删除评测任务
  async function deleteTask(taskId: string) {
    try {
      const response = await evaluationApi.deleteTask(taskId)
      const taskIndex = tasks.value.findIndex((task) => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value.splice(taskIndex, 1)
      }
      return response
    } catch (error) {
      throw error
    }
  }

  // 更新任务状态
  function updateTaskStatus(taskId: string, status: EvaluationTask['status'], progress?: number) {
    const taskIndex = tasks.value.findIndex((task) => task.id === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].status = status
      if (progress !== undefined) {
        tasks.value[taskIndex].progress = progress
      }
    }
  }

  return {
    tasks,
    models,
    loading,
    pagination,
    runningTasks,
    completedTasks,
    fetchTasks,
    fetchModels,
    createTask,
    startTask,
    stopTask,
    deleteTask,
    updateTaskStatus,
  }
})
