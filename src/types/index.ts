// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  role: 'admin' | 'user'
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
}

// 模型相关类型
export interface Model {
  id: string
  name: string
  description: string
  version: string
  type: 'llm' | 'vision' | 'multimodal'
  provider: string
  status: 'active' | 'inactive' | 'training'
  createdAt: string
  updatedAt: string
}

// 评测任务相关类型
export interface EvaluationTask {
  id: string
  name: string
  description: string
  modelId: string
  modelName: string
  dataset: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  startTime?: string
  endTime?: string
  createdAt: string
  updatedAt: string
}

export interface EvaluationResult {
  id: string
  taskId: string
  modelId: string
  metrics: Record<string, number>
  details: any
  createdAt: string
}

// 性能指标类型
export interface PerformanceMetric {
  name: string
  value: number
  unit: string
  description: string
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string[]
    borderColor?: string[]
    borderWidth?: number
  }[]
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

export interface ListResponse<T> {
  items: T[]
  pagination: PaginationParams
}

// 数据字典相关类型

// 分类模型 (DictCategory)
export interface DictCategory {
  id: number // 自增主键ID
  code: string // 分类编码（唯一）
  name: string // 分类中文名称
  description?: string // 分类描述
  sort_order: number // 排序顺序
  is_active: number // 是否启用：1-启用 0-禁用
  created_at: string // 创建时间
  updated_at: string // 更新时间
}

// 字典数据模型 (DataDict)
export interface DataDict {
  id: number // 自增主键ID
  category_id: number // 分类ID（整数，关联分类表）
  category_name?: string // 分类中文名称（查询时自动填充）
  parent_id: number // 父级ID，0表示顶级
  code: string // 字典编码
  name: string // 字典名称
  description?: string // 描述
  sort_order: number // 排序
  is_active: number // 是否启用：1-启用 0-禁用
  extra_data?: any // 扩展数据（JSON）
  created_at: string // 创建时间
  updated_at: string // 更新时间
}

// 字典选项模型 (用于下拉选择)
export interface DictOption {
  id: number
  name: string
  code: string
  parent_id: number
  sort_order: number
}

// 字典树形节点
export interface DictTreeNode extends DictOption {
  children: DictTreeNode[]
}

// 分类创建请求
export interface CreateCategoryRequest {
  code: string // 必填：分类编码
  name: string // 必填：分类名称
  description?: string // 可选：描述
  sort_order?: number // 可选：排序，默认0
}

// 分类更新请求
export interface UpdateCategoryRequest {
  name?: string // 可选：分类名称
  description?: string // 可选：描述
  sort_order?: number // 可选：排序
}

// 字典数据创建请求
export interface CreateDictRequest {
  category_id: number // 必填：分类ID（整数）
  parent_id?: number // 可选：父级ID，默认0
  code: string // 必填：字典编码
  name: string // 必填：字典名称
  description?: string // 可选：描述
  sort_order?: number // 可选：排序，默认0
  is_active?: number // 可选：是否启用，默认1
  extra_data?: any // 可选：扩展数据
}

// 字典数据更新请求
export interface UpdateDictRequest {
  name?: string
  description?: string
  sort_order?: number
  is_active?: number
  extra_data?: any
}

// 保持向后兼容的旧接口
export interface DataDictOption {
  id: number
  name: string
  value: string
}

export interface DataDictItem {
  id: number
  category: string
  name: string
  value: string
  parent_id: number
  sort_order: number
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// 数据集相关类型
export interface Dataset {
  id: number
  title: string
  description: string
  dataset_type: DatasetType
  dataset_status: DatasetStatus
  status_name?: string // 状态名称
  dataset_type_name?: string // 类型名称
  creator_id: number
  tags: string[]
  extra_metadata?: Record<string, any>
  data: DatasetData
  created_at: string
  updated_at: string
}

// 数据集类型枚举
export enum DatasetType {
  COMPREHENSIVE_CASE = 1, // 综合性病例数据集
  MEDICAL_IMAGING = 2, // 影像数据集
  EXAM_QUESTIONS = 3, // 考试数据集
}

// 数据集状态枚举
export enum DatasetStatus {
  DRAFT = 1, // 草稿
  PENDING = 2, // 待审核
  APPROVED = 3, // 已通过
  REJECTED = 4, // 已拒绝
  ARCHIVED = 5, // 已归档
}

// 数据集数据类型（联合类型）
export type DatasetData = ComprehensiveCaseData | MedicalImagingData | ExamQuestionData

// 综合性病例数据结构
export interface ComprehensiveCaseData {
  id?: number
  dataset_id?: number
  case_labels?: number[]
  specialty_category_id?: number
  department_disease_type_id?: number | null
  animal_category_id?: number
  animal_breed_id?: number
  gender_id?: number
  age_value?: number
  age_unit_id?: number
  weight?: number
  is_neutered?: number
  chief_complaint?: string
  medical_history?: string
  physical_examination?: string | null
  laboratory_examination?: string | null
  lab_images?: string | null
  diagnosis_result?: string
  treatment_plan?: string
  medication_info?: string | null
  case_summary?: string | null
  created_at?: string
  updated_at?: string

  // 兼容旧版本字段
  animal_type_id?: number
  age_months?: number
  weight_kg?: number
  symptoms?: string
  diagnosis?: string
  treatment?: string
  outcome?: string
}

// 影像数据集数据结构
export interface MedicalImagingData {
  id?: number
  dataset_id?: number
  system_type_id?: number
  image_category_id?: number
  detailed_category_id?: number
  animal_breed_id?: number
  image_signs?: string
  detailed_description?: string
  diagnosis_suggestion?: string
  image_files?: string
  created_at?: string
  updated_at?: string
}

// 单个考试题目数据结构
export interface ExamQuestion {
  id?: number
  question_type_id: number
  subject_id: number
  difficulty_level_id: number
  question_title: string
  question_description: string
  question_content: string
  options: Record<string, string> | null
  correct_answer: {
    answer: string | string[]
    type: 'single' | 'multiple'
  }
  answer_explanation: string
  question_images?: string[] | null
  question_metadata?: Record<string, any> | null
  sort_order?: number
  created_at?: string
  updated_at?: string

  // 兼容旧版本字段
  question_text?: string
  explanation?: string
}

// 考试题目数据集数据结构（包含多道题目）
export interface ExamQuestionData {
  questions: ExamQuestion[]
  dataset_metadata?: {
    total_questions?: number
    description?: string
    tags?: string[]
  }

  // 兼容旧版本单题目结构
  id?: number
  dataset_id?: number
  question_type_id?: number
  subject_id?: number
  difficulty_level_id?: number
  question_title?: string
  question_description?: string
  question_content?: string
  options?: Record<string, string> | null
  correct_answer?: {
    answer: string | string[]
    type: 'single' | 'multiple'
  }
  answer_explanation?: string
  question_images?: string[] | null
  question_metadata?: Record<string, any> | null
  question_text?: string
  explanation?: string
}

// 数据集统计信息
export interface DatasetStats {
  total_datasets: number
  by_type: Record<string, number>
  by_status: Record<string, number>
  my_datasets: number
  type_details?: Record<
    string,
    {
      count: number
      last_updated: string
      contributors: number
    }
  >
}

// 数据集列表响应
export interface DatasetListResponse {
  items: Dataset[]
  total: number
  page: number
  size: number
  pages: number
}

// 数据集创建/更新请求
export interface DatasetCreateRequest {
  title: string
  description: string
  dataset_type: DatasetType
  tags: string[]
  data: DatasetData
}

export interface DatasetUpdateRequest extends DatasetCreateRequest {}

// 数据集审核相关类型
export interface DatasetReview {
  id: number
  dataset_id: number
  reviewer_id: number
  review_status: ReviewStatus
  review_comment?: string
  review_reason?: string
  reviewed_at: string
  dataset: {
    id: number
    title: string
    dataset_type: DatasetType
  }
}

// 审核状态枚举
export enum ReviewStatus {
  APPROVED = 1, // 通过
  REJECTED = 2, // 拒绝
}

// 审核请求
export interface ReviewRequest {
  review_status: ReviewStatus
  review_comment?: string
  review_reason?: string // 拒绝时必填
}

// 审核统计信息
export interface ReviewStats {
  pending_count: number
  approved_count: number
  rejected_count: number
  my_reviews_count: number
  recent_reviews: Array<{
    id: number
    dataset_title: string
    review_status: ReviewStatus
    reviewed_at: string
  }>
}

// 权限相关类型
export interface Permission {
  id: number
  name: string
  resource: string
  action: string
  description?: string
  created_at: string
  updated_at: string
}

export interface UserPermissions {
  permissions: string[] // 权限标识符数组，如 ["user:read", "dataset:write"]
}

export interface PermissionCheck {
  resource: string
  action: string
}

// 权限常量定义
export const PERMISSIONS = {
  // 用户权限
  USER_READ: 'user:read',
  USER_WRITE: 'user:write',
  USER_DELETE: 'user:delete',
  USER_CREATE: 'user:create',

  // 角色权限
  ROLE_READ: 'role:read',
  ROLE_WRITE: 'role:write',
  ROLE_DELETE: 'role:delete',
  ROLE_CREATE: 'role:create',

  // 权限管理
  PERMISSION_READ: 'permission:read',
  PERMISSION_WRITE: 'permission:write',
  PERMISSION_DELETE: 'permission:delete',
  PERMISSION_CREATE: 'permission:create',

  // 评估权限
  EVALUATION_READ: 'evaluation:read',
  EVALUATION_WRITE: 'evaluation:write',
  EVALUATION_DELETE: 'evaluation:delete',
  EVALUATION_CREATE: 'evaluation:create',
  EVALUATION_PARTICIPATE: 'evaluation:participate',

  // 知识库权限
  KNOWLEDGE_READ: 'knowledge:read',
  KNOWLEDGE_WRITE: 'knowledge:write',
  KNOWLEDGE_DELETE: 'knowledge:delete',
  KNOWLEDGE_CREATE: 'knowledge:create',

  // 数据集权限
  DATASET_READ: 'dataset:read',
  DATASET_WRITE: 'dataset:write',
  DATASET_DELETE: 'dataset:delete',
  DATASET_REVIEW: 'dataset:review',
  DATASET_STATS: 'dataset:stats',

  // 数据字典权限
  DATA_DICT_READ: 'data_dict:read',
  DATA_DICT_WRITE: 'data_dict:write',
  DATA_DICT_DELETE: 'data_dict:delete',

  // 系统管理
  SYSTEM_ADMIN: 'system:admin',
} as const

export type PermissionKey = (typeof PERMISSIONS)[keyof typeof PERMISSIONS]

// 路由相关类型
export interface RouteMenuItem {
  path: string
  name: string
  icon?: string
  children?: RouteMenuItem[]
  meta?: {
    title: string
    requiresAuth?: boolean
    roles?: string[]
    permissions?: PermissionKey[] // 添加权限要求
  }
}
