# API 快速参考卡片

## 基础信息
- **Base URL**: `http://localhost:8000/api/v1`
- **认证**: `Authorization: Bearer {token}`
- **Content-Type**: `application/json`

## 🔐 认证接口

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | `/auth/login/password` | 密码登录 | ❌ |
| POST | `/auth/login/sms` | 短信登录（支持自动注册） | ❌ |
| POST | `/auth/sms/send-code` | 发送短信验证码 | ❌ |
| POST | `/auth/login` | 兼容性登录接口 | ❌ |

## 👤 用户管理

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/users/me` | 获取当前用户信息 | ✅ |
| POST | `/users` | 创建用户 | ❌ |
| GET | `/users` | 获取用户列表（超级用户） | ✅ |
| GET | `/users/{id}` | 获取指定用户信息 | ✅ |
| PUT | `/users/{id}` | 更新用户信息 | ✅ |

## 🎭 角色权限

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/roles` | 获取角色列表 | ✅ |
| POST | `/roles` | 创建角色 | ✅ |
| GET | `/roles/{id}` | 获取角色详情 | ✅ |
| PUT | `/roles/{id}` | 更新角色 | ✅ |
| DELETE | `/roles/{id}` | 删除角色 | ✅ |
| POST | `/roles/assign-to-user` | 为用户分配角色 | ✅ |
| GET | `/permissions` | 获取权限列表 | ✅ |
| GET | `/permissions/me` | 获取我的权限 | ✅ |
| POST | `/permissions/check` | 检查用户权限 | ✅ |

## 📚 数据字典

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/data-dict/categories` | 获取字典分类 | ✅ |
| GET | `/data-dict/{category}` | 获取字典列表 | ✅ |
| GET | `/data-dict/{category}/options` | 获取字典选项 | ✅ |
| GET | `/data-dict/{category}/tree` | 获取字典树形结构 | ✅ |
| POST | `/data-dict` | 创建字典项 | ✅ |
| PUT | `/data-dict/{id}` | 更新字典项 | ✅ |
| DELETE | `/data-dict/{id}` | 删除字典项 | ✅ |

## 📊 数据集管理

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/datasets` | 获取数据集列表 | ✅ |
| GET | `/datasets/my` | 获取我的数据集 | ✅ |
| GET | `/datasets/{id}` | 获取数据集详情 | ✅ |
| POST | `/datasets` | 创建数据集 | ✅ |
| PUT | `/datasets/{id}` | 更新数据集 | ✅ |
| DELETE | `/datasets/{id}` | 删除数据集 | ✅ |
| POST | `/datasets/{id}/submit-review` | 提交审核 | ✅ |
| GET | `/datasets/stats` | 获取数据集统计 | ✅ |

## 🔍 数据集审核

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | `/dataset-reviews/pending` | 获取待审核数据集 | ✅ |
| GET | `/dataset-reviews/history` | 获取审核历史 | ✅ |
| GET | `/dataset-reviews/my` | 获取我的审核记录 | ✅ |
| GET | `/dataset-reviews/{id}` | 获取审核详情 | ✅ |
| POST | `/dataset-reviews/{dataset_id}/review` | 审核数据集 | ✅ |
| GET | `/dataset-reviews/check/{dataset_id}` | 检查审核权限 | ✅ |
| GET | `/dataset-reviews/stats` | 获取审核统计 | ✅ |

## 📝 常用查询参数

### 分页参数
- `page`: 页码（默认1）
- `size`: 每页数量（默认20，最大100）

### 数据集查询参数
- `dataset_type`: 数据集类型（1-综合性病例 2-影像数据集 3-考试题目）
- `dataset_status`: 状态（1-草稿 2-待审核 3-已通过 4-已拒绝 5-已归档）
- `creator_id`: 创建者ID
- `search`: 搜索关键词
- `tags`: 标签筛选

### 审核查询参数
- `reviewer_id`: 审核人ID
- `review_status`: 审核状态（1-通过 2-拒绝）
- `start_date`: 开始日期
- `end_date`: 结束日期

## 🏷️ 枚举值参考

### 用户来源 (user_source)
```
1: 企业    2: 个人    3: 学校    4: 医院    5: 政府
```

### 数据集类型 (dataset_type)
```
1: 综合性病例数据集    2: 影像数据集    3: 考试数据集
```

### 数据集状态 (dataset_status)
```
1: 草稿    2: 待审核    3: 已通过    4: 已拒绝    5: 已归档
```

### 审核状态 (review_status)
```
1: 通过    2: 拒绝
```

## 🚀 快速开始示例

### 1. 登录获取Token
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login/password" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### 2. 短信登录（自动注册）
```bash
# 发送验证码
curl -X POST "http://localhost:8000/api/v1/auth/sms/send-code" \
  -H "Content-Type: application/json" \
  -d '{"phone": "13800138000", "purpose": "login"}'

# 登录
curl -X POST "http://localhost:8000/api/v1/auth/login/sms" \
  -H "Content-Type: application/json" \
  -d '{"phone": "13800138000", "code": "123456", "full_name": "张三"}'
```

### 3. 获取用户信息
```bash
curl -X GET "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 获取数据集列表
```bash
curl -X GET "http://localhost:8000/api/v1/datasets?page=1&size=20&dataset_type=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 创建数据集
```bash
curl -X POST "http://localhost:8000/api/v1/datasets" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试数据集",
    "description": "测试用数据集",
    "dataset_type": 1,
    "tags": ["测试"],
    "data": {
      "animal_type_id": 1,
      "age_months": 12,
      "symptoms": "发热、咳嗽",
      "diagnosis": "感冒",
      "treatment": "对症治疗"
    }
  }'
```

## ⚠️ 注意事项

1. **Token过期**: JWT Token有效期为30分钟，过期后需要重新登录
2. **权限控制**: 部分接口需要特定权限，确保用户有相应权限
3. **数据验证**: 请求数据需要符合schema定义，注意必填字段
4. **错误处理**: 注意处理各种HTTP状态码和错误响应
5. **分页限制**: 列表接口最大每页100条记录
6. **手机号格式**: 短信相关接口要求中国大陆手机号格式（1[3-9]xxxxxxxxx）

## 📞 技术支持

- 查看完整API文档: `API_DOCUMENTATION.md`
- 前端开发指南: `FRONTEND_DEVELOPMENT_GUIDE.md`
- Postman集合: `VetEval_API_Postman_Collection.json`
- 短信登录说明: `SMS_LOGIN_README.md`
