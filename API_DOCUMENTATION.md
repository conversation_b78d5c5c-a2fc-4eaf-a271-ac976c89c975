# 兽医评估平台 API 接口文档

## 基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **API版本**: v1.0

## 通用响应格式

所有API接口都使用统一的响应格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-08-14T10:30:00Z"
}
```

## 认证相关接口

### 1. 密码登录

**接口地址**: `POST /auth/login/password`

**请求参数**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer"
  },
  "message": "登录成功"
}
```

### 2. 发送短信验证码

**接口地址**: `POST /auth/sms/send-code`

**请求参数**:
```json
{
  "phone": "13800138000",
  "purpose": "login"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "验证码发送成功",
    "code": "123456"
  },
  "message": "验证码发送成功"
}
```

### 3. 短信验证码登录（支持自动注册）

**接口地址**: `POST /auth/login/sms`

**请求参数**:
```json
{
  "phone": "13800138000",
  "code": "123456",
  "full_name": "张三"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "is_new_user": true,
    "user_info": {
      "id": 2,
      "username": "user_38000",
      "phone": "13800138000",
      "full_name": "张三",
      "email": "<EMAIL>"
    }
  },
  "message": "注册并登录成功"
}
```

### 4. 兼容性登录接口

**接口地址**: `POST /auth/login`

与密码登录接口相同，保持向后兼容。

## 用户管理接口

### 1. 获取当前用户信息

**接口地址**: `GET /users/me`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "full_name": "管理员",
    "is_active": true,
    "user_source": 2,
    "created_at": "2025-08-14T10:00:00Z",
    "updated_at": "2025-08-14T10:00:00Z"
  },
  "message": "获取用户信息成功"
}
```

### 2. 创建用户

**接口地址**: `POST /users`

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "phone": "13900139000",
  "password": "password123",
  "full_name": "新用户",
  "user_source": 2
}
```

### 3. 获取用户列表（仅超级用户）

**接口地址**: `GET /users?skip=0&limit=100`

### 4. 获取指定用户信息

**接口地址**: `GET /users/{user_id}`

### 5. 更新用户信息

**接口地址**: `PUT /users/{user_id}`

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "full_name": "更新后的姓名",
  "phone": "13900139001"
}
```

## 角色管理接口

### 1. 创建角色

**接口地址**: `POST /roles`

**请求参数**:
```json
{
  "name": "数据管理员",
  "description": "负责数据集管理",
  "is_active": true
}
```

### 2. 获取角色列表

**接口地址**: `GET /roles?skip=0&limit=100&is_active=true`

### 3. 获取角色详情

**接口地址**: `GET /roles/{role_id}`

### 4. 更新角色

**接口地址**: `PUT /roles/{role_id}`

### 5. 删除角色

**接口地址**: `DELETE /roles/{role_id}`

### 6. 为用户分配角色

**接口地址**: `POST /roles/assign-to-user`

**请求参数**:
```json
{
  "user_id": 1,
  "role_ids": [1, 2, 3]
}
```

### 7. 获取用户角色

**接口地址**: `GET /roles/user/{user_id}`

## 权限管理接口

### 1. 获取权限列表

**接口地址**: `GET /permissions?skip=0&limit=100&resource=dataset&action=read`

### 2. 创建权限

**接口地址**: `POST /permissions`

**请求参数**:
```json
{
  "name": "数据集读取",
  "resource": "dataset",
  "action": "read",
  "description": "读取数据集权限"
}
```

### 3. 获取我的权限

**接口地址**: `GET /permissions/me`

### 4. 为角色分配权限

**接口地址**: `POST /permissions/assign-to-role`

**请求参数**:
```json
{
  "role_id": 1,
  "permission_ids": [1, 2, 3]
}
```

### 5. 检查用户权限

**接口地址**: `POST /permissions/check`

**请求参数**:
```json
{
  "user_id": 1,
  "resource": "dataset",
  "action": "read"
}
```

### 6. 获取资源列表

**接口地址**: `GET /permissions/resources`

### 7. 获取资源操作列表

**接口地址**: `GET /permissions/resources/{resource}/actions`

## 数据字典接口

### 1. 获取字典分类

**接口地址**: `GET /data-dict/categories`

**响应示例**:
```json
{
  "success": true,
  "data": ["animal_type", "disease_type", "image_type", "difficulty_level"],
  "message": "获取字典分类成功"
}
```

### 2. 获取字典列表

**接口地址**: `GET /data-dict/{category}?parent_id=0&is_active=true`

### 3. 获取字典选项（下拉选择用）

**接口地址**: `GET /data-dict/{category}/options?parent_id=0`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "犬",
      "value": "dog"
    },
    {
      "id": 2,
      "name": "猫",
      "value": "cat"
    }
  ],
  "message": "获取字典选项成功"
}
```

### 4. 获取字典树形结构

**接口地址**: `GET /data-dict/{category}/tree`

### 5. 创建字典项

**接口地址**: `POST /data-dict`

**请求参数**:
```json
{
  "category": "animal_type",
  "name": "兔子",
  "value": "rabbit",
  "parent_id": 0,
  "sort_order": 3,
  "description": "兔子类型"
}
```

### 6. 更新字典项

**接口地址**: `PUT /data-dict/{dict_id}`

### 7. 删除字典项

**接口地址**: `DELETE /data-dict/{dict_id}`

## 数据集管理接口

### 1. 获取数据集列表

**接口地址**: `GET /datasets`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20，最大100）
- `dataset_type`: 数据集类型（1-综合性病例 2-影像数据集 3-考试题目）
- `dataset_status`: 状态（1-草稿 2-待审核 3-已通过 4-已拒绝 5-已归档）
- `creator_id`: 创建者ID
- `search`: 搜索关键词
- `tags`: 标签筛选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "犬瘟热病例数据集",
        "description": "包含多个犬瘟热病例的综合数据",
        "dataset_type": 1,
        "dataset_status": 3,
        "creator_id": 1,
        "tags": ["犬瘟热", "传染病"],
        "created_at": "2025-08-14T10:00:00Z",
        "updated_at": "2025-08-14T10:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 20,
    "pages": 3
  },
  "message": "获取数据集列表成功"
}
```

### 2. 获取我的数据集

**接口地址**: `GET /datasets/my`

查询参数与获取数据集列表相同。

### 3. 获取数据集详情

**接口地址**: `GET /datasets/{dataset_id}?include_details=false`

**查询参数**:
- `include_details`: 是否包含详细数据（默认false）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "犬瘟热病例数据集",
    "description": "包含多个犬瘟热病例的综合数据",
    "dataset_type": 1,
    "dataset_status": 3,
    "creator_id": 1,
    "tags": ["犬瘟热", "传染病"],
    "extra_metadata": {},
    "data": {
      "animal_type_id": 1,
      "age_months": 12,
      "symptoms": "发热、咳嗽、流鼻涕",
      "diagnosis": "犬瘟热",
      "treatment": "支持疗法，抗病毒治疗"
    },
    "created_at": "2025-08-14T10:00:00Z",
    "updated_at": "2025-08-14T10:00:00Z"
  },
  "message": "获取数据集详情成功"
}
```

### 4. 创建数据集

**接口地址**: `POST /datasets`

**请求参数（综合性病例）**:
```json
{
  "title": "新的病例数据集",
  "description": "病例描述",
  "dataset_type": 1,
  "tags": ["标签1", "标签2"],
  "data": {
    "animal_type_id": 1,
    "age_months": 24,
    "weight_kg": 15.5,
    "symptoms": "症状描述",
    "diagnosis": "诊断结果",
    "treatment": "治疗方案",
    "outcome": "治疗结果"
  }
}
```

**请求参数（影像数据集）**:
```json
{
  "title": "X光片数据集",
  "description": "胸部X光片集合",
  "dataset_type": 2,
  "tags": ["X光", "胸部"],
  "data": {
    "animal_type_id": 1,
    "image_type_id": 1,
    "image_detail_id": 2,
    "body_part": "胸部",
    "image_description": "正位胸部X光片",
    "findings": "肺部阴影",
    "image_url": "https://example.com/image.jpg"
  }
}
```

**请求参数（考试题目）**:
```json
{
  "title": "兽医基础知识题目",
  "description": "基础理论考试题目",
  "dataset_type": 3,
  "tags": ["基础知识", "理论"],
  "data": {
    "question_type_id": 1,
    "subject_id": 1,
    "difficulty_level_id": 2,
    "question_text": "犬瘟热的主要症状是什么？",
    "options": {
      "A": "发热",
      "B": "咳嗽",
      "C": "流鼻涕",
      "D": "以上都是"
    },
    "correct_answer": {
      "answer": "D",
      "type": "single"
    },
    "explanation": "犬瘟热的主要症状包括发热、咳嗽、流鼻涕等"
  }
}
```

### 5. 更新数据集

**接口地址**: `PUT /datasets/{dataset_id}`

请求参数格式与创建数据集相同。

### 6. 删除数据集

**接口地址**: `DELETE /datasets/{dataset_id}`

### 7. 提交审核

**接口地址**: `POST /datasets/{dataset_id}/submit-review`

**请求参数**:
```json
{
  "submit_comment": "请审核此数据集"
}
```

### 8. 获取数据集统计

**接口地址**: `GET /datasets/stats`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_datasets": 100,
    "by_type": {
      "1": 40,
      "2": 35,
      "3": 25
    },
    "by_status": {
      "1": 10,
      "2": 15,
      "3": 60,
      "4": 10,
      "5": 5
    },
    "my_datasets": 20
  },
  "message": "获取统计信息成功"
}
```

## 数据集审核接口

### 1. 获取待审核数据集列表

**接口地址**: `GET /dataset-reviews/pending`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）
- `dataset_type`: 数据集类型
- `creator_id`: 创建者ID
- `search`: 搜索关键词

**响应示例**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "待审核的病例数据集",
        "description": "需要审核的病例数据",
        "dataset_type": 1,
        "dataset_status": 2,
        "creator_id": 2,
        "created_at": "2025-08-14T10:00:00Z",
        "submit_comment": "请审核此数据集"
      }
    ],
    "total": 10,
    "page": 1,
    "size": 20,
    "pages": 1
  },
  "message": "获取待审核列表成功"
}
```

### 2. 获取审核历史

**接口地址**: `GET /dataset-reviews/history`

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `dataset_id`: 数据集ID
- `reviewer_id`: 审核人ID
- `review_status`: 审核状态（1-通过 2-拒绝）
- `dataset_type`: 数据集类型
- `start_date`: 开始日期
- `end_date`: 结束日期

### 3. 获取我的审核记录

**接口地址**: `GET /dataset-reviews/my`

查询参数与审核历史相同。

### 4. 获取审核详情

**接口地址**: `GET /dataset-reviews/{review_id}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "dataset_id": 1,
    "reviewer_id": 3,
    "review_status": 1,
    "review_comment": "数据质量良好，通过审核",
    "review_reason": null,
    "reviewed_at": "2025-08-14T11:00:00Z",
    "dataset": {
      "id": 1,
      "title": "犬瘟热病例数据集",
      "dataset_type": 1
    }
  },
  "message": "获取审核详情成功"
}
```

### 5. 审核数据集

**接口地址**: `POST /dataset-reviews/{dataset_id}/review`

**请求参数**:
```json
{
  "review_status": 1,
  "review_comment": "数据质量良好，通过审核",
  "review_reason": null
}
```

**参数说明**:
- `review_status`: 审核状态（1-通过 2-拒绝）
- `review_comment`: 审核意见
- `review_reason`: 拒绝原因（拒绝时必填）

### 6. 检查审核权限

**接口地址**: `GET /dataset-reviews/check/{dataset_id}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "can_review": true
  },
  "message": "检查审核权限成功"
}
```

### 7. 获取审核统计

**接口地址**: `GET /dataset-reviews/stats`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "pending_count": 15,
    "approved_count": 45,
    "rejected_count": 8,
    "my_reviews_count": 12,
    "recent_reviews": [
      {
        "id": 1,
        "dataset_title": "病例数据集",
        "review_status": 1,
        "reviewed_at": "2025-08-14T11:00:00Z"
      }
    ]
  },
  "message": "获取审核统计成功"
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 数据字典枚举值

### 用户来源 (user_source)
- 1: 企业
- 2: 个人
- 3: 学校
- 4: 医院
- 5: 政府

### 数据集类型 (dataset_type)
- 1: 综合性病例数据集
- 2: 影像数据集
- 3: 考试数据集

### 数据集状态 (dataset_status)
- 1: 草稿
- 2: 待审核
- 3: 已通过
- 4: 已拒绝
- 5: 已归档

### 审核状态 (review_status)
- 1: 通过
- 2: 拒绝

## 认证说明

除了登录相关接口外，所有接口都需要在请求头中携带JWT Token：

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Token有效期为30分钟，过期后需要重新登录获取新的Token。

## 分页说明

所有列表接口都支持分页，使用以下参数：
- `page`: 页码，从1开始
- `size`: 每页数量，默认20，最大100

响应中包含分页信息：
- `total`: 总记录数
- `page`: 当前页码
- `size`: 每页数量
- `pages`: 总页数
```
