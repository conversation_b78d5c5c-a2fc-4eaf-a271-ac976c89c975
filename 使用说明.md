# 大模型评测平台 - 使用说明

## 🚀 快速开始

### 1. 启动项目

```bash
npm run dev
```

### 2. 访问地址

打开浏览器访问：http://localhost:3000

### 3. 登录信息

- **用户名**: `admin`
- **密码**: `123456`

## 📋 功能说明

### 主要功能模块

1. **用户认证**
   - 登录/注册页面
   - 用户信息管理
   - 权限控制

2. **仪表板**
   - 系统概览
   - 关键指标展示
   - 最近任务列表
   - 性能趋势图表

3. **模型管理**
   - 模型列表查看
   - 添加新模型
   - 模型信息编辑
   - 模型状态管理

4. **评测任务**
   - 创建评测任务
   - 任务状态监控
   - 任务进度跟踪
   - 结果查看

5. **统计分析**
   - 性能对比图表
   - 趋势分析
   - 数据可视化

6. **系统设置**
   - 个人信息管理
   - 密码修改
   - 系统配置

## 🎨 界面特点

- **PC端优化**: 专为桌面端设计，最小宽度1200px
- **响应式布局**: 适配不同屏幕尺寸
- **现代化UI**: 基于Element Plus组件库
- **中文界面**: 完全中文化的用户界面
- **图表展示**: 集成ECharts数据可视化

## 🛠 技术架构

- **前端框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **构建工具**: Vite
- **图表库**: ECharts + Vue-ECharts
- **HTTP客户端**: Axios

## 📁 项目结构

```
src/
├── api/                 # API接口定义
├── assets/             # 静态资源
├── components/         # 公共组件
│   └── charts/        # 图表组件
├── layouts/           # 布局组件
├── router/            # 路由配置
├── stores/            # Pinia状态管理
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
└── views/             # 页面组件
```

## 🔧 开发说明

### 环境要求

- Node.js >= 20.19.0
- npm >= 10.0.0

### 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 代码格式化
npm run format

# 代码检查
npm run lint

# 类型检查
npm run type-check

# 构建生产版本
npm run build
```

### 模拟数据说明

当前版本使用模拟数据进行演示：

- 登录功能使用本地验证
- 仪表板数据为静态模拟数据
- 图表数据为示例数据

### 后端集成

要连接真实后端API，请：

1. 修改 `.env` 文件中的 `VITE_API_BASE_URL`
2. 更新 `src/stores/counter.ts` 中的登录逻辑
3. 移除模拟数据，使用真实API调用

## 📝 注意事项

1. **浏览器兼容性**: 建议使用Chrome、Firefox、Safari、Edge等现代浏览器
2. **屏幕分辨率**: 最佳体验需要屏幕宽度不小于1200px
3. **开发环境**: 确保Node.js版本符合要求
4. **网络环境**: 首次运行需要下载依赖包

## 🆘 常见问题

**Q: 登录后页面空白？**
A: 检查浏览器控制台是否有错误信息，确保所有依赖正确安装。

**Q: 图表不显示？**
A: 确保ECharts相关依赖已正确安装，检查网络连接。

**Q: 样式显示异常？**
A: 清除浏览器缓存，确保Element Plus样式正确加载。

**Q: 页面没有填充整个浏览器宽度？**
A: 项目已经包含了专门的布局CSS文件(`src/assets/layout.css`)来确保PC端全宽显示。如果仍有问题，请刷新浏览器或清除缓存。

**Q: 开发服务器启动失败？**
A: 检查端口3000是否被占用，或修改vite.config.ts中的端口配置。

**Q: 在小屏幕上显示异常？**
A: 本项目专为PC端设计，建议使用宽度不小于1200px的屏幕。在小屏幕上会显示水平滚动条。
